import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o usuário pode cadastrar um novo número de WhatsApp.
 */
@Injectable()
export class CanAddNewWhatsAppNumberGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user; // NOTE: Pequeno Gafanhoto isso aqui é para pegar o token no header.

    // NOTE: Gafanhotenho essa parada aqui é um BYPASS para master ;)
    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    if (!user.activeAccount.accountSecureId) {
      throw new BadRequestException(
        'Nenhuma Conta encontrada para esse usuário. Entre em contato com o suporte',
      );
    }

    const accountData = await this.prismaService.accounts.findFirst({
      where: {
        secureId: { equals: user.activeAccount.accountSecureId },
      },

      select: {
        companyName: true,
        subscriptions: {
          where: {
            isActive: true,
            endsAt: { gte: new Date() },
          },

          select: {
            isActive: true,

            plan: {
              select: {
                whatsappNumberLimit: true,
              },
            },
          },
        },

        whatsAppIntegration: {
          where: {
            isDeleted: false,
            isActive: true,
          },

          select: {
            secureId: true,
          },
        },
      },
    });

    if (accountData.companyName === 'PlyrChat') {
      return true; // Bypass para PlyrChat, não precisa de assinatura
    }

    const subscriptions = accountData.subscriptions;
    if (!subscriptions || subscriptions.length < 1) {
      throw new BadRequestException('Nenhuma inscrição ativa foi encontrada!');
    }

    // NOTE: Gafanhoto, aqui eu to pegando a primeira subscription ativa, porque de acordo com nossa regra de negócio, pelo menos nesse momento, o usuário só pode ter uma subscription ativa.
    const activeSubscription = subscriptions.find(
      (subscription) => subscription.isActive,
    );

    const whatsappNumberLimit = activeSubscription.plan.whatsappNumberLimit;
    const whatsAppIntegrations = accountData.whatsAppIntegration.length;

    if (whatsAppIntegrations >= whatsappNumberLimit) {
      throw new BadRequestException(
        'Você atingiu o limite de números de WhatsApp cadastrados.',
      );
    }

    return true;
  }
}
