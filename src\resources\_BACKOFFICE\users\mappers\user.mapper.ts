import { CPFMask } from 'src/@shared/helpers/mask/cpf.mask';
import { GetUserOutputDto } from '../dto/get-user-output.dto';
import { UserWithAccountAndAccountPermissionsModel } from '../users.contract';

export class BackofficeUserGetUserMapper {
  static fromUserWithAccountAndAccountPermissionsModelToOutputDto(
    model: UserWithAccountAndAccountPermissionsModel,
  ): GetUserOutputDto {
    const backofficeAccount = model.usersAccounts.find(
      (userAccount) => userAccount.role.slug === "BACKOFFICE",
    );

    const hasAllPermissions = model.usersAccounts[0].accountsPermissions.some(
      (accPer) =>
        [
          'backoffice_view',
          'backoffice_create',
          'backoffice_edit',
          'backoffice_delete',
        ].includes(accPer.permission.slug),
    );

    const permissions = model.usersAccounts[0].accountsPermissions.map(
      (per) => per.permission.secureId,
    );
    return {
      secureId: model.secureId,
      email: model.email,
      name: model.name,
      cpf: CPFMask.apply(model.cpf),
      hasAllPermissions: hasAllPermissions,
      permissions: !hasAllPermissions ? permissions : undefined,
      isActive: backofficeAccount.isActive, // ASSOCIADO AO USER_ACCOUNT
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
