import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IIsAIResponderDecorator } from 'src/@shared/contracts/decorators/is-ai-responder/interface';

interface isAIResponderProps extends Headers {
  isAIResponder?: string;
}

export const IsAIResponderDecorator = createParamDecorator(
  (data: any, ctx: ExecutionContext): IIsAIResponderDecorator => {
    const request = ctx.switchToHttp().getRequest();
    const query: isAIResponderProps = request.query;

    let isAIResponder: boolean | undefined;

    if (!query?.['isAIResponder']) {
      isAIResponder = undefined;
    } else {
      isAIResponder = query['isAIResponder'] === 'true' ? undefined : false;
    }

    return {
      isAIResponder,
    };
  },
);
