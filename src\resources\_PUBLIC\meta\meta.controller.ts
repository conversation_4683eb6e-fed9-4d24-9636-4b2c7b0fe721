import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { MetaService } from './meta.service';
import { CreateMetaDto } from './dto/create-meta.dto';
import { UpdateMetaDto } from './dto/update-meta.dto';
import { WppIntegrationInfosExtractor } from 'src/@shared/decorators/meta/wpp-integration-infos-extractor.decorator';
import { RabbitMQService } from 'src/third-party/rabbitmq/rabbitmq.service';

@Controller('meta/webhook')
export class MetaController {
  constructor(
    private readonly metaService: MetaService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  @Post()
  async create(
    @Body() createMetaDto: CreateMetaDto,
    @WppIntegrationInfosExtractor()
    data: { accountId: number; wppIntegrationId: number },
  ) {
    if (!createMetaDto?.entry?.[0]?.changes?.[0]?.value?.messages) {
      return;
    }
    await this.rabbitMQService.publish('meta-incoming-messages', {
      createMetaDto,
      accountId: data.accountId,
      wppIntegrationId: data.wppIntegrationId,
    });
    return { status: 'queued' };
  }

  @Get()
  async webhookSubscription(
    @Query('hub.mode') mode: string,
    @Query('hub.verify_token') token: string,
    @Query('hub.challenge') challenge: string,
    @WppIntegrationInfosExtractor()
    data: { accountId: number; wppIntegrationId: number },
  ) {
    if (mode !== 'subscribe') {
      throw new NotFoundException('Modo não encontrado');
    }

    const validateToken = await this.metaService.validateWebhookToken(
      data.wppIntegrationId,
      token,
      data.accountId,
    );

    if (!validateToken) {
      return;
    }

    return challenge;
  }
}
