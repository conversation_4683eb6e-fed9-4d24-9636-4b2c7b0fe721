type AppDashboardVOConstructorProps = {
  chatsQuantity: number;
  leadsQuantity: number;
  aiMessagesQuantity: number;
  aiSessionsQuantity: number;
  attendantSessionsQuantity: number;
  waitingSessionsQuantity: number;
  ongoingConversationsQuantity?: number;
  finalizedConversationsQuantity?: number;
  averageFirstResponseTime?: number;
};

export class AppDashboardVO {
  chatsQuantity: number;
  leadsQuantity: number;
  aiMessagesQuantity: number;
  aiSessionsQuantity: number;
  attendantSessionsQuantity: number;
  waitingSessionsQuantity: number;
  ongoingConversationsQuantity: number;
  finalizedConversationsQuantity: number;
  averageFirstResponseTime: number;

  constructor(props: AppDashboardVOConstructorProps) {
    this.chatsQuantity = props.chatsQuantity;
    this.leadsQuantity = props.leadsQuantity;
    this.aiMessagesQuantity = props.aiMessagesQuantity;
    this.aiSessionsQuantity = props.aiSessionsQuantity;
    this.attendantSessionsQuantity = props.attendantSessionsQuantity;
    this.waitingSessionsQuantity = props.waitingSessionsQuantity;
    this.ongoingConversationsQuantity = props.ongoingConversationsQuantity || 0;
    this.finalizedConversationsQuantity = props.finalizedConversationsQuantity || 0;
    this.averageFirstResponseTime = props.averageFirstResponseTime || 0;
  }
}
