import { ChatSessions as SessionModel } from '@prisma/client';
import { Session } from '../entities/session.entity';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';

export class SessionFactory {
  static convertBatchFromModelToSession(models: SessionModel[]): Session[] {
    return models.map((model) => {
      const sessionOutput = new Session({
        ...model,
      });

      return sessionOutput;
    });
  }

  static convertFromModelToSession(model: SessionModel): Session {
    const sessionOutput = new Session({
      ...model,
    });

    return sessionOutput;
  }
}
