import {
  Is<PERSON>otE<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateChatbotDto {
  @MaxLength(255, {
    message: 'O nome do chatbot deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O nome do chatbot deve ter no mínimo 3 caracteres',
  })
  @IsNotEmpty({ message: 'O nome do chatbot é obrigatório' })
  name: string;

  @IsNotEmpty({ message: 'O chatbot é uma inteligencia artificial?' })
  isAI: boolean;

  @IsNotEmpty({ message: 'A Captura de lead no chatbot deve ser informada!' })
  isLeadCaptureActive: boolean;
}
