import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { Account } from './entities/account.entity';

import { UpdateAccountDto } from './dto/update-account.dto';
import { CreateAccountInputDto } from './dto/create-account.dto';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';

import { AccountMapper } from './mapper/account.mapper';
import { AccountFactory } from './factory/account.factory';

import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';

import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';

@Injectable()
export class AccountsService {
  constructor(private prisma: PrismaService) {}

  async create(createAccountInputDto: CreateAccountInputDto) {
    const accountAlreadyExists = await this.findExistsByCompanyName(
      createAccountInputDto.companyName,
    );

    if (accountAlreadyExists) {
      throw new ConflictException('Essa empresa já possui conta');
    }

    const account = new Account(createAccountInputDto);
    const accountModel = AccountMapper.toModel(account);

    await this.prisma.accounts.create({
      data: accountModel,
    });

    return;
  }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
  ): Promise<IWithPagination<Account>> {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    // DRY
    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      companyName: {
        contains: paginationQuery?.search && paginationQuery.search,
      },
    };

    const accountsModel = await this.prisma.accounts.findMany({
      take: paginationQuery.limit,
      skip: skip,
      orderBy: {
        companyName: 'asc',
      },
      where: whereClause,
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prisma.accounts,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    return {
      meta: meta,
      data: !accountsModel
        ? ([] as Account[])
        : AccountFactory.createBatchAccountFromModels(accountsModel),
    };
  }

  async findOne(secureId: string) {
    const account = await this.prisma.accounts.findFirst({
      where: { secureId: { equals: secureId } },
    });

    if (!account) {
      throw new NotFoundException('Account não encontrada');
    }

    return AccountMapper.toEntity(account);
  }

  async update(secureId: string, updateAccountDto: UpdateAccountDto) {
    const account = await this.prisma.accounts.findFirst({
      where: { secureId: { equals: secureId } },
    });
    if (!account) {
      throw new NotFoundException('Account não encontrada');
    }

    if (
      updateAccountDto.companyName &&
      account.companyName !== updateAccountDto.companyName
    ) {
      const companyNameAlreadyExists = await this.findExistsByCompanyName(
        updateAccountDto.companyName,
      );

      if (companyNameAlreadyExists) {
        throw new ConflictException(
          'Esse nome de empresa já existe. Utilize outro!',
        );
      }
    }

    await this.prisma.accounts.update({
      where: { secureId: secureId },
      data: updateAccountDto,
    });

    return;
  }

  async remove(secureId: string) {
    const account = await this.prisma.accounts.findFirst({
      where: { secureId: { equals: secureId } },
    });

    if (!account) {
      throw new NotFoundException('Account não encontrada');
    }

    const accountEntity = AccountMapper.toEntity(account);
    accountEntity.delete();

    const deletedAccountModel = AccountMapper.toModel(accountEntity);

    await this.prisma.accounts.update({
      where: {
        secureId: secureId,
      },
      data: deletedAccountModel,
    });

    return;
  }

  async findExistsBySecureId(secureId: string) {
    if (!secureId) {
      return false;
    }

    const queryResult = await this.prisma.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM accounts WHERE secure_id = ?) AS has;`,
      secureId,
    );

    return Boolean(Number(queryResult[0]?.has || 0));
  }

  async findExistsByCompanyName(companyName: string) {
    if (!companyName) {
      return false;
    }

    const queryResult = await this.prisma.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM accounts WHERE company_name = ?) AS has;`,
      companyName,
    );

    return Boolean(Number(queryResult[0]?.has || 0));
  }
}
