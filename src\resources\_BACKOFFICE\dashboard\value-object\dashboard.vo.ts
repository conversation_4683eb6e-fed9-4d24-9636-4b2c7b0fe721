type BackofficeDashboardVOConstructorProps = {
  accountsQuantity: number;
  chatsQuantity: number;
  attendantsQuantity: number;
  transactionAmount: {
    totalWaiting: number;
    totalPaid: number;
    totalUnpaid: number;
    totalRefunded: number;
  };

  subscriptions: {
    trials: number;
    paid: number;
    free: number;
    canceled: number;
  };
};

export class BackofficeDashboardVO {
  accountsQuantity: number;
  chatsQuantity: number;
  attendantsQuantity: number;

  transactionAmount: {
    totalWaiting: number;
    totalPaid: number;
    totalUnpaid: number;
    totalRefunded: number;
  };

  subscriptions: {
    trials: number;
    paid: number;
    free: number;
    canceled: number;
  };

  constructor(props: BackofficeDashboardVOConstructorProps) {
    this.accountsQuantity = props.accountsQuantity;
    this.chatsQuantity = props.chatsQuantity;
    this.transactionAmount = {
      totalWaiting: props.transactionAmount.totalWaiting,
      totalPaid: props.transactionAmount.totalPaid,
      totalUnpaid: props.transactionAmount.totalUnpaid,
      totalRefunded: props.transactionAmount.totalRefunded,
    };
    this.attendantsQuantity = props.attendantsQuantity;
    this.subscriptions = props.subscriptions;
  }
}
