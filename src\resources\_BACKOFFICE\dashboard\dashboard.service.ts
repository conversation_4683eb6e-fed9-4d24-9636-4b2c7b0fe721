import { Injectable } from '@nestjs/common';

import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { BackofficeDashboardVO } from './value-object/dashboard.vo';

import { BackofficeDashboardMapper } from './mapper/dashboard.mapper';

@Injectable()
export class BackofficeDashboardService {
  constructor(private readonly prismaService: PrismaService) {}

  async findAll(startDate?: string, endDate?: string) {
    const dateFilter = startDate
      ? {
          createdAt: {
            gte: new Date(startDate),
            lte: endDate ? new Date(endDate) : new Date(),
          },
        }
      : undefined;

    const accountsQuantity = await this.prismaService.accounts.count({
      where: dateFilter,
    });

    const chatsQuantity = await this.prismaService.chats.count({
      where: dateFilter,
    });

    const attendantsQuantity = await this.prismaService.users.count({
      where: {
        usersAccounts: {
          some: { isOwner: false },
        },
        ...(dateFilter && { createdAt: dateFilter.createdAt }),
      },
    });

    const transactions = await this.prismaService.$queryRawUnsafe(`
      SELECT
        SUM(IF(status = 'waiting', amount, 0)) AS totalWaiting,
        SUM(IF(status = 'paid', amount, 0)) AS totalPaid,
        SUM(IF(status = 'unpaid', amount, 0)) AS totalUnpaid,
        SUM(IF(status = 'refunded', amount, 0)) AS totalRefunded
      FROM transactions
      WHERE
        (status != 'paid' AND ${
          startDate && endDate
            ? `created_at BETWEEN '${startDate}' AND '${endDate}'`
            : '1=1'
        })
        OR
        (status = 'paid' AND ${
          startDate && endDate
            ? `payed_at BETWEEN '${startDate}' AND '${endDate}'`
            : '1=1'
        })
      `);

    const subscriptions = await this.prismaService.$queryRawUnsafe(`
      SELECT
        COUNT(IF(type = 'trial' AND status != 'canceled', 1, null)) AS trials,
        COUNT(IF(type = 'paid' AND status != 'canceled', 1, null)) AS paid,
        COUNT(IF(type = 'free' AND status != 'canceled', 1, null)) AS free,
        COUNT(IF(status = 'canceled', 1, null)) AS canceled
      FROM subscriptions
      WHERE 
        (status != 'canceled' AND ${
          startDate && endDate
            ? `created_at BETWEEN '${startDate}' AND '${endDate}'`
            : '1=1'
        })
        OR 
        (status = 'canceled' AND ${
          startDate && endDate
            ? `canceled_at BETWEEN '${startDate}' AND '${endDate}'`
            : '1=1'
        })
    `);

    const vo = new BackofficeDashboardVO({
      accountsQuantity,
      chatsQuantity,
      attendantsQuantity,
      transactionAmount: transactions[0],
      subscriptions: {
        trials: subscriptions[0].trials,
        paid: subscriptions[0].paid,
        free: subscriptions[0].free,
        canceled: subscriptions[0].canceled,
      },
    });

    return BackofficeDashboardMapper.fromDashboardVOToDashboardOutputDto(vo);
  }
}
