import { Modu<PERSON> } from '@nestjs/common';
import { ChatsService } from './chats.service';
import { <PERSON>ts<PERSON>ontroller } from './chats.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { MessageChainModule } from 'src/third-party/langchain/message-chain/message-chain.module';
import { ExtractorInfosModule } from 'src/third-party/langchain/extractor-infos/extractor-infos.module';
import { AnalyzeSentimentModule } from 'src/third-party/langchain/analyze-sentiment/analyze-sentiment.module';
import { ChatsModule as ChatsWebSocketModule } from 'src/resources/chats/chats.module';
import { AIModule } from 'src/resources/ia/ai.module';
import { SubscriptionSessionModule } from 'src/@shared/services/subscription-session/subscription-session.module';

@Module({
  imports: [
    PrismaModule,
    MessageChainModule,
    ChatsWebSocketModule,
    ExtractorInfosModule,
    AnalyzeSentimentModule,
    AIModule,
    SubscriptionSessionModule,
  ],
  controllers: [ChatsController],
  providers: [ChatsService],
})
export class ChatsModule {}
