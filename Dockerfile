
# Base image
FROM node:20.15.0
ARG SERVER_PORT=${SERVER_PORT}
ARG SERVER_BASE_URL=${SERVER_BASE_URL}

ARG DATABASE_URL=${DATABASE_URL}
ARG SHADOW_DATABASE_URL=${SHADOW_DATABASE_URL}

ARG HASH_ROUNDS=${HASH_ROUNDS}
ARG JWT_SECRET=${JWT_SECRET}
ARG JWT_EXPIRES_IN=${JWT_EXPIRES_IN}

ARG EFI_CLIENT_ID=${EFI_CLIENT_ID}
ARG EFI_CLIENT_SECRET=${EFI_CLIENT_SECRET}
ARG EFI_BASE_URL=${EFI_BASE_URL}

ARG EVO_API_KEY=${EVO_API_KEY}
ARG EVO_BASE_URL=$EVO_BASE_URL{}

ARG STORAGE_BUCKET_NAME=$STORAGE_BUCKET_NAME{}
ARG STORAGE_REGION=${STORAGE_REGION}
ARG STORAGE_ENDPOINT=${STORAGE_ENDPOINT}
ARG STORAGE_CDN_URL=${STORAGE_CDN_URL}
ARG STORAGE_ACCESS_KEY=${STORAGE_ENDPOINT}
ARG STORAGE_SECRET_KEY=${STORAGE_SECRET_KEY}
ARG STORAGE_ACL=${STORAGE_ACL}
ARG STORAGE_UPLOAD_DIR=${STORAGE_UPLOAD_DIR}

ARG QDRANT_BASE_URL=${QDRANT_BASE_URL}
ARG QDRANT_BASE_PORT=${QDRANT_BASE_PORT}
ARG QDRANT_API_KEY=${QDRANT_API_KEY}
ARG OPENAI_API_KEY=${OPENAI_API_KEY}

ARG META_API_URL=${META_API_URL}

# Create app directory
WORKDIR /usr/src/app

# Expose port
EXPOSE 3333:3333

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package.json ./

# Install app dependencies
RUN yarn

# Bundle app source
COPY . .

ENV TZ="America/Sao_Paulo"
RUN date

# Creates a "dist" folder with the production build
RUN npx prisma generate && npx prisma migrate deploy && yarn build && yarn commander sync-roles && yarn commander sync-permissions

# Run Other Commands
RUN yarn commander subscribe-freemium

# RUN yarn global add dotenv-cli

# Start the server using the production build
# CMD [ "sh", "-c", "node ace migration:run --force && node ace deploy && node build/server.js" ]
# CMD env > /usr/src/app/.env && npx prisma generate && node dist/main.js

CMD node dist/main.js

# Base image
# FROM node:20.15.0

# Install cron
# RUN apt-get update && apt-get install -y cron

# Create app directory
# WORKDIR /usr/src/app

# Expose port
# EXPOSE 3333:3333

# A wildcard is used to ensure both package.json AND package-lock.json are copied
# COPY package.json ./

# Install app dependencies
# RUN yarn

# Bundle app source
# COPY . .

# Fix Timezone of Container
# ENV TZ="America/Sao_Paulo"
# RUN date

# Creates a "dist" folder with the production build
# RUN npx prisma generate && yarn build

# Install dotenv-cli
# RUN yarn global add dotenv-cli

# # Add crontab file in the cron directory
# COPY crontab /etc/cron.d/app-cron

# # Give execution rights on the cron job
# RUN chmod 0644 /etc/cron.d/app-cron

# # Apply cron job
# RUN crontab /etc/cron.d/app-cron

# # Create the log file to be able to run tail
# RUN touch /var/log/cron.log

# RUN chmod u+x /usr/src/app/runner.sh

# Start the server using the production build
# CMD env > /usr/src/app/.env && cron && node dist/src/main.js
