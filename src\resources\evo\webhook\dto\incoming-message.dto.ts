export class IncomingMessageWebhookDto {
  event: string;
  instance: string;
  data: DataIncomingMessage;
  destination: string;
  sender: string;
  server_url: string;
  apikey: string | null;
}

class ContextInfo {
  stanzaId: string;
  participant: string;
  quotedMessage: {
    conversation: string;
  };
}
class DataIncomingMessage {
  key: KeyIncomingMessage;
  pushName: string;
  status: string;
  message: MessageIncomingMessage;
  contextInfo?: ContextInfo;
  messageType: string;
  messageTimestamp: string;
  instanceId: string;
  source: string;
  state: string | undefined;
}

class KeyIncomingMessage {
  remoteJid: string;
  fromMe: boolean;
  id: string;
}

class MessageIncomingMessage {
  conversation: string;
  messageContextInfo?: {
    deviceListMetadata: {
      senderKeyHash: string;
      senderTimestamp: string;
      recipientKeyHash: string;
      recipientTimestamp: string;
    };
    deviceListMetadataVersion: number;
    messageSecret: string;
  };
  audioMessage?: {
    url: string;
    mimetype: string;
    waveform: string;
    seconds: number;
  };
  documentMessage?: {
    url: string;
    mimetype: string;
    fileName: string;
    fileSize: number;
  };
  imageMessage?: {
    url: string;
    mimetype: string;
    fileName: string;
    fileSize: number;
  };
  base64?: string;
}
