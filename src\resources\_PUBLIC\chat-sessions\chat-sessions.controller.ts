import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ChatSessionsService } from './chat-sessions.service';
import { Response } from 'express';
@Controller('chat-sessions')
export class ChatSessionsController {
  constructor(private readonly sessionsService: ChatSessionsService) {}

  @Get(':secureId')
  async findOne(@Param('secureId') secureId: string) {
    return await this.sessionsService.findOne(secureId);
  }

  @Get('/messages/:secureId')
  findMessages(@Param('secureId') secureId: string) {
    return this.sessionsService.findMessages(secureId);
  }
}
