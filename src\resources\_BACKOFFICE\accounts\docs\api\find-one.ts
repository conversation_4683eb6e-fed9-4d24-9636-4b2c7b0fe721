import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const okResponse: ApiResponseNoStatusOptions = {
  examples: {
    withData: {
      summary: 'Lista a account com base no secureId',
      value: {
        secureId: '57044b2c-67ea-4753-99c2-d84ec348b91d',
        companyName: 'plyrtech',
        isActive: false,
        isDeleted: false,
        createdAt: '2024-11-21T16:33:50.734Z',
        updatedAt: '2024-11-21T16:33:50.734Z',
      },
    },
  },
};

const notFound: ApiResponseNoStatusOptions = {
  examples: {
    accountNotFound: {
      summary: 'Account não encontrada',
      value: {
        message: 'Account não encontrada',
        error: 'Not Found',
        statusCode: 404,
      },
    },
  },
};

export const findOneAccount = {
  status: {
    okResponse,
    notFound,
  },
};
