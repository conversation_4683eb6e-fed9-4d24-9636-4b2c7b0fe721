import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const notFound: ApiResponseNoStatusOptions = {
  examples: {
    accountNotFound: {
      summary: 'Account não encontrada',
      value: {
        message: 'Account não encontrada',
        error: 'Not Found',
        statusCode: 404,
      },
    },
  },
};

const conflict: ApiResponseNoStatusOptions = {
  example: {
    message: 'Esse nome de empresa já existe. Utilize outro!',
    error: 'Conflict',
    statusCode: 409,
  },
};

export const updateAccount = {
  status: {
    notFound,
    conflict,
  },
};
