export class FormatMoney {
  static fromModelToOutput(modelValue: string): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(Number(modelValue) / 100);
  }

  static fromEntityToModel(entityValue: string): string {
    return entityValue.replace(/\D/g, '');
  }

  static fromOutputDtoToModel(outputDtoValue?: string): string | undefined {
    if (!outputDtoValue) {
      return undefined;
    }

    return outputDtoValue.replace(/\D/g, '');
  }
}
