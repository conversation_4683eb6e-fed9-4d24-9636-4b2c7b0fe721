import { ConfigType } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Inject, Injectable } from '@nestjs/common';

import evoEnvModule from './env.config';

@Injectable()
export class EvoApi {
  private static _instance: AxiosInstance;

  private constructor(
    @Inject(evoEnvModule.KEY)
    private readonly env: ConfigType<typeof evoEnvModule>,
  ) {}

  public static instance(env: ConfigType<typeof evoEnvModule>): AxiosInstance {
    if (!EvoApi._instance) {
      EvoApi._instance = axios.create({
        baseURL: env.baseUrl,
        timeout: 20 * 1000, // 20 seconds
        headers: {
          'Content-Type': 'application/json',
          apiKey: env.apiKey,
        },
      });
    }
    return EvoApi._instance;
  }
}
