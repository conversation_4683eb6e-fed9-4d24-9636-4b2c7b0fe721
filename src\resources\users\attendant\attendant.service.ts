import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { UpdateAttendantDto } from './dto/update-attendant.dto';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { AttendantOutputDto } from './dto/attendant-output.dto';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { AttendantFactory } from './factory/attendant.factory';
import { HashService } from 'src/resources/auth/jwt/hash/hash.service';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class AttendantService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly hashService: HashService,
  ) {}

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountId: number,
    name?: string,
    email?: string,
    onlyInRotation?: boolean,
  ): Promise<IWithPagination<AttendantOutputDto>> {
    const roles = await this.prismaService.roles.findMany({
      where: {
        slug: {
          in: ['ATTENDANT', 'APP', 'MASTER'],
        },
      },
    });

    const roleIds = roles.map((role) => role.id);

    const whereClause = {
      usersAccounts: {
        some: {
          roleId: { in: roleIds },
          isActive: isActiveIsDeletedQuery.isActive,
          // isDeleted: isActiveIsDeletedQuery.isDeleted,
          isDeleted: false,
          participatesInRotation:
            onlyInRotation !== undefined ? Boolean(onlyInRotation) : undefined,
          // isOwner: false,
          accountId: { equals: accountId },
        },
      },
      name: name && { contains: name },
      email: email && { contains: email },
    };

    const paginationHelper = new PaginationHelper({
      modelDelegate: this.prismaService.users,
      paginationQuery,
      whereClause: whereClause as never,
    });

    const attendantsModel = await this.prismaService.users.findMany({
      where: {
        ...whereClause,
      },

      include: {
        usersAccounts: {
          where: { accountId: accountId, isDeleted: false },
          select: {
            secureId: true,
            isActive: true,
            participatesInRotation: true,
          },
        },
      },

      take: paginationHelper.take,
      skip: paginationHelper.skip,
    });

    const meta = await paginationHelper.getPaginationMeta();
    const data = !attendantsModel
      ? ([] as AttendantOutputDto[])
      : AttendantFactory.createBatchAttendantOutputDtoFromBatchModel(
          attendantsModel,
        );

    return {
      meta: meta,
      data: data,
    };
  }

  async findOne(secureId: string, accountId): Promise<AttendantOutputDto> {
    const attendantModel = await this.prismaService.users.findFirst({
      where: {
        secureId,
      },
      include: {
        usersAccounts: {
          where: { accountId: accountId, isDeleted: false },
          select: {
            secureId: true,
            isActive: true,
            participatesInRotation: true,
            accountsPermissions: {
              select: {
                permission: {
                  select: {
                    secureId: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return AttendantFactory.createOneBatchAttendantOutputDtoFromBatchModel(
      attendantModel,
    );
  }

  async deleteAttendant(attendantSecureId: string, accountId: number) {
    const attendantUserModel = await this.prismaService.users.findFirst({
      where: {
        secureId: attendantSecureId,
      },
      select: {
        id: true,
        usersAccounts: {
          where: { accountId: accountId, isDeleted: false },
          select: {
            secureId: true,
            isOwner: true,
          },
        },
      },
    });

    if (attendantUserModel.usersAccounts[0].isOwner) {
      throw new ConflictException(
        'Não é possível excluir o proprietário da conta',
      );
    }

    if (!attendantUserModel) {
      throw new NotFoundException('Attendant not found');
    }

    const attendantAccountModel =
      await this.prismaService.usersAccounts.findFirst({
        where: {
          userId: { equals: attendantUserModel.id },
          accountId: { equals: accountId },
        },

        select: {
          secureId: true,
        },
      });

    await this.prismaService.usersAccounts.update({
      where: {
        secureId: attendantAccountModel.secureId,
      },

      data: {
        isActive: false,
        isDeleted: true,
      },
    });

    return;
  }

  async updateAttendant(
    attendantSecureId: string,
    updateAttendantDto: UpdateAttendantDto,
    accountId: number,
    userIdToRequest: number,
  ) {
    const attendantUserModel = await this.prismaService.users.findFirst({
      where: {
        secureId: { equals: attendantSecureId },
        isDeleted: false,
      },
      select: { id: true, email: true, cpf: true },
    });

    if (!attendantUserModel) {
      throw new NotFoundException('Atendente não encontrado!');
    }

    const attendantAccountModel =
      await this.prismaService.usersAccounts.findFirst({
        where: {
          userId: { equals: attendantUserModel.id },
          accountId: { equals: accountId },
          isDeleted: false,
        },

        select: {
          id: true,
          secureId: true,
          isOwner: true,
          accountsPermissions: {
            select: {
              secureId: true,
            },
          },
        },
      });

    if (!attendantAccountModel) {
      throw new NotFoundException(
        'Nenhuma conta foi encontrada para esse atendente. Talvez essa conta tenha sido excluída.',
      );
    }

    if (
      attendantAccountModel.isOwner &&
      userIdToRequest !== attendantUserModel.id
    ) {
      throw new ConflictException(
        'Você não tem permissão para alterar o proprietário da conta',
      );
    }

    const { isActive, participatesInRotation } = updateAttendantDto;

    if (isActive !== undefined || participatesInRotation !== undefined) {
      await this.prismaService.usersAccounts.update({
        where: {
          secureId: attendantAccountModel.secureId,
        },
        data: {
          isActive: isActive,
          participatesInRotation: participatesInRotation,
        },
      });
    }

    const { name, email, cellPhone, cpf, password } = updateAttendantDto;
    const formattedCpf = cpf ? cpf.replace(/\D/g, '') : undefined;
    const formattedCellPhone = cellPhone
      ? cellPhone.replace(/\D/g, '')
      : undefined;

    if (!!email && email !== attendantUserModel.email) {
      const emailAlreadyExists = await this.checkIfRecordExistsByEmail(email);
      if (emailAlreadyExists) {
        throw new ConflictException(
          'Já existe um usuário com esse e-mail cadastrado na sua conta',
        );
      }
    }

    if (!!formattedCpf && formattedCpf !== attendantUserModel.cpf) {
      const cpfAlreadyExists =
        await this.checkIfRecordExistsByCPF(formattedCpf);

      if (cpfAlreadyExists) {
        throw new ConflictException(
          'Já existe um usuário com esse CPF cadastrado na sua conta',
        );
      }
    }

    await this.prismaService.users.update({
      where: {
        id: attendantUserModel.id,
      },

      data: {
        name,
        email,
        cellPhone: formattedCellPhone,
        cpf: formattedCpf,
        password: password
          ? await this.hashService.generate(password)
          : undefined,
      },
    });

    if (updateAttendantDto.hasAllPermissions) {
      const permissionsModel = await this.prismaService.permissions.findMany({
        select: { id: true },
        where: { group: 'app' },
      });

      if (!permissionsModel) {
        throw new NotFoundException(
          'Permissões de atendentes não encontradas. Contate o suporte.',
        );
      }

      await this.prismaService.accountsPermissions.deleteMany({
        where: {
          userAccountId: attendantAccountModel.id,
        },
      });

      const permissions = permissionsModel.map((permission) => {
        return {
          secureId: uuidV4(),
          userAccountId: attendantAccountModel.id,
          permissionId: permission.id,
        };
      });

      await this.prismaService.accountsPermissions.createMany({
        data: permissions,
      });

      return;
    }

    if (
      updateAttendantDto.permissionsSecureIds &&
      updateAttendantDto.permissionsSecureIds.length > 0
    ) {
      const permissionModel = await this.prismaService.permissions.findMany({
        where: {
          secureId: {
            in: updateAttendantDto.permissionsSecureIds,
          },
        },
        select: { id: true },
      });

      if (!permissionModel) {
        throw new NotFoundException(
          'Permissões de atendente não encontradas. Contate o suporte.',
        );
      }

      await this.prismaService.accountsPermissions.deleteMany({
        where: {
          userAccountId: attendantAccountModel.id,
        },
      });

      const permissions = permissionModel.map((permission) => {
        return {
          secureId: uuidV4(),
          userAccountId: attendantUserModel.id,
          permissionId: permission.id,
        };
      });

      await this.prismaService.accountsPermissions.createMany({
        data: permissions,
      });
    }

    return;
  }

  private async checkIfRecordExistsByEmail(email: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM users WHERE email = ?) AS has;`,
      email,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  private async checkIfRecordExistsByCPF(cpf: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM users WHERE cpf = ?) AS has;`,
      cpf,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }
}
