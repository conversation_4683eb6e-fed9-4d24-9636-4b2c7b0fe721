import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

export const TokenPayload = createParamDecorator(
  (
    data: string | undefined,
    ctx: ExecutionContext,
  ): ReceivedUserTokenPayload => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user; // NOTE: A<PERSON>ho é onde o token é decodificado e o payload é retornado

    return data ? user?.[data] : user;
  },
);
