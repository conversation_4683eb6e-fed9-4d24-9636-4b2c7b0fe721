import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { v4 as uuid } from 'uuid';

@Injectable()
export class GenerateContactsService {
  async execute(prisma: PrismaClient) {
    console.log(
      '\x1b[32m',
      `Starting contact generation from chat sessions...`,
    );

    const sessions = await prisma.chatSessions.findMany({
      where: {
        OR: [
          { customerEmail: { not: null } },
          { customerPhone: { not: null } },
          { customerDocument: { not: null } },
        ],
      },
      select: {
        id: true,
        accountId: true,
        customerName: true,
        customerEmail: true,
        customerPhone: true,
        customerDocument: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    const totalSessions = sessions.length;
    let processedSessions = 0;
    let contactsUpserted = 0;

    console.log(`Found ${totalSessions} relevant sessions to process.`);

    for await (const session of sessions) {
      const whereConditions = [];
      if (session.customerEmail) {
        whereConditions.push({ email: session.customerEmail });
      }
      if (session.customerPhone) {
        whereConditions.push({ phone: session.customerPhone });
      }
      if (session.customerDocument) {
        whereConditions.push({ document: session.customerDocument });
      }

      if (whereConditions.length === 0 || !session.accountId) {
        processedSessions++;
        this.updateProgressBar(
          processedSessions,
          totalSessions,
          contactsUpserted,
        );
        continue;
      }

      const existingContact = await prisma.contacts.findFirst({
        where: {
          accountId: session.accountId,
          OR: whereConditions,
        },
      });

      if (existingContact) {
        await prisma.contacts.update({
          where: { id: existingContact.id },
          data: {
            name: session.customerName ?? existingContact.name,
            email: session.customerEmail ?? existingContact.email,
            phone: session.customerPhone ?? existingContact.phone,
            document: session.customerDocument ?? existingContact.document,
            lastUpdatedSessionId: session.id,
            updatedAt: new Date(),
          },
        });
        contactsUpserted++;
      } else {
        await prisma.contacts.create({
          data: {
            secureId: uuid(),
            accountId: session.accountId,
            email: session.customerEmail,
            phone: session.customerPhone,
            document: session.customerDocument,
            name: session.customerName,
            lastUpdatedSessionId: session.id,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });
        contactsUpserted++;
      }

      processedSessions++;
      this.updateProgressBar(
        processedSessions,
        totalSessions,
        contactsUpserted,
      );
    }

    process.stdout.write('\n'); // New line after progress bar
    console.log(
      '\x1b[32m',
      `Contact generation complete. Processed ${totalSessions} sessions. Upserted ${contactsUpserted} contacts.`,
    );
  }

  private updateProgressBar(
    current: number,
    total: number,
    upserted: number,
  ): void {
    const percentage = total === 0 ? 100 : Math.floor((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.floor((percentage * barLength) / 100);

    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(barLength - filledLength);

    process.stdout.write(
      `\r[${filledBar}${emptyBar}] ${percentage}% (${current}/${total}) Sessions | ${upserted} Contacts Upserted`,
    );
  }
}
