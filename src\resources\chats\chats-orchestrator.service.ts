import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  MessageFlowInputDTO,
  MessageFlowOutputDTO,
} from './messages/dto/message-flow.dto';
import { ChatsService } from './chats.service';
import { EvoMessageService } from 'src/third-party/evo/message/message.service';
import { S3Service } from 'src/third-party/s3/s3.service';
import { FacebookGraphService } from 'src/third-party/meta/facebook-graph/facebook-graph.service';
import SendWppMessageInputType from 'src/types/chats/chats-orchestrator/send-wpp-message';

@Injectable()
export class ChatsOrchestratorService {
  constructor(
    private readonly chatsService: ChatsService,
    private readonly evoMessageService: EvoMessageService,
    private readonly s3Service: S3Service,
    private readonly facebookGraphService: FacebookGraphService,
  ) {}

  async orchestrateMessageFlow(
    messageFlowPayload: MessageFlowInputDTO,
  ): Promise<MessageFlowOutputDTO> {
    const {
      userId,
      sessionId,
      content,
      isAttendant,
      chatId,
      customerName,
      customerId,
      fileSecureId,
    } = messageFlowPayload;
    try {
      if (!chatId) {
        const session = await this.chatsService.findSession(sessionId);

        const attendantName =
          await this.chatsService.findAttendantNameBySecureId(userId);

        if (!session) {
          throw new Error('Sessão não encontrada');
        }

        const newMessage = await this.chatsService.createMessage({
          sessionSecureId: session.secureId,
          sendMessage: content,
          receiveMessage: undefined,
          messageDirection: isAttendant ? 'sent' : 'received',
          uploadSecureId: fileSecureId,
          userId: userId,
          replyTo: messageFlowPayload.replyTo,
        });

        const messageToSend = {
          secureId: newMessage.secureId,
          sendMessage: newMessage.sendMessage,
          receiveMessage: newMessage.receiveMessage,
          messageDirection: newMessage.messageDirection,
          sessionId,
          createdAt: newMessage.createdAt,
          messageType: newMessage.messageType,
          urlFile: newMessage.upload?.urlCdn,
          attendantName: newMessage.userAccount?.user.name,
          replyTo: messageFlowPayload.replyTo,
        };

        const contentWithName = `*${attendantName}:*\n\n${content}`;

        let messageToReply: any = undefined;

        if (messageFlowPayload.replyTo && newMessage.replyTo.messageJid) {
          messageToReply = {
            key: {
              id: newMessage.replyTo.messageJid,
              fromMe: newMessage.replyTo.messageDirection === 'sent',
              remoteJid: session.customerPhone,
            },
            message: {
              conversation: newMessage.replyTo.message,
            },
          };
        }

        await this.sendWhatsappMessage({
          contentWithName,
          fileName: newMessage.upload?.fileName,
          fileType: newMessage.upload?.type,
          fileUrl: newMessage.upload?.urlCdn,
          messageToSend: content,
          messageToReply,
          messageType: newMessage.messageType,
          mimeType: newMessage.upload?.fileType,
          session,
        });

        return { success: true, messageToSend };
      } else {
        const chat = await this.chatsService.findOne(chatId);

        const session = await this.chatsService.findSession(sessionId, chatId);

        if (!session) {
          const newSession = await this.chatsService.createSession({
            secureId: sessionId,
            accountId: chat.account.secureId,
            chatId: chat.secureId,
            customerId,
            customerName,
          });

          if (!newSession) {
            return { error: 'Failed to create session' };
          }

          const newMessage = await this.chatsService.createMessage({
            sessionSecureId: newSession.secureId,
            uploadSecureId: fileSecureId,
            messageDirection: isAttendant ? 'sent' : 'received',
            sendMessage: isAttendant ? content : undefined,
            receiveMessage: !isAttendant ? content : undefined,
            userId: userId,
            replyTo: messageFlowPayload.replyTo,
          });

          if (!newMessage) {
            return { error: 'Failed to send message' };
          }

          const messageToSend = {
            secureId: newMessage.secureId,
            sendMessage: newMessage.sendMessage,
            receiveMessage: newMessage.receiveMessage,
            messageDirection: newMessage.messageDirection,
            sessionId,
            createdAt: newMessage.createdAt,
            messageType: newMessage.messageType,
            urlFile: newMessage.upload?.urlCdn,
            attendantName: newMessage.userAccount?.user.name,
            replyTo: messageFlowPayload.replyTo,
          };

          return { success: true, messageToSend };
        }

        const newMessage = await this.chatsService.createMessage({
          sessionSecureId: session.secureId,
          uploadSecureId: fileSecureId,
          messageDirection: isAttendant ? 'sent' : 'received',
          sendMessage: isAttendant ? content : undefined,
          receiveMessage: !isAttendant ? content : undefined,
          userId: userId,
          replyTo: messageFlowPayload.replyTo,
        });

        const messageToSend = {
          secureId: newMessage.secureId,
          sendMessage: newMessage.sendMessage,
          receiveMessage: newMessage.receiveMessage,
          sessionId,
          createdAt: newMessage.createdAt,
          messageDirection: newMessage.messageDirection,
          messageType: newMessage.messageType,
          urlFile: newMessage.upload?.urlCdn,
          attendantName: newMessage.userAccount?.user.name,
          replyTo: messageFlowPayload.replyTo,
        };

        return { success: true, messageToSend };
      }
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Não foi possível enviar a mensagem');
    }
  }

  private async sendWhatsappMessage({
    contentWithName,
    fileName,
    fileType,
    fileUrl,
    messageToReply,
    messageToSend,
    messageType,
    mimeType,
    session,
  }: SendWppMessageInputType): Promise<void> {
    const isBusiness = session.whatsapp?.isBusiness;

    const messageToSendWithName = messageToSend ? contentWithName : undefined;

    const messageStrategies = {
      'text-business': async () => {
        await this.facebookGraphService.sendWhatsAppText(
          session.whatsapp?.token,
          session.whatsapp?.numberId,
          session.customerPhone,
          contentWithName,
          messageToReply,
        );
      },
      'text-regular': async () => {
        await this.evoMessageService.sendMessage(
          session.customerId,
          contentWithName,
          session.whatsapp?.instanceName,
          1200,
          messageToReply,
        );
      },
      'audio-business': async () => {
        await this.facebookGraphService.sendWhatsAudio(
          session.whatsapp?.token,
          session.whatsapp?.numberId,
          fileUrl,
          mimeType,
          session.customerPhone,
          messageToReply,
        );
      },
      'audio-regular': async () => {
        await this.evoMessageService.sendNarratedAudio(
          session.customerId,
          session.whatsapp?.instanceName,
          fileUrl,
          1200,
          messageToReply,
        );
      },
      'media-business': async () => {
        await this.facebookGraphService.sendWhatsAppFile(
          session.whatsapp?.token,
          session.whatsapp?.numberId,
          fileUrl,
          mimeType,
          session.customerPhone,
          messageToSendWithName,
          messageToReply,
        );
      },
      'media-regular': async () => {
        await this.evoMessageService.sendMediaUrl({
          number: session.customerId,
          message: messageToSendWithName,
          instanceName: session.whatsapp?.instanceName,
          urlOrBase64: fileUrl,
          messageToReply: messageToReply,
          fileName: fileName,
          fileType: fileType,
          mimeType: mimeType,
        });
      },
    };

    let strategyType = !fileUrl
      ? 'text'
      : messageType === 'audio'
        ? 'audio'
        : 'media';
    const strategyKey = `${strategyType}-${isBusiness ? 'business' : 'regular'}`;
    if (messageStrategies[strategyKey]) {
      await messageStrategies[strategyKey]();
    } else {
      throw new BadRequestException(
        `Unsupported message type or strategy: ${strategyKey}`,
      );
    }
  }
}
