import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { SessionFactory } from './sessions/factory/session.factory';
import { CreateSessionInputDTO } from './sessions/dto/create-session.dto';
import { format } from 'date-fns';
import { CreateMessageInputDTO } from './messages/dto/create-message.dto';
import { v4 as uuidV4 } from 'uuid';
import { MessageFactory } from './messages/factory/message.factory';
import { ChatFactory } from './factory/chat.factory';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { ChatMapper } from './mapper/chat.mapper';
import { CreateChatInputDto } from './dto/create-chat.dto';
import { Chats } from './entities/chat.entity';
import { UpdateChatInputDto } from './dto/update-chat.dto';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';
import { NotificationService } from '../notification/notification.service';

@Injectable()
export class ChatsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationService: NotificationService,
  ) {}

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountId: number,
  ) {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      name: {
        contains: paginationQuery?.search && paginationQuery.search,
      },
      accountId: { equals: accountId },
    };

    const chatsModel = await this.prisma.chats.findMany({
      take: paginationQuery.limit,
      skip,
      orderBy: {
        createdAt: 'desc',
      },
      where: whereClause,
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prisma.chats,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    return {
      meta,
      data: !chatsModel
        ? ([] as Chats[])
        : ChatFactory.convertBatchFromModelToChat(chatsModel),
    };
  }

  async findOne(id: string) {
    const chat = await this.prisma.chats.findFirst({
      where: {
        secureId: id,
      },
      include: {
        account: {
          select: {
            secureId: true,
          },
        },
        upload: {
          select: {
            secureId: true,
            urlCdn: true,
          },
        },
        chatbot: {
          select: {
            secureId: true,
            isAI: true,
          },
        },
      },
    });

    if (!chat) {
      throw new NotFoundException('Chat não encontrado');
    }

    return ChatMapper.toEntity(chat);
  }

  async create(createChatInputDto: CreateChatInputDto, uploadSecureId: string) {
    const account = await this.prisma.accounts.findFirst({
      where: {
        secureId: createChatInputDto.accountSecureId,
      },
    });

    var chatbotId: number | undefined;

    if (createChatInputDto.chatBotSecureId) {
      const chatbot = await this.prisma.chatBots.findFirst({
        where: {
          secureId: createChatInputDto.chatBotSecureId,
          accountId: account.id,
        },
      });

      if (!chatbot) {
        throw new NotFoundException('Chatbot não encontrado');
      }

      chatbotId = chatbot.id;
    }

    const imageUpload = await this.prisma.uploads.findFirst({
      where: {
        secureId: uploadSecureId,
      },
    });

    if (!account || !imageUpload) {
      throw new NotFoundException('Account ou Imagem não encontrada');
    }

    const newChat = new Chats({
      ...createChatInputDto,
      accountId: account.id,
      uploadId: imageUpload.id,
      chatbotId: chatbotId,
      isActive: true,
      isDeleted: false,
    });

    const chatModel = ChatMapper.toModel(newChat);

    const newChatPrisma = await this.prisma.chats.create({
      data: chatModel,
    });

    return newChatPrisma.secureId;
  }

  async update(
    secureId: string,
    updateChatInputDto: UpdateChatInputDto,
    uploadSecureId?: string,
  ) {
    const { accountSecureId, chatBotSecureId, ...updateChat } =
      updateChatInputDto;
    var chatbotId: number | null = null;
    if (chatBotSecureId) {
      const chatbot = await this.prisma.chatBots.findFirst({
        where: {
          secureId: chatBotSecureId,
        },
      });

      if (!chatbot) {
        throw new NotFoundException('Chatbot não encontrado');
      }

      chatbotId = chatbot.id;
    }

    const chat = await this.prisma.chats.findFirst({
      where: {
        secureId: { equals: secureId },
      },
    });

    if (!chat) {
      throw new NotFoundException('Account não encontrada');
    }

    let imagemId: number | undefined;
    if (uploadSecureId) {
      const imageUpload = await this.prisma.uploads.findFirst({
        where: {
          secureId: uploadSecureId,
        },
      });

      if (!imageUpload) {
        throw new NotFoundException('Upload não encontrado');
      }

      imagemId = imageUpload.id;
    }

    const chatbotIsNull = chatBotSecureId === '';

    await this.prisma.chats.update({
      where: {
        secureId: secureId,
      },
      data: {
        uploadId: imagemId,
        chatbotId: chatBotSecureId || chatbotIsNull ? chatbotId : undefined,
        ...updateChat,
      },
    });

    return;
  }

  async delete(secureId: string) {
    const chat = await this.prisma.chats.findFirst({
      where: {
        secureId: secureId,
      },
    });

    if (!chat) {
      throw new NotFoundException('Chat não encontrado');
    }

    await this.prisma.chats.update({
      where: {
        secureId: secureId,
      },
      data: {
        isDeleted: true,
        isActive: false,
      },
    });

    return;
  }

  async findSession(sessionId: string, chatId?: string) {
    const where: any = {
      secureId: sessionId,
    };

    if (chatId) {
      where.chat = {
        secureId: chatId,
      };
    }

    const sessions = await this.prisma.chatSessions.findFirst({
      where: where,
      include: {
        whatsapp: true,
      },
    });

    if (!sessions) {
      return null;
    }

    if (!sessions?.whatsapp && !chatId) {
      const wppIntegrion = await this.prisma.whatsAppIntegration.findFirst({
        where: {
          isActive: true,
          accountId: sessions.accountId,
          isDeleted: false,
        },
      });
      sessions.whatsapp = wppIntegrion;
    }

    return SessionFactory.convertFromModelToSession(sessions);
  }

  async findAttendantNameBySecureId(userId: string) {
    const attendant = await this.prisma.users.findFirst({
      where: {
        secureId: userId,
      },
      select: {
        name: true,
      },
    });

    if (!attendant) {
      return null;
    }

    return attendant.name;
  }

  async createSession(createSessionInputDTO: CreateSessionInputDTO) {
    const senderName = createSessionInputDTO.customerName
      ? createSessionInputDTO.customerName
      : `Usuário ${format(new Date(), 'dd/MM/yy HH:mm')}`;

    const attendant = await this.prisma.usersAccounts.findMany({
      where: {
        account: {
          secureId: createSessionInputDTO.accountId,
        },
        isActive: true,
        isDeleted: false,
        AND: {
          OR: [{ role: { name: 'Attendant' } }, { role: { name: 'App' } }],
          participatesInRotation: true,
        },
      },
    });

    //Roleta de atendentes
    let attendantId: string | undefined = undefined;
    if (attendant.length > 0) {
      const randomAttendant = Math.floor(Math.random() * attendant.length);
      attendantId = attendant[randomAttendant].secureId;
    }

    const session = await this.prisma.chatSessions.create({
      data: {
        source: ChatSourceEnum.webchat,
        secureId: createSessionInputDTO.secureId,
        account: {
          connect: {
            secureId: createSessionInputDTO.accountId,
          },
        },
        attendant: attendantId
          ? {
              connect: {
                secureId: attendantId,
              },
            }
          : undefined,
        customerId: createSessionInputDTO.customerId,
        customerName: senderName,
        chat: {
          connect: {
            secureId: createSessionInputDTO.chatId,
          },
        },
      },
    });

    return SessionFactory.convertFromModelToSession(session);
  }

  async createMessage(createMessageInputDTO: CreateMessageInputDTO) {
    let messageType = 'text';
    let uploadData = {};
    if (createMessageInputDTO.uploadSecureId) {
      const upload = await this.prisma.uploads.findFirst({
        where: {
          secureId: createMessageInputDTO.uploadSecureId,
        },
        select: {
          type: true,
        },
      });

      if (!upload) {
        throw new NotFoundException('Upload não encontrado');
      }
      messageType = upload.type;

      uploadData = {
        upload: {
          connect: {
            secureId: createMessageInputDTO.uploadSecureId,
          },
        },
      };
    }

    const data: any = {
      secureId: uuidV4(),
      receiveMessage: createMessageInputDTO.receiveMessage,
      sendMessage: createMessageInputDTO.sendMessage,
      messageDirection: createMessageInputDTO.messageDirection,
      messageType: messageType,
      chatSession: {
        connect: {
          secureId: createMessageInputDTO.sessionSecureId,
        },
      },
      replyTo: createMessageInputDTO?.replyTo
        ? {
            connect: {
              secureId: createMessageInputDTO.replyTo,
            },
          }
        : undefined,
      ...uploadData,
    };

    if (createMessageInputDTO.userId) {
      const userAccountExists = await this.prisma.usersAccounts.findFirst({
        where: { user: { secureId: { equals: createMessageInputDTO.userId } } },
      });

      if (!userAccountExists) {
        throw new NotFoundException('Conta do usuário não encontrada');
      }

      data.userAccount = {
        connect: {
          secureId: userAccountExists.secureId,
        },
      };
    }

    if (createMessageInputDTO.chatBotId) {
      data.chatBot = {
        connect: {
          secureId: createMessageInputDTO.chatBotId,
        },
      };
    } else {
      // Busca automaticamente o chatBotId baseado na sessão
      const chatBotId = await this.getChatBotIdFromSession(createMessageInputDTO.sessionSecureId);
      if (chatBotId) {
        data.chatBot = {
          connect: {
            id: chatBotId,
          },
        };
      }
    }

    const message = await this.prisma.chatMessages.create({
      data,
      include: {
        chatSession: {
          select: {
            customerName: true,
            attendant: {
              select: {
                userId: true,
              },
            },
            accountId: true,
            id: true,
          },
        },
        upload: {
          select: {
            secureId: true,
            urlCdn: true,
            type: true,
            fileType: true,
            fileName: true,
          },
        },
        replyTo: true,
      },
    });

    if (createMessageInputDTO.receiveMessage) {
      await this.notificationService.createNotification({
        title: 'Nova mensagem recebida',
        message: `Nova mensagem recebida de ${message.chatSession.customerName}`,
        userId: message.chatSession.attendant?.userId,
        accountId: message.chatSession.accountId,
        sessionId: message.chatSession.id,
      });
    }

    await this.prisma.chatSessions.update({
      where: {
        secureId: createMessageInputDTO.sessionSecureId,
      },
      data: {
        isArchived: createMessageInputDTO.receiveMessage ? false : undefined,
        // NOVA REGRA: Se atendente enviou mensagem, marca como não finalizada
        isFinalized: createMessageInputDTO.userId ? false : undefined,
      },
    });

    return MessageFactory.convertFromModelToMessage(message);
  }

  /**
   * Busca o chatBotId baseado no sessionSecureId seguindo a relação:
   * chat_session_id > chat_id > chatbot_id
   */
  private async getChatBotIdFromSession(sessionSecureId: string): Promise<number | null> {
    const session = await this.prisma.chatSessions.findUnique({
      where: { secureId: sessionSecureId },
      select: {
        chat: {
          select: {
            chatbotId: true,
          },
        },
        whatsapp: {
          select: {
            chatbotId: true,
          },
        },
      },
    });

    if (!session) {
      return null;
    }

    // Prioriza chatbot do chat, depois do whatsapp
    return session.chat?.chatbotId || session.whatsapp?.chatbotId || null;
  }

  async attendantInterrupt(sessionSecureId: string, attendantSecureId: string) {
    // Verificar se a sessão existe e obter accountId em uma única consulta
    const chatSession = await this.prisma.chatSessions.findFirst({
      where: { secureId: sessionSecureId },
      select: { secureId: true, accountId: true },
    });

    if (!chatSession) {
      throw new NotFoundException('Chat não encontrado');
    }

    // Buscar o atendente e atualizar a sessão em uma transação
    return this.prisma.$transaction(async (tx) => {
      const attendant = await tx.usersAccounts.findFirst({
        where: {
          user: { secureId: attendantSecureId },
          accountId: chatSession.accountId,
          isActive: true,
        },
        select: { id: true },
      });

      if (!attendant) {
        throw new NotFoundException('Atendente não encontrado');
      }

      return tx.chatSessions.update({
        where: { secureId: sessionSecureId },
        data: {
          isAIResponder: false,
          attendantId: attendant.id,
        },
      });
    });
  }
}
