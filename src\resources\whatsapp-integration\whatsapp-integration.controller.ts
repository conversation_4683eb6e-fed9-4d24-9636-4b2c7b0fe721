import {
  Controller,
  Post,
  UseGuards,
  Body,
  HttpCode,
  Get,
  Param,
  Put,
  Query,
} from '@nestjs/common';
import { WhatsappIntegrationService } from './whatsapp-integration.service';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { SubscriptionGuard } from 'src/@shared/guards/subscription/subscription.guard';
import { CanAddNewWhatsAppNumberGuard } from 'src/@shared/guards/plan/whatsapp-numbers.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { CreateWhatsappIntegrationDTO } from './dto/create-whatsapp-integration.dto';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { CreateEvoInstanceOutputDTO } from './dto/create-whatsapp-integration-output.dto';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { UpdateWhatsappIntegrationDto } from './dto/update-whatsapp-integration.dto';

@Controller('whatsapp-integration')
export class WhatsappIntegrationController {
  constructor(
    private readonly whatsappIntegrationService: WhatsappIntegrationService,
  ) {}

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_view', 'app_view'])
  @Get()
  async findAll(
    @AccountIdExtractor() accountId: number,
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @Query('onlyType') onlyType?: 'messager' | 'business',
  ) {
    return await this.whatsappIntegrationService.findAll(
      accountId,
      paginationQuery,
      isActiveIsDeletedQuery,
      onlyType,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_view', 'app_view'])
  @Get(':secureId')
  async findOne(
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.whatsappIntegrationService.findOne(secureId, accountId);
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
    SubscriptionGuard,
    CanAddNewWhatsAppNumberGuard,
  )
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_create', 'app_create'])
  @HttpCode(200)
  @Post()
  async create(
    @Body() createWhatsappIntegrationDto: CreateWhatsappIntegrationDTO,
    @AccountIdExtractor() accountId: number,
  ): Promise<CreateEvoInstanceOutputDTO> {
    return await this.whatsappIntegrationService.create(
      createWhatsappIntegrationDto,
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_edit', 'app_edit'])
  @Put(':secureId')
  async update(
    @Param('secureId') secureId: string,
    @Body() updateEvoIntegrationDto: UpdateWhatsappIntegrationDto,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.whatsappIntegrationService.update(
      secureId,
      updateEvoIntegrationDto,
      accountId,
    );
  }
}
