import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { EFIGetTokenModule } from '../get-token/efi-get-token.module';

import { EFIPlansService } from './efi-plans.service';

import efiPlansEnvs from './env.config';

@Module({
  imports: [ConfigModule.forFeature(efiPlansEnvs), EFIGetTokenModule],
  providers: [EFIPlansService],
  exports: [EFIPlansService, EFIGetTokenModule],
})
export class EfiPlansModule {}
