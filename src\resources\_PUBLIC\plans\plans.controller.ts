import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { PublicPlansService } from './plans.service';
import { DoesPlanSlugExistsGuard } from 'src/@shared/guards/plan/does-slug-exists.guard';
import { Plan } from 'src/resources/_BACKOFFICE/plans/entities/plan.entity';

@Controller('plans')
export class PublicPlansController {
  constructor(private readonly publicPlanService: PublicPlansService) {}

  @Get()
  async findAllActive() {
    return await this.publicPlanService.findAllActive();
  }

  @UseGuards(DoesPlanSlugExistsGuard)
  @Get('bySlug/:slug')
  async findBySlug(@Param('slug') slug: string): Promise<Plan> {
    return await this.publicPlanService.findBySlug(slug);
  }
}
