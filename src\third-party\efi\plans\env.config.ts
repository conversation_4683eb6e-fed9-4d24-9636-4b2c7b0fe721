import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { IEFIPlansEnvs } from './efi-plans.contract';

export default registerAs('efi-plans-envs', () => {
  const values: IEFIPlansEnvs = {
    baseUrl: process.env.EFI_BASE_URL,
    plansEP: '/v1/plans',
    planEP: '/v1/plan',
  };

  const schema = Joi.object<IEFIPlansEnvs>({
    baseUrl: Joi.string().required().messages({
      'any.required': 'ENV: EFI_BASE_URL is required',
    }),
    plansEP: Joi.string().required().messages({
      'any.required': 'Declaration: PlansEP is not declared and is required',
    }),
    planEP: Joi.string().required().messages({
      'any.required': 'Declaration: PlanEP is not declared and is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
