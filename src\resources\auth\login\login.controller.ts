import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  MethodNotAllowedException,
} from '@nestjs/common';

import { LoginService } from './login.service';

import { LoginInputDto } from './dto/login-input.dto';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { ChangeAccountInputDto } from './dto/select-account-input.dto';
import { TokenGuard } from 'src/@shared/guards/token/token.guard';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';
import { PrintTokenPayload } from 'src/@shared/guards/print-token-payload/print-token-payload.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import {
  ApiBadRequestResponse,
  ApiCreatedResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { LoginOutputDto } from './dto/login-output.dto';
import { swaggerLoginApi } from './docs';

interface ChangeAccountRequest extends Request {
  user: ReceivedUserTokenPayload;
}

@ApiTags('Login')
@Controller('login')
export class LoginController {
  constructor(private readonly loginService: LoginService) { }

  @Post()
  @ApiOperation({
    summary: 'Realiza o login e retorna o token',
  })
  @ApiCreatedResponse(swaggerLoginApi.login.created)
  @ApiBadRequestResponse(swaggerLoginApi.login.badRequest)
  @ApiNotFoundResponse(swaggerLoginApi.login.notFound)
  async login(@Body() loginInputDto: LoginInputDto): Promise<LoginOutputDto> {
    return await this.loginService.login(loginInputDto);
  }

  @UseGuards(JwtGuard, TokenGuard)
  @Post('change-account')
  @ApiOperation({
    summary: 'Troca de conta',
  })
  async changeAccount(
    @Body() body: ChangeAccountInputDto,
    @Req() req: ChangeAccountRequest,
  ): Promise<LoginOutputDto> {
    const token = req.user;

    return await this.loginService.changeAccount(body, token.subject);
  }

  // XXX: Delete me later
  @UseGuards(JwtGuard, PrintTokenPayload)
  @Get('test')
  async decode() {
    if (process.env.NODE_ENV === 'production') {
      throw new MethodNotAllowedException();
    }
    return;
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Permissions([])
  @Roles(['MASTER'])
  @Get('master')
  testeMaster() {
    if (process.env.NODE_ENV === 'production') {
      throw new MethodNotAllowedException();
    }
    return 'only Master';
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Permissions([])
  @Roles(['BACKOFFICE'])
  @Get('backoffice')
  testeBackoffice() {
    if (process.env.NODE_ENV === 'production') {
      throw new MethodNotAllowedException();
    }
    return 'Master and Backoffice';
  }

  @UseGuards(JwtGuard, RoleGuard)
  // @Roles(['INSIGHTER'])
  @Get('insighter')
  testeInsighter() {
    if (process.env.NODE_ENV === 'production') {
      throw new MethodNotAllowedException();
    }
    return 'Master and Insighter';
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  // @Permissions(['partner_edit'])
  // @Roles(['PARTNER'])
  @Get('partner')
  testePartner() {
    if (process.env.NODE_ENV === 'production') {
      throw new MethodNotAllowedException();
    }
    return 'Master and Partner';
  }
}
