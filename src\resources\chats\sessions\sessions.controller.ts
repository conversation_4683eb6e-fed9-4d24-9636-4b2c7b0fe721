import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { SessionsService } from './sessions.service';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { UpdateSessionDto } from './dto/update-session.dto';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { IsAIResponderDecorator } from 'src/@shared/decorators/is-ai-responder/is-ai-responder.decorator';
import { IsAIResponderQueryType } from 'src/@shared/types/decorators/is-ai-responder';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';
import { SessionsParamsQueryType } from 'src/@shared/types/decorators/show-all';
import { StartSessionDTO } from './dto/start-session.dto';
import { SessionsParamsDecorator } from 'src/@shared/decorators/sessions-params/sessions-params.decorator';

@Controller('chat-sessions')
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Post()
  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_create', 'attendant_chat_create'])
  async start(
    @Body() startSessionDto: StartSessionDTO,
    @AccountIdExtractor() accountId: number,
    @UserIdExtractorDecorator() userId: number,
  ) {
    return await this.sessionsService.startSession(
      startSessionDto,
      accountId,
      userId,
    );
  }

  @Get()
  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_chat_view'])
  @ApiOperation({
    summary: 'Lista todas as Sessões',
    description:
      'Lista todas as Sessões disponíveis da Account. Esta ação requer permissões de usuário com a role APP',
  })
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @IsAIResponderDecorator() isAIResponder: IsAIResponderQueryType,
    @SessionsParamsDecorator() sessionsParams: SessionsParamsQueryType,
    @UserIdExtractorDecorator() userId: number,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.sessionsService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      isAIResponder.isAIResponder,
      accountId,
      userId,
      sessionsParams.showAll,
      sessionsParams.showOnlyArchived,
      sessionsParams.showOnlyFinalized,
      sessionsParams.sessionTypeFilter,
      sessionsParams.sortOrder,
    );
  }

  @Get(':secureId')
  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_chat_view'])
  @ApiOperation({
    summary: 'Busca a sessão!',
    description:
      'Lista tudo sobre a sessão. Esta ação requer permissões de usuário com a role APP',
  })
  findOne(
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
  ) {
    return this.sessionsService.findOne(secureId, accountId);
  }

  @ApiOkResponse()
  @Put(':secureId')
  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_edit', 'attendant_chat_edit'])
  async update(
    @Param('secureId') secureId: string,
    @Body() updateSessionDto: UpdateSessionDto,
  ) {
    return await this.sessionsService.updateSession(secureId, updateSessionDto);
  }
}
