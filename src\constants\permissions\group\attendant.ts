import { v4 as uuidV4 } from 'uuid';

import { Permission } from 'src/resources/permissions/entities/permission.entity';

const attendantChatViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Chat_View',
  slug: 'attendant_chat_view',
  group: 'attendant',
  description: 'Usu<PERSON>rio pode ver as conversas',
});

const attendantChatCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Chat_Create',
  slug: 'attendant_chat_create',
  group: 'attendant',
  description: 'Usuário pode Interagir com a conversa',
});

const attendantChatEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Chat_Edit',
  slug: 'attendant_chat_edit',
  group: 'attendant',
  description: 'Usuário pode editar os dados do Cliente',
});

const attendantContactViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Contact_View',
  slug: 'attendant_contact_view',
  group: 'attendant',
  description: '<PERSON>u<PERSON>rio pode ver os contatos',
});

const attendantContactCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Contact_Create',
  slug: 'attendant_contact_create',
  group: 'attendant',
  description: 'Usuário pode criar um contato',
});

const attendantContactEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Contact_Edit',
  slug: 'attendant_contact_edit',
  group: 'attendant',
  description: 'Usuário pode editar um contato',
});

const attendantContactDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Contact_Delete',
  slug: 'attendant_contact_delete',
  group: 'attendant',
  description: 'Usuário pode apagar um contato',
});

const attendantDashboardViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Dashboard_View',
  slug: 'attendant_dashboard_view',
  group: 'attendant',
  description: 'Usuário pode ver o dashboard',
});

const attendantReportViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Report_View',
  slug: 'attendant_report_view',
  group: 'attendant',
  description: 'Usuário pode ver os relatórios',
});

const attendantConfigurationsViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Configuration_View',
  slug: 'attendant_configuration_view',
  group: 'attendant',
  description: 'Usuário pode ver as configurações',
});

const attendantWhatsAppViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WhatsApp_View',
  slug: 'attendant_whatsapp_view',
  group: 'attendant',
  description: 'Usuário pode ver os números de WhatsApp',
});

const attendantWhatsAppCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WhatsApp_Create',
  slug: 'attendant_whatsapp_create',
  group: 'attendant',
  description: 'Usuário pode criar um número de WhatsApp',
});

const attendantWhatsAppEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WhatsApp_Edit',
  slug: 'attendant_whatsapp_edit',
  group: 'attendant',
  description: 'Usuário pode criar um número de WhatsApp',
});

const attendantWhatsAppDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WhatsApp_Delete',
  slug: 'attendant_whatsapp_delete',
  group: 'attendant',
  description: 'Usuário pode apagar um número de WhatsApp',
});

const attendantWebChatViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WebChat_View',
  slug: 'attendant_webchat_view',
  group: 'attendant',
  description: 'Usuário pode ver os webchats',
});

const attendantWebChatCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WebChat_Create',
  slug: 'attendant_webchat_create',
  group: 'attendant',
  description: 'Usuário pode criar um webchat',
});

const attendantWebChatEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WebChat_Edit',
  slug: 'attendant_webchat_edit',
  group: 'attendant',
  description: 'Usuário pode editar um webchat',
});

const attendantWebChatDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_WebChat_Delete',
  slug: 'attendant_webchat_delete',
  group: 'attendant',
  description: 'Usuário pode apagar um webchat',
});

const attendantChatbotViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_ChatBot_View',
  slug: 'attendant_chatbot_view',
  group: 'attendant',
  description: 'Usuário pode ver os chatbots',
});

const attendantChatbotCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_ChatBot_Create',
  slug: 'attendant_chatbot_create',
  group: 'attendant',
  description: 'Usuário pode criar um chatbot',
});

const attendantChatbotEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_ChatBot_Edit',
  slug: 'attendant_chatbot_edit',
  group: 'attendant',
  description: 'Usuário pode editar um chatbot',
});

const attendantChatbotDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_ChatBot_Delete',
  slug: 'attendant_chatbot_delete',
  group: 'attendant',
  description: 'Usuário pode apagar um chatbot',
});

const attendantKnowledgeBaseCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Knowledge_Base_Create',
  slug: 'attendant_knowledge_base_create',
  group: 'attendant',
  description: 'Usuário inserir documentos na base de conhecimento',
});

const attendantKnowledgeBaseDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Knowledge_Base_Delete',
  slug: 'attendant_knowledge_base_delete',
  group: 'attendant',
  description: 'Usuário apagar documentos na base de conhecimento',
});

const attendantAttendantViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Attendant_View',
  slug: 'attendant_attendant_view',
  group: 'attendant',
  description: 'Usuário pode ver os atendentes',
});

const attendantAttendantCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Attendant_Create',
  slug: 'attendant_attendant_create',
  group: 'attendant',
  description: 'Usuário pode criar um atendente',
});

const attendantAttendantEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Attendant_Edit',
  slug: 'attendant_attendant_edit',
  group: 'attendant',
  description: 'Usuário pode editar um atendente',
});

const attendantAttendantDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Attendant_Attendant_Delete',
  slug: 'attendant_attendant_delete',
  group: 'attendant',
  description: 'Usuário pode apagar um atendente',
});

export const attendantPermissions: Permission[] = [
  attendantChatViewPermission,
  attendantChatCreatePermission,
  attendantChatEditPermission,
  attendantDashboardViewPermission,
  attendantReportViewPermission,
  attendantConfigurationsViewPermission,
  attendantWhatsAppViewPermission,
  attendantWhatsAppEditPermission,
  attendantWhatsAppCreatePermission,
  attendantWhatsAppDeletePermission,
  attendantWebChatViewPermission,
  attendantWebChatCreatePermission,
  attendantWebChatEditPermission,
  attendantWebChatDeletePermission,
  attendantChatbotViewPermission,
  attendantChatbotCreatePermission,
  attendantChatbotEditPermission,
  attendantChatbotDeletePermission,
  attendantKnowledgeBaseCreatePermission,
  attendantKnowledgeBaseDeletePermission,
  attendantAttendantViewPermission,
  attendantAttendantCreatePermission,
  attendantAttendantEditPermission,
  attendantAttendantDeletePermission,
  attendantContactViewPermission,
  attendantContactCreatePermission,
  attendantContactEditPermission,
  attendantContactDeletePermission,
];
