import { Prisma } from '@prisma/client';
import {
  SubscriptionStatus,
  SubscriptionType,
} from 'src/@shared/types/subscriptions';

export interface AccountSubscriptionDecoratorOutput {
  subscriptionStatus?: SubscriptionStatus;
  subscriptionType?: SubscriptionType;
  planId?: number;
  accountId?: number;
}

export type AccountSubscriptionWithPlanAndAccount =
  Prisma.SubscriptionsGetPayload<{
    include: {
      plan: {
        select: {
          secureId: true;
          name: true;
          isActive: true;
          createdAt: true;
          updatedAt: true;
        };
      };

      account: {
        select: {
          secureId: true;
          companyName: true;
          isActive: true;
          isDeleted: true;
          createdAt: true;
          updatedAt: true;
        };
      };
    };
  }>;
