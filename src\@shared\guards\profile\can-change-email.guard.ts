import {
  CanActivate,
  ConflictException,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o usuário pode alterar o seu email, verificando se o email não está sendo usado por outro usuário.
 */
@Injectable()
export class DoesUserCanChangeEmailGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;
    const body = context.switchToHttp().getRequest().body;
    if (!body.email) {
      return true;
    }

    const userWithSameEmail = await this.prismaService.users.findFirst({
      where: {
        email: { equals: body.email },
        secureId: { not: user.subject },
      },
    });
    if (userWithSameEmail) {
      throw new ConflictException(
        'Email já está sendo utilizado por outro usuário',
      );
    }

    return true;
  }
}
