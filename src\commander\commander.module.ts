import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';

import { DeployCommand } from './commands/deploy/deploy.command';
import { SyncUserRecordsCommand } from './commands/users/sync-records/sync-users.command';
import { SyncAccountsCommand } from './commands/accounts/sync-records/sync-accounts.command';
import { SyncRoleRecordsCommand } from './commands/roles/sync-records/sync-role-records.command';
import { SyncPermissionRecordsCommand } from './commands/permissions/sync-records/sync-permission-records.command';

import { SyncUsersRecordService } from './commands/users/sync-records/sync-users.service';
import { SyncAccountsService } from './commands/accounts/sync-records/sync-accounts.service';
import { SyncRoleRecordsService } from './commands/roles/sync-records/sync-role-records.service';
import { SyncPermissionRecordsService } from './commands/permissions/sync-records/sync-permission-records.service';

import { HashModule } from 'src/resources/auth/jwt/hash/hash.module';
import { FixMessagesCommand } from './commands/others/fix-messages/fix-messages.command';
import { FixMessagesService } from './commands/others/fix-messages/fix-messages.service';
import { FixLastUpdatedMessageService } from './commands/others/fix-last-updated-messages/fix-last-updated-messages.service';
import { FixLastUpdatedMessagesCommand } from './commands/others/fix-last-updated-messages/fix-last-updated-messages.command';
import { GenerateContactsCommand } from './commands/others/generate-contacts/generate-contacts.command';
import { GenerateContactsService } from './commands/others/generate-contacts/generate-contacts.service';
import { FixWppBugService } from './commands/others/fix-wpp-bug/fix-wpp-bug.service';
import { FixWppBugCommand } from './commands/others/fix-wpp-bug/fix-wpp-bug.command';
import { FixSubscriptionCommand } from './commands/others/fix-subscription/fix-subscription.command';
import { FixSubscriptionService } from './commands/others/fix-subscription/fix-subscription.service';
import { SubscribeFreemiumCommand } from './commands/others/subscribe-freemium/subscribe-freemium.command';
import { SubscribeFreemiumService } from './commands/others/subscribe-freemium/subscribe-freemium.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    PrismaModule,
    HashModule,
  ],

  controllers: [],

  providers: [
    DeployCommand,
    SyncRoleRecordsCommand,
    SyncRoleRecordsService,
    SyncPermissionRecordsCommand,
    SyncPermissionRecordsService,
    SyncAccountsCommand,
    SyncAccountsService,
    FixMessagesCommand,
    FixMessagesService,
    FixLastUpdatedMessageService,
    FixLastUpdatedMessagesCommand,
    SyncUserRecordsCommand,
    SyncUsersRecordService,
    GenerateContactsCommand,
    GenerateContactsService,
    FixWppBugService,
    FixWppBugCommand,
    FixSubscriptionCommand,
    FixSubscriptionService,
    SubscribeFreemiumCommand,
    SubscribeFreemiumService,
  ],
})
export class CommanderModule {}
