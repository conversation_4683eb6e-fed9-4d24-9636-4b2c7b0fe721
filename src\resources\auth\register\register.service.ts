import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { v4 as uuidV4 } from 'uuid';

import { HashService } from '../jwt/hash/hash.service';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { User } from 'src/resources/users/entities/user.entity';
import { Account } from 'src/resources/_BACKOFFICE/accounts/entities/account.entity';

import { UserMapper } from 'src/resources/users/mapper/user.mapper';
import { AccountMapper } from 'src/resources/_BACKOFFICE/accounts/mapper/account.mapper';

import { CreateAccountInputDto } from './dto/create-account-input.dto';
import { CreateAttendantInputDto } from './dto/create-attendant-input.dto';
import { CreateBackofficeInputDto } from './dto/create-backoffice-input.dto';
import { getBackofficeDependencyOutputMapper } from './mappers/get-backoffice-dependency-output.mapper';
import { CreateAccountWithPlanInputDto } from './dto/create-account-with-plan-input.dto';
import { SubscriptionStatus, SubscriptionType } from '@prisma/client';
import { LoginService } from '../login/login.service';

// TODO: Finalizar cadastro com dados de subscription e transaction
@Injectable()
export class RegisterService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly hashService: HashService,
    private readonly loginService: LoginService,
  ) {}

  async create(createAccountInputDto: CreateAccountInputDto) {
    let newAccountSecureId: string = '';
    let newUserSecureId: string = '';

    const emailAlreadyExists = await this.checkIfRecordExistsByEmail(
      createAccountInputDto.email,
    );
    if (emailAlreadyExists) {
      throw new ConflictException(
        'Já existe um usuário com esse e-mail cadastrado',
      );
    }

    const companyNameAlreadyExists =
      await this.checkIfRecordExistsByCompanyName(
        createAccountInputDto.companyName,
      );
    if (companyNameAlreadyExists) {
      throw new ConflictException(
        'Essa empresa já possui uma conta cadastrada',
      );
    }

    const cpfAlreadyExists = await this.checkIfRecordExistsByCPF(
      createAccountInputDto.cpf,
    );
    if (cpfAlreadyExists) {
      throw new ConflictException('Já existe um usuário com esse CPF');
    }

    const partnerRoleModel = await this.prismaService.roles.findFirst({
      where: {
        slug: 'APP',
      },
    });

    const partnerPermissionsModel =
      await this.prismaService.permissions.findMany({
        where: {
          group: 'app',
        },
      });

    const account = new Account({
      companyName: createAccountInputDto.companyName,
      isActive: true,
      isDeleted: false,
    });
    const accountModel = AccountMapper.toModel(account);

    const user = new User({
      cpf: createAccountInputDto.cpf.replace(/\D/g, ''),
      email: createAccountInputDto.email,
      name: createAccountInputDto.name,
      password: await this.hashService.generate(createAccountInputDto.password),
      cellPhone: createAccountInputDto?.cellPhone
        ? createAccountInputDto.cellPhone.replace(/\D/g, '')
        : undefined,
      isActive: true,
      isDeleted: false,
    });
    const userModel = UserMapper.fromEntityToModel(user);

    await this.prismaService.$transaction(async (prisma) => {
      try {
        const createdAccount = await prisma.accounts.create({
          data: accountModel,
        });

        const createdUser = await prisma.users.create({
          data: userModel,
        });

        newAccountSecureId = createdAccount.secureId;
        newUserSecureId = createdUser.secureId;

        const userAccount = await prisma.usersAccounts.create({
          data: {
            secureId: uuidV4(),
            isOwner: true,

            accountId: createdAccount.id,
            userId: createdUser.id,
            roleId: partnerRoleModel.id,

            isActive: true,
            isDeleted: false,
          },
        });

        await prisma.accountsPermissions.createMany({
          data: partnerPermissionsModel.map((permission) => ({
            secureId: uuidV4(),
            userAccountId: userAccount.id,
            permissionId: permission.id,
          })),
        });

        const freePlan = await prisma.plans.findFirst({
          where: {
            slug: 'freemium',
            isActive: true,
          },
        });

        if (!freePlan) {
          throw new InternalServerErrorException(
            'Plano gratuito não encontrado. Contate o administrador.',
          );
        }

        await prisma.subscriptions.create({
          data: {
            secureId: uuidV4(),
            accountId: createdAccount.id,
            planId: freePlan.id,
            remainingSessions: freePlan.iaMessagesLimit,
            cycle: 1,
            status: SubscriptionStatus.active,
            type: SubscriptionType.free,
            gatewaySubscriptionId: null,
            startsAt: new Date(),
            endsAt: new Date(
              new Date().setFullYear(new Date().getFullYear() + 10),
            ),
            trialEndsAt: new Date(
              new Date().setDate(new Date().getDate() + freePlan.trialDays),
            ),
            isActive: true,
          },
        });
      } catch (error) {
        console.log(error);

        throw new InternalServerErrorException(
          'Erro ao criar conta, Contate o administrador',
        );
      }
    });

    return this.loginService.login({
      email: createAccountInputDto.email,
      password: createAccountInputDto.password,
    });

    // return { newUserSecureId, newAccountSecureId };
  }

  /**
   *
   * @description Cria um atendente com base do cliente. Caso o cliente passe um atendente com o email e cpf jác cadastrados ele vai adicionar a account do cliente para o atendente. No caso do atendente não estar cadastrado no sistema será gerado uma conta para ele, essa conta será uma conta de atendente. Ou seja ele não será um owner de sua própria account.
   *
   * @param createAttendantDto Object => { name: string, email: string, cpf: string, cellPhone?: string }
   * @param accountId number => Extraído do token
   */
  async createAttendant(
    createAttendantDto: CreateAttendantInputDto,
    accountId: number,
  ) {
    const formattedCpf = createAttendantDto.cpf.replace(/\D/g, '');

    const attendantModel = await this.prismaService.users.findFirst({
      where: {
        email: { equals: createAttendantDto.email },
        cpf: { equals: formattedCpf },
      },

      select: {
        id: true,
        usersAccounts: {
          where: {
            isDeleted: false,
          },
          select: {
            account: { select: { id: true } },
          },
        },
      },
    });

    // NOTE: Aqui é adicionado a conta do cliente para o atendente Caso ele JÁ esteja cadastrado
    if (attendantModel) {
      const attendantIncludeAccountsId = attendantModel?.usersAccounts.map(
        (userAccount) => userAccount.account.id,
      );

      if (attendantIncludeAccountsId.includes(accountId)) {
        throw new ConflictException(
          'Esse atendente já está cadastrado na sua conta',
        );
      }

      const clientAccountModel = await this.prismaService.accounts.findFirst({
        where: { id: { equals: accountId } },
      });
      if (!clientAccountModel) {
        throw new NotFoundException('Conta do cliente não encontrada.');
      }

      const attendantRoleModel = await this.prismaService.roles.findFirst({
        where: { slug: 'ATTENDANT' },
        select: { id: true },
      });

      if (!attendantRoleModel) {
        throw new NotFoundException(
          'Role de atendente não encontrada. Contate o suporte.',
        );
      }

      const attendantPermissions =
        await this.prismaService.permissions.findMany({
          where: { group: 'attendant' },
          select: { id: true },
        });
      if (!attendantPermissions) {
        throw new NotFoundException(
          'Permissões de atendente não encontradas. Contate o suporte.',
        );
      }

      await this.prismaService.$transaction(async (prisma) => {
        const attendantAccount = await prisma.usersAccounts.create({
          data: {
            secureId: uuidV4(),
            isOwner: false,

            accountId: clientAccountModel.id,
            participatesInRotation: createAttendantDto.participatesInRotation,
            userId: attendantModel.id,
            roleId: attendantRoleModel.id,

            isActive: true,
            isDeleted: false,
          },
        });

        const permissions = attendantPermissions.map((permission) => {
          return {
            secureId: uuidV4(),
            userAccountId: attendantAccount.id,
            permissionId: permission.id,
          };
        });

        await prisma.accountsPermissions.createMany({
          data: permissions,
        });
      });

      return;
    }

    // NOTE: Essa exception acontece quando os dados passados não conferem com os dados do sistema. Ou seja pessoas diferente.
    // Nesse caso é gerado um erro, pois não é possível adicionar um usuário com email duplicado. Ai o usuário deve passar o cpf correto ou trocar o email do atendente.
    // O mesmo é valido para o CPF.
    const emailAlreadyExists = await this.checkIfRecordExistsByEmail(
      createAttendantDto.email,
    );
    if (emailAlreadyExists) {
      throw new ConflictException(
        'Já existe um usuário com esse e-mail cadastrado na sua conta',
      );
    }

    const cpfAlreadyExists = await this.checkIfRecordExistsByCPF(formattedCpf);
    if (cpfAlreadyExists) {
      throw new ConflictException('Já existe um usuário com esse CPF');
    }

    const newAttendant = new User({
      cpf: formattedCpf,
      email: createAttendantDto.email,
      name: createAttendantDto.name,
      password: await this.hashService.generate(createAttendantDto.password),
      cellPhone: createAttendantDto?.cellPhone
        ? createAttendantDto.cellPhone.replace(/\D/g, '')
        : undefined,
      isActive: true,
      isDeleted: false,
    });
    const newAttendantModel = UserMapper.fromEntityToModel(newAttendant);

    const attendantRoleModel = await this.prismaService.roles.findFirst({
      where: { slug: 'ATTENDANT' },
      select: { id: true },
    });
    if (!attendantRoleModel) {
      throw new NotFoundException(
        'Role de atendente não encontrada. Contate o suporte.',
      );
    }

    await this.prismaService.$transaction(async (prisma) => {
      try {
        const createdAttendant = await prisma.users.create({
          data: newAttendantModel,
        });

        const attendantAccount = await prisma.usersAccounts.create({
          data: {
            secureId: uuidV4(),
            isOwner: false,

            accountId,
            participatesInRotation: createAttendantDto.participatesInRotation,
            userId: createdAttendant.id,
            roleId: attendantRoleModel.id,

            isActive: true,
            isDeleted: false,
          },
        });

        if (createAttendantDto.hasAllPermissions) {
          const permissionsModel =
            await this.prismaService.permissions.findMany({
              select: { id: true },
              where: { group: 'app' },
            });

          if (!permissionsModel) {
            throw new NotFoundException(
              'Permissões de atendentes não encontradas. Contate o suporte.',
            );
          }

          const permissions = permissionsModel.map((permission) => {
            return {
              secureId: uuidV4(),
              userAccountId: attendantAccount.id,
              permissionId: permission.id,
            };
          });

          await prisma.accountsPermissions.createMany({
            data: permissions,
          });

          return;
        }

        if (
          createAttendantDto.permissionsSecureIds &&
          createAttendantDto.permissionsSecureIds.length > 0
        ) {
          const permissionModel = await this.prismaService.permissions.findMany(
            {
              where: {
                secureId: {
                  in: createAttendantDto.permissionsSecureIds,
                },
              },
              select: { id: true },
            },
          );

          if (!permissionModel) {
            throw new NotFoundException(
              'Permissões de atendente não encontradas. Contate o suporte.',
            );
          }

          const permissions = permissionModel.map((permission) => {
            return {
              secureId: uuidV4(),
              userAccountId: attendantAccount.id,
              permissionId: permission.id,
            };
          });

          await prisma.accountsPermissions.createMany({
            data: permissions,
          });
        }
      } catch (error) {
        console.log(error);

        throw new InternalServerErrorException(
          'Erro ao criar conta de atendente, Contate o suporte',
        );
      }
    });
  }

  async createBackoffice(
    createBackofficeInputDto: CreateBackofficeInputDto,
    accountId: number,
  ) {
    const mainAccountModel = await this.prismaService.accounts.findFirst({
      where: { id: { equals: accountId } },
      select: { id: true },
    });

    const formattedCPF = createBackofficeInputDto.cpf.replace(/\D/g, '');

    const newBackoffice = new User({
      cpf: formattedCPF,
      email: createBackofficeInputDto.email,
      name: createBackofficeInputDto.name,
      password: await this.hashService.generate(
        createBackofficeInputDto.password,
      ),
      isActive: true,
      isDeleted: false,
    });
    const newBackofficeModel = UserMapper.fromEntityToModel(newBackoffice);

    const backofficeRoleModel = await this.prismaService.roles.findFirst({
      where: { slug: 'BACKOFFICE' },
      select: { id: true },
    });
    if (!backofficeRoleModel) {
      throw new NotFoundException(
        'Role de backoffice não encontrada. Contate o suporte.',
      );
    }

    await this.prismaService.$transaction(async (prisma) => {
      try {
        const createdBackoffice = await prisma.users.create({
          data: newBackofficeModel,
        });

        const createdBackofficeUserAccount = await prisma.usersAccounts.create({
          data: {
            secureId: uuidV4(),
            isOwner: false,

            accountId: mainAccountModel.id,
            userId: createdBackoffice.id,
            roleId: backofficeRoleModel.id,

            isActive: true,
            isDeleted: false,
          },
        });

        // NOTE: Nessa parte o código lida com as permissions mas apenas para caso de hasSuperPowers = true
        if (createBackofficeInputDto.hasAllPermissions) {
          const permissionsModel =
            await this.prismaService.permissions.findMany({
              where: {
                group: 'backoffice',
                slug: {
                  in: [
                    'backoffice_view',
                    'backoffice_create',
                    'backoffice_delete',
                    'backoffice_edit',
                  ],
                },
              },
              select: { id: true },
            });
          if (!permissionsModel) {
            throw new NotFoundException(
              'Permissões de backoffice não encontradas. Contate o suporte.',
            );
          }

          const permissions = permissionsModel.map((permission) => {
            return {
              secureId: uuidV4(),
              userAccountId: createdBackofficeUserAccount.id,
              permissionId: permission.id,
            };
          });

          await prisma.accountsPermissions.createMany({
            data: permissions,
          });

          return;
        }

        // NOTE: Nessa parte o código lida com as permissões do backoffice, Caso não tenham sido passadas ele não as cria.
        if (
          createBackofficeInputDto.permissionsSecureIds &&
          createBackofficeInputDto.permissionsSecureIds.length > 0
        ) {
          const permissionsModel =
            await this.prismaService.permissions.findMany({
              where: {
                secureId: {
                  in: createBackofficeInputDto.permissionsSecureIds,
                },
              },
              select: { id: true },
            });
          if (!permissionsModel) {
            throw new NotFoundException(
              'Permissões de backoffice não encontradas. Contate o suporte.',
            );
          }

          const permissions = permissionsModel.map((permission) => {
            return {
              secureId: uuidV4(),
              userAccountId: createdBackofficeUserAccount.id,
              permissionId: permission.id,
            };
          });

          await prisma.accountsPermissions.createMany({
            data: permissions,
          });
        }
      } catch (error) {
        console.log(error);

        throw new InternalServerErrorException(
          'Erro ao criar conta de backoffice, Contate o suporte',
        );
      }
    });
  }

  async createAccountWithPlan(
    createAccountWithPlanInputDto: CreateAccountWithPlanInputDto,
  ) {
    const subscriptionSecureId = uuidV4();

    const selectedPlan = await this.prismaService.plans.findUnique({
      where: {
        secureId: createAccountWithPlanInputDto.planSecureId,
        isActive: true,
      },
    });

    if (!selectedPlan) {
      throw new NotFoundException('Plano selecionado não encontrado.');
    }

    const partnerRoleModel = await this.prismaService.roles.findFirst({
      where: {
        slug: 'APP',
      },
    });

    const partnerPermissionsModel =
      await this.prismaService.permissions.findMany({
        where: {
          group: 'app',
        },
      });

    const account = new Account({
      companyName: createAccountWithPlanInputDto.companyName,
      isActive: true,
      isDeleted: false,
    });
    const accountModel = AccountMapper.toModel(account);

    const user = new User({
      cpf: createAccountWithPlanInputDto.cpf.replace(/\D/g, ''),
      name: createAccountWithPlanInputDto.name,
      email: createAccountWithPlanInputDto.email,
      password: await this.hashService.generate(
        createAccountWithPlanInputDto.password,
      ),
      cellPhone: createAccountWithPlanInputDto?.cellPhone
        ? createAccountWithPlanInputDto.cellPhone.replace(/\D/g, '')
        : undefined,
      isActive: true,
      isDeleted: false,
    });
    const userModel = UserMapper.fromEntityToModel(user);

    await this.prismaService.$transaction(async (prisma) => {
      try {
        const createdAccount = await prisma.accounts.create({
          data: accountModel,
        });

        const createdUser = await prisma.users.create({
          data: userModel,
        });

        const userAccount = await prisma.usersAccounts.create({
          data: {
            secureId: uuidV4(),
            isOwner: true,

            accountId: createdAccount.id,
            userId: createdUser.id,
            roleId: partnerRoleModel.id,

            isActive: true,
            isDeleted: false,
          },
        });

        await prisma.accountsPermissions.createMany({
          data: partnerPermissionsModel.map((permission) => ({
            secureId: uuidV4(),
            userAccountId: userAccount.id,
            permissionId: permission.id,
          })),
        });

        await prisma.subscriptions.create({
          data: {
            secureId: subscriptionSecureId,
            cycle: 1,
            status: SubscriptionStatus.active,
            type: SubscriptionType.sponsored,
            plan: { connect: { id: selectedPlan.id } },
            account: { connect: { id: createdAccount.id } },
            remainingSessions: selectedPlan.iaMessagesLimit,
            gatewaySubscriptionId: null,
            startsAt: new Date(),
            endsAt: new Date(
              new Date().setFullYear(new Date().getFullYear() + 10),
            ),
            trialEndsAt:
              selectedPlan.trialDays > 0
                ? new Date(
                    new Date().setDate(
                      new Date().getDate() + selectedPlan.trialDays,
                    ),
                  )
                : null,
            isActive: true,
          },
        });
      } catch (error) {
        console.log(error);

        throw new InternalServerErrorException(
          'Erro ao criar conta, Contate o suporte',
        );
      }
    });
  }

  async update(secureId: string, updateAccountInputDto: CreateAccountInputDto) {
    const existingUser = await this.prismaService.users.findUnique({
      where: { secureId },
      include: {
        usersAccounts: {
          include: { account: true },
        },
      },
    });

    if (!existingUser) {
      throw new NotFoundException('Usuário não encontrado.');
    }

    const userAccount = existingUser.usersAccounts[0];
    if (!userAccount || !userAccount.account) {
      throw new NotFoundException('Conta associada ao usuário não encontrada.');
    }

    const { account } = userAccount;

    const updatedAccountData = {
      companyName: updateAccountInputDto.companyName ?? account.companyName,
    };

    const updatedUserData = {
      name: updateAccountInputDto.name ?? existingUser.name,
      email: updateAccountInputDto.email ?? existingUser.email,
      cpf: updateAccountInputDto.cpf
        ? updateAccountInputDto.cpf.replace(/\D/g, '')
        : existingUser.cpf,
      cellPhone: updateAccountInputDto.cellPhone
        ? updateAccountInputDto.cellPhone.replace(/\D/g, '')
        : existingUser.cellPhone,
      password: updateAccountInputDto.password
        ? await this.hashService.generate(updateAccountInputDto.password)
        : existingUser.password,
    };

    await this.prismaService.$transaction(async (prisma) => {
      try {
        await prisma.accounts.update({
          where: { id: account.id },
          data: updatedAccountData,
        });

        await prisma.users.update({
          where: { secureId },
          data: updatedUserData,
        });
      } catch (error) {
        console.error(error);
        throw new InternalServerErrorException(
          'Erro ao atualizar conta e usuário. Contate o administrador.',
        );
      }
    });

    return { message: 'Conta e usuário atualizados com sucesso.' };
  }

  async updateAttendant(
    userSecureId: string,
    updateAttendantDto:  CreateAttendantInputDto,
    accountId: number,
  ) {
    const userModel = await this.prismaService.users.findFirst({
      where: {
        secureId: userSecureId,
        isDeleted: false,
      },
      select: { id: true, email: true, cpf: true }
    });

    if (!userModel) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const userAccountModel = await this.prismaService.usersAccounts.findFirst({
      where: {
        userId: userModel.id,
        accountId: accountId,
        isDeleted: false,
      },
      select: { id: true, secureId: true }
    });

    if (!userAccountModel) {
      throw new NotFoundException('Usuário não está associado a esta conta');
    }

    // Atualizar dados do usuário
    await this.prismaService.users.update({
      where: { id: userModel.id },
      data: {
        name: updateAttendantDto.name,
        email: updateAttendantDto.email,
        cpf: updateAttendantDto.cpf ? updateAttendantDto.cpf.replace(/\D/g, '') : undefined,
        cellPhone: updateAttendantDto.cellPhone ? updateAttendantDto.cellPhone.replace(/\D/g, '') : undefined,
        password: updateAttendantDto.password ? await this.hashService.generate(updateAttendantDto.password) : undefined,
      }
    });

    // Atualizar permissões se necessário
    if (updateAttendantDto.hasAllPermissions || 
        (updateAttendantDto.permissionsSecureIds && updateAttendantDto.permissionsSecureIds.length > 0)) {
      
      // Remover permissões existentes
      await this.prismaService.accountsPermissions.deleteMany({
        where: { userAccountId: userAccountModel.id }
      });

      // Adicionar novas permissões
      if (updateAttendantDto.hasAllPermissions) {
        const permissionsModel = await this.prismaService.permissions.findMany({
          where: { group: 'app' },
          select: { id: true },
        });

        const permissions = permissionsModel.map(permission => ({
          secureId: uuidV4(),
          userAccountId: userAccountModel.id,
          permissionId: permission.id,
        }));

        await this.prismaService.accountsPermissions.createMany({
          data: permissions,
        });
      } else if (updateAttendantDto.permissionsSecureIds) {
        const permissionModel = await this.prismaService.permissions.findMany({
          where: {
            secureId: {
              in: updateAttendantDto.permissionsSecureIds,
            },
          },
          select: { id: true },
        });

        const permissions = permissionModel.map(permission => ({
          secureId: uuidV4(),
          userAccountId: userAccountModel.id,
          permissionId: permission.id,
        }));

        await this.prismaService.accountsPermissions.createMany({
          data: permissions,
        });
      }
    }

    return { message: 'Usuário atualizado com sucesso' };
  }

  async getBackofficeDependencies() {
    const permissionsModel = await this.prismaService.permissions.findMany({
      where: {
        group: 'backoffice',
        slug: {
          notIn: [
            'backoffice_view',
            'backoffice_create',
            'backoffice_delete',
            'backoffice_edit',
          ],
        },
      },
    });

    const permissionsOutput =
      getBackofficeDependencyOutputMapper.fromCustomDtoToGetBackofficeDependencyOutputDto(
        { permissionsModel: permissionsModel },
      );
    return permissionsOutput;
  }

  async getAttendantDependencies() {
    const permissionsModel = await this.prismaService.permissions.findMany({
      where: { group: 'attendant' },
    });

    const permissionsOutput =
      getBackofficeDependencyOutputMapper.fromCustomDtoToGetBackofficeDependencyOutputDto(
        { permissionsModel: permissionsModel },
      );

    return permissionsOutput;
  }

  private async checkIfRecordExistsByEmail(email: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM users WHERE email = ?) AS has;`,
      email,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  private async checkIfRecordExistsByCompanyName(
    companyName: string,
  ): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM accounts WHERE company_name = ?) AS has;`,
      companyName,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  private async checkIfRecordExistsByCPF(cpf: string): Promise<boolean> {
    cpf = cpf.replace(/\D/g, '');
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM users WHERE cpf = ?) AS has;`,
      cpf,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  async getAccountIdBySecureId(accountSecureId: string): Promise<number> {
    const account = await this.prismaService.accounts.findFirst({
      where: {
        secureId: accountSecureId,
        isActive: true,
        isDeleted: false,
      },
      select: { id: true },
    });

    if (!account) {
      throw new NotFoundException('Conta não encontrada ou inativa');
    }

    return account.id;
  }
}
