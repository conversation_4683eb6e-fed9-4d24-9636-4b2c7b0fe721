import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { FixMessagesService } from './fix-messages.service';

@Command({
  name: 'fix-messages',
  description:
    'Arruma as mensagens do sistema preenchendo o campo messageDirection com o valor correto para cada mensagem, resolvendo o problema das mensagens no frontend',
})
@Injectable()
export class FixMessagesCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(private readonly fixMessagesService: FixMessagesService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    await this.fixMessagesService
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
