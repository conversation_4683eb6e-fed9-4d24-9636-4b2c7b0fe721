import { Contacts } from '@prisma/client';
import { ContactOutputDto } from '../dto/contact-output.dto';

export class GetOneContactFactory {
  static createFromModelToOutputDto(model: Contacts): ContactOutputDto {
    return {
      secureId: model.secureId,
      name: model.name,
      email: model.email,
      phone: model.phone,
      document: model.document,
      isActive: model.isActive,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
