import { Module } from '@nestjs/common';
import { ForgotService } from './forgot.service';
import { ForgotController } from './forgot.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { HashModule } from '../jwt/hash/hash.module';
import { TokenModule } from '../jwt/token/token.module';
import { JwtStrategyModule } from '../jwt/strategy/hash.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import loginEnvs from '../login/env.config';
import { OneSignalService } from 'src/third-party/one-signal/one-signal.service';
import { PayloadService } from '../jwt/payload/payload.service';
import { NotificationModule } from 'src/resources/notification/notification.module';

@Module({
  imports: [
    PrismaModule,
    HashModule,
    TokenModule,
    JwtStrategyModule,
    JwtModule.registerAsync({
      imports: [ConfigModule.forFeature(loginEnvs)],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: { expiresIn: '10s' },
      }),
      inject: [ConfigService],
    }),
    NotificationModule,
  ],
  controllers: [ForgotController],
  providers: [ForgotService, PayloadService],
})
export class ForgotModule {}
