import { Prisma } from '@prisma/client';

export type UserWithAccountAndAccountPermissionsModel = Prisma.UsersGetPayload<{
  select: {
    secureId: true;
    name: true;
    email: true;
    cpf: true;

    createdAt: true;
    updatedAt: true;

    usersAccounts: {
      select: {
        isActive: true;
        role: {
          select: {
            slug: true;
          }
        }
        accountsPermissions: {
          select: {
            permission: {
              select: {
                secureId: true;
                slug: true;
              };
            };
          };
        };
      };
    };
  };
}>;

export type UserWithAccount = Prisma.UsersGetPayload<{
  select: {
    secureId: true;
    name: true;
    email: true;
    cpf: true;

    createdAt: true;
    updatedAt: true;

    usersAccounts: {
      select: {
        isActive: true;
        role: {
          select: {
            slug: true;
          }
        }
      };
    };
  };
}>;
