/*
  Warnings:

  - The values [Account View,Account Create,Account Edit,Account Delete,Evo Integration View,Evo Integration Create,Evo Integration Edit,Evo Integration Delete,Attendant View,Attendant Create,Attendant Edit,Attendant Delete,Chat View,Chat Create,Dashboard View,Configuration View,Configuration Create,Configuration Edit,Configuration Delete,Configuration WhatsApp View,Configuration WhatsApp Create,Configuration WhatsApp Edit,Configuration WhatsApp Delete,Configuration WebChat View,Configuration WebChat Create,Configuration WebChat Edit,Configuration WebChat Delete,Configuration ChatBot View,Configuration ChatBot Create,Configuration ChatBot Edit,Configuration ChatBot Delete,Configuration Attendant View,Configuration Attendant Create,Configuration Attendant Edit,Configuration Attendant Delete,User View,User Create,User Edit,User Delete] on the enum `permissions_name` will be removed. If these variants are still used in the database, this will fail.
  - The values [account_view,account_create,account_edit,account_delete,evo_integration_view,evo_integration_create,evo_integration_edit,evo_integration_delete,attendant_view,attendant_create,attendant_edit,attendant_delete,chat_view,chat_create,dashboard_view,configuration_view,configuration_create,configuration_edit,configuration_delete,configuration_whatsapp_view,configuration_whatsapp_create,configuration_whatsapp_edit,configuration_whatsapp_delete,configuration_webchat_view,configuration_webchat_create,configuration_webchat_edit,configuration_webchat_delete,configuration_chatbot_view,configuration_chatbot_create,configuration_chatbot_edit,configuration_chatbot_delete,configuration_attendant_view,configuration_attendant_create,configuration_attendant_edit,configuration_attendant_delete,user_view,user_create,user_edit,user_delete] on the enum `permissions_slug` will be removed. If these variants are still used in the database, this will fail.
  - The values [account,evo_integration,chat,dashboard,configuration,configuration_whatsapp,configuration_webchat,configuration_chatbot,configuration_attendant,user] on the enum `permissions_group` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterTable
ALTER TABLE `permissions` MODIFY `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete', 'Attendant Chat View', 'Attendant Chat Create', 'Attendant Chat Edit', 'Attendant Dashboard View', 'Attendant Report View', 'Attendant Configuration View', 'Attendant WhatsApp View', 'Attendant WhatsApp Create', 'Attendant WhatsApp Delete', 'Attendant WebChat View', 'Attendant WebChat Create', 'Attendant WebChat Edit', 'Attendant WebChat Delete', 'Attendant ChatBot View', 'Attendant ChatBot Create', 'Attendant ChatBot Edit', 'Attendant ChatBot Delete', 'Attendant Knowledge Base Create', 'Attendant Knowledge Base Delete', 'Attendant Attendant View', 'Attendant Attendant Create', 'Attendant Attendant Edit', 'Attendant Attendant Delete', 'Backoffice Dashboard View', 'Backoffice Account View', 'Backoffice Account Edit', 'Backoffice Plans View', 'Backoffice Plans Create', 'Backoffice Plans Delete', 'Backoffice Plans Edit', 'Backoffice User View', 'Backoffice User Create', 'Backoffice User Edit', 'Backoffice User Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete', 'attendant_chat_view', 'attendant_chat_create', 'attendant_chat_edit', 'attendant_dashboard_view', 'attendant_report_view', 'attendant_configuration_view', 'attendant_whatsapp_view', 'attendant_whatsapp_create', 'attendant_whatsapp_delete', 'attendant_webchat_view', 'attendant_webchat_create', 'attendant_webchat_edit', 'attendant_webchat_delete', 'attendant_chatbot_view', 'attendant_chatbot_create', 'attendant_chatbot_edit', 'attendant_chatbot_delete', 'attendant_knowledge_base_create', 'attendant_knowledge_base_delete', 'attendant_attendant_view', 'attendant_attendant_create', 'attendant_attendant_edit', 'attendant_attendant_delete', 'backoffice_dashboard_view', 'backoffice_account_view', 'backoffice_account_edit', 'backoffice_plans_view', 'backoffice_plans_create', 'backoffice_plans_edit', 'backoffice_plans_delete', 'backoffice_user_view', 'backoffice_user_create', 'backoffice_user_edit', 'backoffice_user_delete') NOT NULL,
    MODIFY `group` ENUM('backoffice', 'app', 'attendant') NOT NULL;
