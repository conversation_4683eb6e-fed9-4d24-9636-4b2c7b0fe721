import { IterableReadableStream } from '@langchain/core/dist/utils/stream';
import storageEnvs from '../env.config';
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai';
import { Inject } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { Document } from '@langchain/core/documents';
import { Annotation, StateGraph } from '@langchain/langgraph';
import { promptTemplate, promptTemplateWpp } from './prompt';
import { CustomQdrant } from './custom-qdrant';
import { AIMessageChunk } from '@langchain/core/messages';
import { ChatPromptTemplate } from '@langchain/core/prompts';

export class MessageChainService {
  private openAImodel: ChatOpenAI;

  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
  ) {}

  async streamMessageWithGraph({
    collectionName,
    messagesHistory,
    question,
    temperature,
    generatedPrompt,
    alternativeApiKey,
  }: MessageChainStreamInputDTO): Promise<{
    stream: Promise<IterableReadableStream<any>>;
    usage: Promise<{ inputTokens: number; outputTokens: number }>;
  }> {
    const currentApiKey = !!alternativeApiKey
      ? alternativeApiKey
      : this.storageConfig.openApiKey;

    this.openAImodel = new ChatOpenAI({
      model: 'gpt-4.1-mini-2025-04-14',
      temperature: 0,
      openAIApiKey: currentApiKey,
    });

    let inputTokens = 0;
    let outputTokens = 0;

    this.openAImodel.temperature = temperature / 100;

    const qdrant = new CustomQdrant(
      new OpenAIEmbeddings({
        openAIApiKey: currentApiKey,
        model: 'text-embedding-3-small',
      }),
      this.storageConfig.qdrantBaseUrl,
      this.storageConfig.qdrantApiKey,
      this.storageConfig.qdrantBasePort,
      collectionName,
    );

    const InputStateAnnotation = Annotation.Root({
      question: Annotation<string>,
    });

    const StateAnnotation = Annotation.Root({
      messagesHistory: Annotation<string[]>,
      question: Annotation<string>,
      context: Annotation<Document[]>,
      answer: Annotation<AIMessageChunk>,
    });

    const retrieve = async (state: typeof InputStateAnnotation.State) => {
      const retrievedDocs = await qdrant.similaritySearch(state.question, 3);
      return { context: retrievedDocs };
    };

    const generate = async (state: typeof StateAnnotation.State) => {
      const docsContent = state.context.length
        ? state.context.map((doc) => doc.pageContent).join('\n')
        : '';

      let prompt: ChatPromptTemplate<any, any>;
      if (generatedPrompt) {
        prompt = ChatPromptTemplate.fromMessages([
          ['system', generatedPrompt],
          ['human', '{question}'],
        ]);
      } else {
        prompt = promptTemplate;
      }

      const messages = await prompt.invoke({
        question: state.question,
        messagesHistory: state.messagesHistory,
        context: docsContent || 'Nenhum contexto disponível',
      });

      const response = await this.openAImodel.invoke(messages);

      inputTokens += response.usage_metadata.input_tokens;
      outputTokens += response.usage_metadata.output_tokens;

      return {
        answer: response,
      };
    };

    const graph = new StateGraph(StateAnnotation)
      .addNode('retrieve', retrieve)
      .addNode('generate', generate)
      .addEdge('__start__', 'retrieve')
      .addEdge('retrieve', 'generate')
      .addEdge('generate', '__end__')
      .compile();

    // Criar uma Promise que será resolvida quando o stream terminar
    const usagePromise = new Promise<{
      inputTokens: number;
      outputTokens: number;
    }>((resolve) => {
      graph
        .stream({ question }, { streamMode: 'messages' })
        .then(async (stream) => {
          // Consumir o stream para garantir que todas as operações sejam concluídas
          for await (const _ of stream) {
            // Apenas consumir o stream
          }
          // Resolver com os valores finais dos tokens
          resolve({ inputTokens, outputTokens });
        });
    });

    return {
      stream: graph.stream(
        { question, messagesHistory },
        { streamMode: 'messages' },
      ),
      usage: usagePromise,
    };
  }

  async streamMessage({
    collectionName,
    messagesHistory,
    question,
    temperature,
    generatedPrompt,
    alternativeApiKey,
  }: MessageChainStreamInputDTO): Promise<
    IterableReadableStream<AIMessageChunk>
  > {
    const currentApiKey = !!alternativeApiKey
      ? alternativeApiKey
      : this.storageConfig.openApiKey;

    this.openAImodel = new ChatOpenAI({
      model: 'gpt-4.1-mini-2025-04-14',
      temperature: temperature / 100,
      openAIApiKey: currentApiKey,
    });

    const qdrant = new CustomQdrant(
      new OpenAIEmbeddings({
        openAIApiKey: currentApiKey,
        model: 'text-embedding-3-small',
      }),
      this.storageConfig.qdrantBaseUrl,
      this.storageConfig.qdrantApiKey,
      this.storageConfig.qdrantBasePort,
      collectionName,
    );

    const retrievedDocs = await qdrant.similaritySearch(question, 3);

    const docsContent = retrievedDocs
      ? retrievedDocs.map((doc) => doc.pageContent).join('\n')
      : '';

    let prompt: ChatPromptTemplate<any, any>;
    if (generatedPrompt) {
      prompt = ChatPromptTemplate.fromMessages([
        ['system', generatedPrompt],
        ['human', '{question}'],
      ]);
    } else {
      prompt = promptTemplate;
    }

    const messages = await prompt.invoke({
      question: question,
      messagesHistory: messagesHistory,
      context: docsContent || 'Nenhum contexto disponível',
    });

    return await this.openAImodel.stream(messages);
  }

  async streamGenericSystemPrompt({
    alternativeApiKey,
    genericPrompt,
    question,
  }: {
    alternativeApiKey?: string;
    genericPrompt: string;
    question?: string;
  }) {
    const currentApiKey = !!alternativeApiKey
      ? alternativeApiKey
      : this.storageConfig.openApiKey;

    this.openAImodel = new ChatOpenAI({
      model: 'gpt-4.1-mini-2025-04-14',
      temperature: 1,
      openAIApiKey: currentApiKey,
    });

    const prompt = ChatPromptTemplate.fromTemplate('{genericPrompt}');

    const messages = await prompt.invoke({
      genericPrompt,
    });

    return await this.openAImodel.stream(messages);
  }

  async messageChainWpp(
    question: string,
    collectionName: string,
    customerName: string,
    messageHistory?: string[],
  ) {
    const qdrant = new CustomQdrant(
      new OpenAIEmbeddings({
        openAIApiKey: this.storageConfig.openApiKey,
        model: 'text-embedding-3-small',
      }),
      this.storageConfig.qdrantBaseUrl,
      this.storageConfig.qdrantApiKey,
      this.storageConfig.qdrantBasePort,
      collectionName,
    );

    const InputStateAnnotation = Annotation.Root({
      question: Annotation<string>,
    });

    const StateAnnotation = Annotation.Root({
      messageHistory: Annotation<string[]>,
      customerName: Annotation<string>,
      question: Annotation<string>,
      context: Annotation<Document[]>,
      answer: Annotation<AIMessageChunk>,
    });

    const retrieve = async (state: typeof InputStateAnnotation.State) => {
      const retrievedDocs = await qdrant.similaritySearch(state.question, 3);
      return { context: retrievedDocs };
    };

    const generate = async (state: typeof StateAnnotation.State) => {
      const docsContent = state.context.length
        ? state.context.map((doc) => doc.pageContent).join('\n')
        : '';

      const messages = await promptTemplateWpp.invoke({
        customerName: state.customerName,
        messageHistory: state.messageHistory,
        question: state.question,
        context: docsContent || 'Nenhum contexto disponível',
      });

      const response = await this.openAImodel.invoke(messages);

      return {
        answer: response,
      };
    };

    const graph = new StateGraph(StateAnnotation)
      .addNode('retrieve', retrieve)
      .addNode('generate', generate)
      .addEdge('__start__', 'retrieve')
      .addEdge('retrieve', 'generate')
      .addEdge('generate', '__end__')
      .compile();

    return await graph.invoke({ customerName, question, messageHistory });
  }
}
