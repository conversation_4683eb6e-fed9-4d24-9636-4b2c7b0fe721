import { v4 as uuidV4 } from 'uuid';

import { Permission } from 'src/resources/permissions/entities/permission.entity';

const backofficeViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_View',
  slug: 'backoffice_view',
  group: 'backoffice',
  description: 'Usuário pode ver qualquer coisa no backoffice',
});

const backofficeCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Create',
  slug: 'backoffice_create',
  group: 'backoffice',
  description: 'Usuário pode criar qualquer coisa no backoffice',
});

const backofficeEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Edit',
  slug: 'backoffice_edit',
  group: 'backoffice',
  description: 'Usuário pode editar qualquer coisa no backoffice',
});

const backofficeDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Delete',
  slug: 'backoffice_delete',
  group: 'backoffice',
  description: 'Usuário pode apagar qualquer coisa no backoffice',
});

const backofficeDashboardViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Dashboard_View',
  slug: 'backoffice_dashboard_view',
  group: 'backoffice',
  description: 'Usuário pode ver o dashboard do backoffice',
});

const backofficeAccountViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Account_View',
  slug: 'backoffice_account_view',
  group: 'backoffice',
  description: 'Usuário pode ver as contas no backoffice',
});

const backofficeAccountEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Account_Edit',
  slug: 'backoffice_account_edit',
  group: 'backoffice',
  description: 'Usuário pode editar as contas no backoffice',
});

const backofficePlansViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Plans_View',
  slug: 'backoffice_plans_view',
  group: 'backoffice',
  description: 'Usuário pode ver os planos no backoffice',
});

const backofficePlansCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Plans_Create',
  slug: 'backoffice_plans_create',
  group: 'backoffice',
  description: 'Usuário pode criar os planos no backoffice',
});

const backofficePlansEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Plans_Edit',
  slug: 'backoffice_plans_edit',
  group: 'backoffice',
  description: 'Usuário pode editar os planos no backoffice',
});

const backofficePlansDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_Plans_Delete',
  slug: 'backoffice_plans_delete',
  group: 'backoffice',
  description: 'Usuário pode apagar os planos no backoffice',
});

const backofficeUserViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_User_View',
  slug: 'backoffice_user_view',
  group: 'backoffice',
  description: 'Usuário pode ver os usuários no backoffice',
});

const backofficeUserCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_User_Create',
  slug: 'backoffice_user_create',
  group: 'backoffice',
  description: 'Usuário pode criar um usuário no backoffice',
});

const backofficeUserEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_User_Edit',
  slug: 'backoffice_user_edit',
  group: 'backoffice',
  description: 'Usuário pode editar um usuário no backoffice',
});

const backofficeUserDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'Backoffice_User_Delete',
  slug: 'backoffice_user_delete',
  group: 'backoffice',
  description: 'Usuário pode apagar um usuário no backoffice',
});

export const backofficePermissions: Permission[] = [
  backofficeViewPermission,
  backofficeCreatePermission,
  backofficeEditPermission,
  backofficeDeletePermission,
  backofficeDashboardViewPermission,
  backofficeAccountViewPermission,
  backofficeAccountEditPermission,
  backofficePlansViewPermission,
  backofficePlansCreatePermission,
  backofficePlansEditPermission,
  backofficePlansDeletePermission,
  backofficeUserViewPermission,
  backofficeUserCreatePermission,
  backofficeUserEditPermission,
  backofficeUserDeletePermission,
];
