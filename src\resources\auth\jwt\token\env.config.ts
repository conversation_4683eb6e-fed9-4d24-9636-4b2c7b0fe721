import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { ITokenEnvs } from './token.contract';

export default registerAs('token-envs', () => {
  const values: ITokenEnvs = {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN,
  };

  const schema = Joi.object<ITokenEnvs>({
    secret: Joi.string().required().messages({
      'any.required': 'ENV: JWT_SECRET is required',
    }),
    expiresIn: Joi.string().required().messages({
      'any.required': 'ENV: JWT_EXPIRES_IN is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
