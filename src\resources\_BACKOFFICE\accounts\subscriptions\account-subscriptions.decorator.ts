import {
  SubscriptionStatus as SubscriptionStatusEnum,
  SubscriptionType as SubscriptionTypeEnum,
} from '@prisma/client';

import {
  BadRequestException,
  createParamDecorator,
  ExecutionContext,
  NotFoundException,
} from '@nestjs/common';

import {
  SubscriptionStatus,
  SubscriptionType,
} from 'src/@shared/types/subscriptions';

import { AccountSubscriptionDecoratorOutput } from './account-subscriptions.contracts';

interface AccountSubscriptionDecoratorProps extends Headers {
  subscriptionStatus?: SubscriptionStatus;
  subscriptionType?: SubscriptionType;
  planSecureId?: string;
  accountSecureId?: string;
}

export const AccountSubscriptionDecorator = createParamDecorator(
  async (
    data: any,
    ctx: ExecutionContext,
  ): Promise<AccountSubscriptionDecoratorOutput> => {
    const request = ctx.switchToHttp().getRequest();
    const query: AccountSubscriptionDecoratorProps = request.query;

    const prisma = request.prismaService;

    let subscriptionStatus: SubscriptionStatus | undefined;
    let subscriptionType: SubscriptionType | undefined;
    let planId: number | undefined;
    let accountId: number | undefined;

    if (!query?.['subscriptionStatus']) {
      subscriptionStatus = undefined;
    } else {
      if (
        !Object.values(SubscriptionStatusEnum).includes(
          query?.['subscriptionStatus'],
        )
      ) {
        throw new BadRequestException('Valor do status incorreto!');
      }
      subscriptionStatus = query['subscriptionStatus'] as SubscriptionStatus;
    }

    if (!query?.['subscriptionType']) {
      subscriptionType = undefined;
    } else {
      if (
        !Object.values(SubscriptionTypeEnum).includes(
          query?.['subscriptionType'],
        )
      ) {
        throw new BadRequestException('Valor do tipo incorreto!');
      }
      subscriptionType = query['subscriptionType'] as SubscriptionType;
    }

    if (!query?.['planSecureId']) {
      planId = undefined;
    } else {
      const plan = await prisma.plans.findFirst({
        where: {
          secureId: { equals: query['planSecureId'] },
        },

        select: { id: true },
      });

      if (!plan) {
        throw new NotFoundException('Plano não encontrado!');
      }

      planId = plan.id;
    }

    if (!query?.['accountSecureId']) {
      accountId = undefined;
    } else {
      const account = await prisma.accounts.findFirst({
        where: {
          secureId: { equals: query['accountSecureId'] },
        },

        select: { id: true },
      });

      if (!account) {
        throw new NotFoundException('Conta não encontrada!');
      }

      accountId = account.id;
    }

    return {
      subscriptionStatus,
      subscriptionType,
      planId,
      accountId,
    };
  },
);
