import { Injectable, NotFoundException } from '@nestjs/common';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { ProfileMapper } from './mapper/profile.mapper';
import { HashService } from '../auth/jwt/hash/hash.service';

@Injectable()
export class ProfileService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly hashService: HashService,
  ) { }

  async findOne(userId: number, accountId: number) {
    const userModel = await this.prismaService.users.findFirst({
      where: {
        id: { equals: userId },
      },
      include: {
        usersAccounts: {
          where: {
            accountId: { equals: accountId },
          },
          include: {
            account: {
              select: {
                secureId: true,
                subscriptions: {
                  where: {
                    isActive: true,
                    plan: {
                      isActive: true,
                    }
                  },
                  select: {
                    secureId: true,
                    cycle: true,
                    status: true,
                    type: true,
                    plan: {
                      select: {
                        secureId: true,
                        name: true,
                        slug: true,
                        knowledgeBaseLimit: true,
                        attendantsLimit: true,
                        chatbotsLimit: true,
                        description: true,
                        details: true,
                        iaMessagesLimit: true,
                        price: true,
                        whatsappNumberLimit: true,
                      },
                    },
                  }
                },
                transactions: {
                  select: {
                    payedAt: true,
                    secureId: true,
                    amount: true,
                    status: true,
                  }
                },
              },
            },
          },
        },
      },
    });

    if (!userModel) {
      throw new NotFoundException('Usuário não encontrado!');
    }

    return ProfileMapper.fromUserWithUserAccountsAndAccountModelToProfileOutputDto(
      userModel,
    );
  }

  async update(userId: number, updateProfileDto: UpdateProfileDto) {
    if (updateProfileDto?.companyName) {
      const ownerModel = await this.prismaService.users.findFirst({
        where: {
          id: { equals: userId },
        },

        select: {
          usersAccounts: {
            select: {
              account: { select: { id: true } },
              isOwner: true,
            },
          },
        },
      });
      if (!ownerModel) {
        throw new NotFoundException('Usuário não encontrado!');
      }
      if (!ownerModel.usersAccounts[0].isOwner) {
        throw new NotFoundException(
          'Você deve ser dono da Conta para alterar o nome da empresa!',
        );
      }

      await this.prismaService.accounts.update({
        where: {
          id: ownerModel.usersAccounts[0].account.id,
        },

        data: {
          companyName: updateProfileDto.companyName,
        },
      });
    }

    if (Object.keys(updateProfileDto).length > 0) {
      // NOTE: Esse trecho é somente para garantir que o campo isActive seja atualizado corretamente. Pois quando ele vem como false ele não estava sendo alterado corretamente!.
      let isActive: boolean | undefined = undefined;
      if (updateProfileDto?.isActive !== undefined) {
        isActive = updateProfileDto.isActive === false ? false : true;
      }

      // NOTE: Essa parte do código é um pouco complexa, mas é importante para garantir que a senha seja atualizada corretamente. Caso a senha for a mesma, ela não será atualizada.
      let password: string | undefined = undefined;
      if (updateProfileDto?.password) {
        const oldPassword = await this.prismaService.users.findFirst({
          where: {
            id: { equals: userId },
          },
          select: {
            password: true,
          },
        });

        const didPasswordMatch = await this.hashService.compare(
          updateProfileDto.password,
          oldPassword.password,
        );
        if (didPasswordMatch) {
          password = undefined;
        } else {
          password = await this.hashService.generate(updateProfileDto.password);
        }
      }

      const updatedUserModel = await this.prismaService.users.update({
        where: {
          id: userId,
        },

        data: {
          name: updateProfileDto?.name && updateProfileDto.name,
          email: updateProfileDto?.email && updateProfileDto.email,
          cpf: updateProfileDto?.cpf && updateProfileDto.cpf.replace(/\D/g, ''),
          cellPhone:
            updateProfileDto?.cellPhone &&
            updateProfileDto.cellPhone.replace(/\D/g, ''),
          password: password,
          isActive: isActive,
        },
      });
      if (!updatedUserModel) {
        throw new NotFoundException('Usuário não encontrado!');
      }

      return;
    }
  }

  async remove(userId: number) {
    await this.prismaService.$transaction(async (prisma) => {
      const userModel = await prisma.users.findFirst({
        where: { id: { equals: userId } },
        select: {
          id: true,
          usersAccounts: {
            where: { isOwner: true },

            select: {
              id: true,

              account: {
                select: { id: true },
              },
            },
          },
        },
      });

      await prisma.users.update({
        where: { id: userModel.id },
        data: {
          isActive: false,
          isDeleted: true,
        },
      });

      if (userModel.usersAccounts.length > 0) {
        await prisma.usersAccounts.update({
          where: {
            id: userModel.usersAccounts[0].id,
          },

          data: {
            isActive: false,
            isDeleted: true,
          },
        });

        await prisma.accounts.update({
          where: {
            id: userModel.usersAccounts[0].account.id,
          },

          data: {
            isActive: false,
            isDeleted: true,
          },
        });
      }
    });
  }
}
