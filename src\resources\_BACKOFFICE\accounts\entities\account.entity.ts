import { Entity } from 'src/@shared/contracts/entity/interface';
import { v4 as uuidV4 } from 'uuid';

type Subscription = {
  trialEndsAt: Date | null;
  plan: {
    slug: string;
  };
};

type AccountConstructorProps = {
  id?: number;

  secureId?: string;

  companyName: string;
  openaiApiKey?: string;
  prompt?: string;

  isActive?: boolean;
  isDeleted?: boolean;

  subscriptions?: Subscription[];

  createdAt?: Date;
  updatedAt?: Date;
};

type AccountJson = {
  secureId: string;
  companyName: string;
  openaiApiKey?: string;
  prompt?: string;
  isActive: boolean;
  isDeleted: boolean;
  planSlug: string;
  trialEndsAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

export class Account implements Entity {
  id?: number;

  secureId?: string;

  companyName: string;
  openaiApiKey?: string;
  prompt?: string;

  isActive: boolean;
  isDeleted: boolean;

  subscriptions?: Subscription[];

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: AccountConstructorProps) {
    this.id = props?.id && props.id;
    // this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.companyName = props.companyName;
    this.openaiApiKey = props?.openaiApiKey ? props.openaiApiKey : undefined;
    this.prompt = props?.prompt ? props.prompt : undefined;

    this.subscriptions = props?.subscriptions ? props.subscriptions : undefined;

    this.isActive = props?.isActive === false ? false : true;
    this.isDeleted = props?.isDeleted === false ? false : true;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  delete(): void {
    this.isDeleted = true;
    this.isActive = false;
  }

  restore(): void {
    this.isDeleted = false;
  }

  activate(): void {
    this.isActive = true;
  }

  deactivate(): void {
    this.isActive = false;
  }

  toJSON(): AccountJson {
    return {
      secureId: this.secureId,

      companyName: this.companyName,
      openaiApiKey: this.openaiApiKey,
      prompt: this.prompt,

      planSlug:
        this?.subscriptions && this.subscriptions.length > 0
          ? this.subscriptions[0]?.plan?.slug || null
          : null,
      trialEndsAt:
        this?.subscriptions && this.subscriptions.length > 0
          ? this.subscriptions[0]?.trialEndsAt || null
          : null,

      isActive: this.isActive,
      isDeleted: this.isDeleted,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
