import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { FixSubscriptionService } from './fix-subscription.service';

@Command({
  name: 'fix-subscription',
  description: 'Arruma as sessões restantes',
})
@Injectable()
export class FixSubscriptionCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(private readonly fixSubscriptionService: FixSubscriptionService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    await this.fixSubscriptionService
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
