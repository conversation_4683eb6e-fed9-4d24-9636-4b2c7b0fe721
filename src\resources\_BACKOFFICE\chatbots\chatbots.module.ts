import { Modu<PERSON> } from '@nestjs/common';
import { ChatbotsService } from './chatbots.service';
import { ChatbotsController } from './chatbots.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [ChatbotsController],
  providers: [ChatbotsService],
  exports: [ChatbotsService],
})
export class ChatbotsModule {}
