import { Entity } from 'src/@shared/contracts/entity/interface';

import { v4 as uuidV4 } from 'uuid';

type Account = {
  secureId: string;
};

type Upload = {
  secureId: string;
  urlCdn: string;
};

type Chatbot = {
  secureId: string;
  name: string;
  isAI: boolean;
};

type ChatsConstructorProps = {
  id?: number;
  secureId?: string;

  uploadId?: number;
  account?: Account;

  accountId: number;
  upload?: Upload;

  chatbotId?: number;
  chatbot?: Chatbot;

  name: string;
  welcomeMessage?: string;
  description?: string;
  chatBgColor?: string;
  inputChatBgColor?: string;
  inputChatTextColor?: string;
  chatButtonColor?: string;
  chatIconColor?: string;
  inputPlaceholderColor?: string;
  customerMessageBubbleColor?: string;
  customerMessageTextColor?: string;
  attendantMessageBubbleColor?: string;
  attendantMessageTextColor?: string;
  messageTimeColor?: string;
  avatarBgColor?: string;
  messageStatusColor?: string;
  webchatButtonBgColor?: string;

  isActive?: boolean;
  isDeleted?: boolean;

  updatedAt?: Date;
  createdAt?: Date;
};

type ChatsJson = {
  secureId: string;

  name: string;
  welcomeMessage?: string;
  description?: string;
  chatBgColor?: string;
  inputChatBgColor?: string;
  inputChatTextColor?: string;
  chatButtonColor?: string;
  chatIconColor?: string;
  inputPlaceholderColor?: string;
  customerMessageBubbleColor?: string;
  customerMessageTextColor?: string;
  attendantMessageBubbleColor?: string;
  attendantMessageTextColor?: string;
  avatarBgColor?: string;
  messageTimeColor?: string;
  messageStatusColor?: string;
  webchatButtonBgColor?: string;

  isActive: boolean;
  isDeleted: boolean;

  updatedAt: Date;
  createdAt: Date;

  account: Account;
  upload: Upload;
  chatbot: Chatbot;
};

export class Chats implements Entity {
  id: number;
  secureId: string;

  uploadId: number;
  upload?: Upload;

  accountId: number;
  account?: Account;

  chatbotId: number;
  chatbot?: Chatbot;

  name: string;
  welcomeMessage: string | null;
  description: string | null;
  chatBgColor: string | null;
  inputChatBgColor: string | null;
  inputChatTextColor: string | null;
  chatButtonColor: string | null;
  chatIconColor: string | null;
  customerMessageBubbleColor: string | null;
  customerMessageTextColor: string | null;
  attendantMessageBubbleColor: string | null;
  attendantMessageTextColor: string | null;
  avatarBgColor: string | null;
  messageTimeColor: string | null;
  messageStatusColor: string | null;
  webchatButtonBgColor: string | null;

  isActive: boolean;
  isDeleted: boolean;

  updatedAt: Date;
  createdAt: Date;

  constructor(props: ChatsConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.uploadId = props?.uploadId ? props.uploadId : undefined;
    this.upload = props?.upload ? props.upload : undefined;

    this.accountId = props?.accountId ? props.accountId : undefined;
    this.account = props?.account ? props.account : undefined;

    this.chatbotId = props?.chatbotId ? props.chatbotId : undefined;
    this.chatbot = props?.chatbot ? props.chatbot : undefined;

    this.name = props.name;
    this.welcomeMessage = props?.welcomeMessage ? props.welcomeMessage : null;
    this.description = props?.description ? props.description : null;
    this.chatBgColor = props?.chatBgColor ? props.chatBgColor : null;
    this.inputChatBgColor = props?.inputChatBgColor
      ? props.inputChatBgColor
      : null;
    this.inputChatTextColor = props?.inputChatTextColor
      ? props.inputChatTextColor
      : null;
    this.chatButtonColor = props?.chatButtonColor
      ? props.chatButtonColor
      : null;
    this.chatIconColor = props?.chatIconColor ? props.chatIconColor : null;
    this.webchatButtonBgColor = props?.webchatButtonBgColor
      ? props.webchatButtonBgColor
      : null;
    this.customerMessageBubbleColor = props?.customerMessageBubbleColor
      ? props.customerMessageBubbleColor
      : null;
    this.customerMessageTextColor = props?.customerMessageTextColor
      ? props.customerMessageTextColor
      : null;
    this.attendantMessageBubbleColor = props?.attendantMessageBubbleColor
      ? props.attendantMessageBubbleColor
      : null;
    this.attendantMessageTextColor = props?.attendantMessageTextColor
      ? props.attendantMessageTextColor
      : null;
    this.messageTimeColor = props?.messageTimeColor
      ? props.messageTimeColor
      : null;
    this.messageStatusColor = props?.messageStatusColor
      ? props.messageStatusColor
      : null;
    this.avatarBgColor = props?.avatarBgColor ? props.avatarBgColor : null;

    this.isActive = props?.isActive === false ? false : true;
    this.isDeleted = props?.isDeleted === true ? true : false;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  delete(): void {
    this.isDeleted = true;
  }

  restore(): void {
    this.isDeleted = false;
  }

  deactivate(): void {
    this.isActive = false;
  }

  active(): void {
    this.isActive = true;
  }

  toJSON(): ChatsJson {
    return {
      secureId: this.secureId,
      name: this.name,
      welcomeMessage: this.welcomeMessage,
      description: this.description,
      chatBgColor: this.chatBgColor,
      inputChatBgColor: this.inputChatBgColor,
      inputChatTextColor: this.inputChatTextColor,
      chatButtonColor: this.chatButtonColor,
      chatIconColor: this.chatIconColor,
      webchatButtonBgColor: this.webchatButtonBgColor,
      customerMessageBubbleColor: this.customerMessageBubbleColor,
      customerMessageTextColor: this.customerMessageTextColor,
      attendantMessageBubbleColor: this.attendantMessageBubbleColor,
      attendantMessageTextColor: this.attendantMessageTextColor,
      avatarBgColor: this.avatarBgColor,
      messageTimeColor: this.messageTimeColor,
      messageStatusColor: this.messageStatusColor,
      isActive: this.isActive,
      isDeleted: this.isDeleted,
      updatedAt: this.updatedAt,
      createdAt: this.createdAt,
      account: this.account,
      upload: this.upload,
      chatbot: this.chatbot,
    };
  }
}
