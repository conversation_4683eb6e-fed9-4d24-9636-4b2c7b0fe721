import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateChatbotDto } from './dto/create-chatbot.dto';
import { UpdateChatbotDto } from './dto/update-chatbot.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { Chatbot } from './entities/chatbot.entity';
import { ChatBotMapper } from './mapper/chatbot.mapper';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { ChatBotFactory } from './factory/chatbot.factory';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { KnowledgeBase } from '../knowledge-base/entities/knowledge-base.entity';
import { KnowledgeBaseMapper } from '../knowledge-base/mapper/knowledge-base.mapper';
import { v4 as uuid } from 'uuid';
import { RAGService } from 'src/third-party/langchain/rag/rag.service';
import { MessageChainService } from 'src/third-party/langchain/message-chain/message-chain.service';
import { ExtractorInfosService } from 'src/third-party/langchain/extractor-infos/extractor-infos.service';
import { AnalyzeSentimentService } from 'src/third-party/langchain/analyze-sentiment/analyze-sentiment.service';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { PromptBuilderService } from 'src/third-party/langchain/prompt-builder/prompt-builder.service';
import { DefaultLeadCaptureJson } from './constants/leadCaptureJson';
import { ensureCompleteJson } from 'src/utils/ensureCompleteJson';

@Injectable()
export class ChatbotsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly ragService: RAGService,
    private readonly promptBuilderService: PromptBuilderService,
    private readonly messageChainService: MessageChainService,
    private readonly extractorInfosService: ExtractorInfosService,
    private readonly analyzeSentimentService: AnalyzeSentimentService,
  ) {}

  async create(createChatbotDto: CreateChatbotDto, accountId: number) {
    const chatbot = new Chatbot({
      accountId,
      ...createChatbotDto,
      leadCaptureJson: DefaultLeadCaptureJson,
      isActive: true,
      isDeleted: false,
    });

    const modelChatbot = ChatBotMapper.fromEntityToModel(chatbot);

    try {
      const chatbotModel = await this.prismaService.chatBots.create({
        data: modelChatbot,
      });
      const chatBotOutput =
        ChatBotFactory.createOneBatchChatBotOutputDtoFromBatchModel(
          chatbotModel,
        );

      await this.createKnowledgeBase(chatbotModel.secureId, accountId);

      if (chatbotModel.isAI) {
        await this.promptBuilder(chatbotModel.secureId);
      }

      return chatBotOutput;
    } catch (error) {
      console.log(error);
      throw new Error('Erro ao criar chatbot');
    }
  }

  async createKnowledgeBase(chatbotSecureId: string, accountId: number) {
    const chatbot = await this.prismaService.chatBots.findFirst({
      where: {
        secureId: chatbotSecureId,
      },
    });

    if (!chatbot) {
      throw new NotFoundException('Chatbot não encontrado');
    }

    const knowledgeBase = new KnowledgeBase({
      collectionName: uuid(),
      accountId,
      chunkOverlap: 200,
      chunkSize: 1000,
      chatbotId: chatbot.id,
      isActive: true,
      isDeleted: false,
    });

    const modelKnowledgeBase =
      KnowledgeBaseMapper.fromEntityToModel(knowledgeBase);

    try {
      await this.prismaService.knowledgeBase.create({
        data: modelKnowledgeBase,
      });

      await this.ragService.createCollection(modelKnowledgeBase.collectionName);
    } catch (error) {
      throw new Error('Error creating knowledgeBase');
    }
    return;
  }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountId: number,
  ) {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      name: {
        contains: paginationQuery?.search && paginationQuery.search,
      },
      accountId: accountId,
    };

    const chatbots = await this.prismaService.chatBots.findMany({
      take: paginationQuery.limit,
      skip: skip,
      orderBy: {
        name: 'asc',
      },
      where: whereClause,
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prismaService.chatBots,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    const formattedChatBots =
      ChatBotFactory.convertBatchFromModelToChatBot(chatbots);

    return {
      meta: meta,
      data: !formattedChatBots ? [] : formattedChatBots,
    };
  }

  async findOne(secureId: string) {
    const chatBotModel = await this.prismaService.chatBots.findFirst({
      where: { secureId: secureId },
    });

    if (!chatBotModel) {
      throw new NotFoundException('Chatbot não encontrado');
    }

    const chatBotOutput =
      ChatBotFactory.createOneBatchChatBotOutputDtoFromBatchModel(chatBotModel);

    return chatBotOutput;
  }

  async update(secureId: string, updateChatbotDto: UpdateChatbotDto) {
    if (Object.keys(updateChatbotDto).length === 0) {
      throw new BadRequestException(
        'Dados inválidos. É necessário ter ao menos 1 item dentro do objeto',
      );
    }
    const { leadCaptureJson, ...updateData } = updateChatbotDto;

    const chatBotModel = await this.prismaService.chatBots.findFirst({
      where: { secureId: { equals: secureId } },
    });

    if (!chatBotModel) {
      throw new NotFoundException('ChatBot não encontrado');
    }

    let updatedLeadCaptureJson = ensureCompleteJson(
      chatBotModel.leadCaptureJson,
      DefaultLeadCaptureJson,
    );

    if (leadCaptureJson) {
      updatedLeadCaptureJson = {
        ...updatedLeadCaptureJson,
        collectCPF: !!leadCaptureJson.collectCPF,
        collectEmail: !!leadCaptureJson.collectEmail,
        collectName: !!leadCaptureJson.collectName,
        collectPhone: !!leadCaptureJson.collectPhone,
      };
    }

    await this.prismaService.chatBots.update({
      where: { secureId: secureId },
      data: {
        leadCaptureJson: updatedLeadCaptureJson,
        ...updateData,
      },
    });

    await this.promptBuilder(chatBotModel.secureId);

    return;
  }

  async remove(secureId: string) {
    const chatBotModel = this.prismaService.chatBots.findFirst({
      where: { secureId: secureId },
    });

    if (!chatBotModel) {
      throw new NotFoundException('ChatBot não encontrado');
    }

    const chatBot = await this.prismaService.chatBots.update({
      where: { secureId: secureId },
      data: {
        isDeleted: true,
        isActive: false,
      },
    });

    await this.prismaService.chats.updateMany({
      where: { chatbotId: chatBot.id },
      data: {
        chatbotId: null,
      },
    });

    return;
  }

  async isDatabasePopulated(chatbotSecureId: string): Promise<boolean> {
    const chatbot = await this.prismaService.chatBots.findFirst({
      where: {
        secureId: chatbotSecureId,
      },
    });

    if (!chatbot) {
      throw new NotFoundException('Chatbot não encontrado');
    }

    const knowledgeBase = await this.prismaService.knowledgeBase.findFirst({
      where: {
        chatbotId: chatbot.id,
      },
      select: {
        content: true,
      },
    });

    if (!knowledgeBase) {
      throw new NotFoundException('Base de conhecimento não encontrada');
    }

    if (!knowledgeBase.content) {
      return false;
    }
    return true;
  }

  async chatConversation(messages: string[], chatbotSecureId: string) {
    const knowledgeBase = await this.prismaService.knowledgeBase.findFirst({
      where: {
        chatbot: {
          secureId: chatbotSecureId,
        },
      },
      select: {
        collectionName: true,
        chatbot: {
          select: {
            IAPrompt: true,
            temperature: true,
          },
        },
        account: {
          select: {
            prompt: true,
            openaiApiKey: true,
          },
        },
      },
    });

    if (!knowledgeBase) {
      throw new NotFoundException('Base de conhecimento não encontrada');
    }
    const questionMessage = messages[messages.length - 1];

    const currentPrompt = !!knowledgeBase.account.prompt
      ? knowledgeBase.account.prompt
      : knowledgeBase.chatbot.IAPrompt;

    const aiMessage = await this.messageChainService.streamMessage({
      collectionName: knowledgeBase.collectionName,
      messagesHistory: messages,
      question: questionMessage,
      temperature: knowledgeBase.chatbot.temperature,
      generatedPrompt: currentPrompt,
      alternativeApiKey: knowledgeBase.account.openaiApiKey,
    });

    return aiMessage;
  }
  async promptBuilder(chatbotSecureId: string) {
    const chatbot = await this.prismaService.chatBots.findFirst({
      where: {
        secureId: chatbotSecureId,
      },
    });

    if (!chatbot) {
      throw new NotFoundException('Chatbot não encontrado');
    }

    const generatedPrompt = this.promptBuilderService.buildPrompt({
      name: chatbot.name,
      emotionalTone: chatbot.emotionalTone,
      mood: chatbot.mood,
      responseSize: chatbot.responseSize,
      responseStyle: chatbot.responseStyle,
    });

    await this.prismaService.chatBots.update({
      where: { secureId: chatbotSecureId },
      data: {
        IAPrompt: generatedPrompt,
      },
    });
  }
}
