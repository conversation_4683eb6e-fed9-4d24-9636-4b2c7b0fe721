import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class ChangeAccountInputDto {
  @ApiProperty({
    description: 'SecureID da conta selecionada',
    required: true,
    uniqueItems: true,
    maxLength: 255,
    minLength: 3,
    default: '',
    type: String,
    example: 'b7e1e0b7-6a3d-4b2e-9b9a-0e1b5b5b5b5b',
  })
  @IsNotEmpty({ message: 'O id da conta é obrigatório' })
  accountSecureId: string;
}
