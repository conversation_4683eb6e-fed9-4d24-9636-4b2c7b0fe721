const name = {
  description: 'Nome do plano',
  required: true,
  uniqueItems: true,
  maxLength: 255,
  minLength: 3,
  type: String,
  example: 'Plano básico',
};

const slug = {
  description: 'Slug do plano',
  example: 'basic-plan',
  required: true,
  uniqueItems: true,
  type: String,
  minLength: 3,
  maxLength: 255,
};

const description = {
  description: 'Descrição do plano',
  example: 'Plano básico',
  required: false,
  type: String,
  minLength: 3,
  maxLength: 255,
};

const details = {
  description: 'Detalhes do plano, descrições opcionais',
  example: '<ul><li>Atendimento por chat</li></ul>',
  required: false,
  type: String,
  minLength: 3,
  maxLength: 65535,
};

const price = {
  description: 'Preço do plano. NÃO multiplicar o valor por 100',
  example: 'R$ 100.00',
  required: false,
  default: 'R$ 0.00',
  type: String,
};

const attendantsLimit = {
  description: 'Número máximo de atendentes permitidos no plano',
  example: 10,
  required: true,
  maximum: 99999,
  minimum: 1,
  type: Number,
};

const whatsappNumberLimit = {
  description: 'Limite de atendentes',
  example: 10,
  required: true,
  maximum: 99999,
  minimum: 1,
  type: Number,
};

const chatbotsLimit = {
  example: 10,
  description: 'Número máximo de chatbots',
  minimum: 1,
  maximum: 99999,
  required: true,
  type: Number,
};

const knowledgeBaseLimit = {
  description: 'Número máximo de atendentes',
  example: 10,
  minimum: 1,
  maximum: 99999,
  required: true,
  type: Number,
};

const iaMessagesLimit = {
  description: 'Limite de atendentes',
  required: true,
  maximum: 99999,
  minimum: 1,
  type: Number,
  example: 10,
};

const interval = {
  description:
    'Intervalo de tempo entre as cobranças. Exemplo: 1 = mensal, 3 = trimestral, 6 = semestral, 12 = anual',
  required: false,
  maximum: 12,
  minimum: 1,
  type: Number,
  example: 1,
  default: 1,
};

const trialDays = {
  description:
    'Representa a quantidades de dias que o plano ficará em trial. Se for 0, o plano não terá trial. Se for 10 é para 10 dias de trial ou seja 10 dias sem cobrança.',
  required: false,
  maximum: 365,
  type: Number,
  example: 1,
  default: 0,
};

const isActive = {
  description: 'Indica se o plano está ativo',
  example: true,
  required: false,
  default: true,
  type: Boolean,
};

export const createPlansDto = {
  isActive,
  price,
  description,
  slug,
  name,
  iaMessagesLimit,
  knowledgeBaseLimit,
  chatbotsLimit,
  whatsappNumberLimit,
  attendantsLimit,
  interval,
  details,
  trialDays,
};
