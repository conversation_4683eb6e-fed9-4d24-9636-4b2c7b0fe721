import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { MessageFactory } from './factory/message.factory';

@Injectable()
export class MessagesServices {
  constructor(private readonly prismaService: PrismaService) {}

  async findaAllChatMessages(
    paginationQuery: PaginationQueryType,
    secureId: string,
    accountId: number,
  ) {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    const defaultSession = await this.prismaService.chatSessions.findUnique({
      where: {
        secureId: secureId,
        accountId: accountId,
      },
      include: {
        whatsapp: true,
      },
    });

    if (!defaultSession) {
      throw new NotFoundException('Sessão não encontrada');
    }

    const sessionsWithSameCustomerId =
      await this.prismaService.chatSessions.findMany({
        where: {
          customerId: defaultSession.customerId,
          accountId: accountId,
          isActive: true,
          AND: {
            OR: [
              {
                whatsapp: null,
              },
              {
                whatsapp: {
                  secureId: defaultSession.whatsapp?.secureId,
                },
              },
            ],
          },
        },
      });

    const whereClause = {
      chatSession: {
        id: {
          in: sessionsWithSameCustomerId.map((session) => session.id),
        },
      },
    };

    const sessionMessages = await this.prismaService.chatMessages.findMany({
      take: paginationQuery.limit,
      skip: skip,
      where: whereClause,
      include: {
        upload: {
          select: {
            secureId: true,
            urlCdn: true,
          },
        },
        userAccount: {
          select: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        replyTo: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prismaService.chatMessages,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    const formattedData =
      MessageFactory.convertBatchFromModelToMessage(sessionMessages);

    return {
      meta,
      data: formattedData,
    };
  }
}
