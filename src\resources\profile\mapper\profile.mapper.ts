import { UserWithUsersAccountsAndAccountModel } from '../profile.contract';
import { TypeProfileOutputDto } from '../dto/profile-output.dto';
import { FormatMoney } from 'src/@shared/helpers/money/format-money';

export class ProfileMapper {
  static fromUserWithUserAccountsAndAccountModelToProfileOutputDto(
    userModel: UserWithUsersAccountsAndAccountModel,
  ): TypeProfileOutputDto {
    // Garante que haverá ao menos um `userAccount`
    const [primaryUserAccount] = userModel.usersAccounts;

    const subscription = primaryUserAccount?.account.subscriptions[0]
      ? {
        ...primaryUserAccount.account.subscriptions[0],
        plan: {
          ...primaryUserAccount.account.subscriptions[0].plan,
          price: FormatMoney.fromModelToOutput(
            primaryUserAccount.account.subscriptions[0].plan.price,
          ),
        },
      }
      : null;

    const transactions = primaryUserAccount?.account.transactions.map(transaction => ({
      ...transaction,
      amount: FormatMoney.fromModelToOutput(transaction.amount),
    }));

    return {
      user: {
        secureId: userModel.secureId,
        name: userModel.name,
        email: userModel.email,
        cpf: userModel.cpf,
        cellPhone: userModel.cellPhone,
      },
      account: {
        secureId: primaryUserAccount.account.secureId,
      },
      subscription,
      transactions,
    };
  }
}
