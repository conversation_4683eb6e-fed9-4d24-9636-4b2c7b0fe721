import { registerAs } from '@nestjs/config';
import * as Jo<PERSON> from 'joi';
import { IStorageEnvs } from './s3.contract';

export default registerAs('storage-environment', () => {
  const values: IStorageEnvs = {
    bucketName: process.env.STORAGE_BUCKET_NAME,
    region: process.env.STORAGE_REGION,
    endpoint: process.env.STORAGE_ENDPOINT,
    accessKey: process.env.STORAGE_ACCESS_KEY,
    secretKey: process.env.STORAGE_SECRET_KEY,
    acl: process.env.STORAGE_ACL,
    uploadDir: process.env.STORAGE_UPLOAD_DIR,
    cdnUrl: process.env.STORAGE_CDN_URL,
  };

  // Joi validations
  const schema = Joi.object<IStorageEnvs>({
    bucketName: Joi.string().required().messages({
      'any.required': `STORAGE_BUCKET_NAME is missing ENV`,
    }),
    region: Joi.string().required().messages({
      'any.required': `STORAGE_REGION is missing ENV`,
    }),
    endpoint: Joi.string().required().messages({
      'any.required': `STORAGE_ENDPOINT is missing ENV`,
    }),
    accessKey: Joi.string().required().messages({
      'any.required': `STORAGE_ACCESS_KEY is missing ENV`,
    }),
    secretKey: Joi.string().required().messages({
      'any.required': `STORAGE_SECRET_KEY is missing ENV`,
    }),
    acl: Joi.string().required().messages({
      'any.required': `STORAGE_ACL is missing ENV`,
    }),
    uploadDir: Joi.string()
      .required()
      .messages({
        'any.required': `STORAGE_UPLOAD_DIR is missing
        ENV`,
      }),
    cdnUrl: Joi.string().required().messages({
      'any.required': `STORAGE_CDN_URL is missing ENV`,
    }),
  });

  // Validates our values using the schema.
  // Passing a flag to tell Joi to not stop validation on the
  // first error, we want all the errors found.
  const { error } = schema.validate(values, { abortEarly: false });

  // If the validation is invalid, "error" is assigned a
  // ValidationError object providing more information.
  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  // If the validation is valid, then the "error" will be
  // undefined and this will return successfully.
  return values;
});
