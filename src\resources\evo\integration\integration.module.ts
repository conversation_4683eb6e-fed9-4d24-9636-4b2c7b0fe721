import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { EvoIntegrationService } from './integration.service';
import { EvoIntegrationController } from './integration.controller';
import { EvoInstancesModule } from 'src/third-party/evo/instances/instances.module';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [EvoInstancesModule, PrismaModule],
  controllers: [EvoIntegrationController],
  providers: [EvoIntegrationService],
})
export class EvoIntegrationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('whatsapp/integration');
  }
}
