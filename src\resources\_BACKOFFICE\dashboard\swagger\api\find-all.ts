import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const okResponse: ApiResponseNoStatusOptions = {
  examples: {
    withNoData: {
      summary:
        'Lista os dados de dashboard para a conta de backoffice. Quando não existem dados disponíveis',
      value: {
        accounts: {
          quantity: 0,
        },
        chats: {
          quantity: 0,
        },
        attendants: {
          quantity: 0,
        },
        transactions: {
          waitingAmount: {
            rawValue: 0,
            formattedValue: 'R$ 0,00',
          },
          paidAmount: {
            rawValue: 0,
            formattedValue: 'R$ 0,00',
          },
          unpaidAmount: {
            rawValue: 0,
            formattedValue: 'R$ 0,00',
          },
          refundedAmount: {
            rawValue: 0,
            formattedValue: 'R$ 0,00',
          },
        },
        subscriptions: {
          trials: 0,
          paid: 0,
          canceled: 0,
        },
      },
    },

    withData: {
      summary:
        'Lista os dados de dashboard para a conta de backoffice. Quando existem dados disponíveis',
      value: {
        accounts: {
          quantity: 1,
        },
        chats: {
          quantity: 1,
        },
        attendants: {
          quantity: 1,
        },
        transactions: {
          waitingAmount: {
            rawValue: 70000,
            formattedValue: 'R$ 700,00',
          },
          paidAmount: {
            rawValue: 10000,
            formattedValue: 'R$ 100,00',
          },
          unpaidAmount: {
            rawValue: 10000,
            formattedValue: 'R$ 100,00',
          },
          refundedAmount: {
            rawValue: 10000,
            formattedValue: 'R$ 100,00',
          },
        },
        subscriptions: {
          trials: 10,
          paid: 10,
          canceled: 0,
        },
      },
    },
  },
};

export const findAllDashboard = {
  status: {
    okResponse,
  },
};
