import { Injectable } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';

import { SyncRoleRecordsService } from './sync-role-records.service';
import { PrismaClient } from '@prisma/client';

@Command({
  name: 'sync-roles',
  description:
    '<PERSON><PERSON> as roles do sistema com base no arquivo de roles dentro da pasta constants. Esse comando não faz o recadastro de roles; para atualizar as roles, utilize o comando: update-roles',
})
@Injectable()
export class SyncRoleRecordsCommand extends CommandRunner {
  prisma: PrismaClient;
  constructor(private readonly syncRoles: SyncRoleRecordsService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    this.syncRoles.execute(this.prisma).then(() => this.prisma.$disconnect());
  }
}
