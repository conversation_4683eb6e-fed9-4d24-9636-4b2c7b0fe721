import { Injectable } from '@nestjs/common';

import { User } from 'src/resources/users/entities/user.entity';
import { UserTokenPayload } from './entities/user-token-payload.entity';
import { PayloadMapper } from './mapper/payload.mapper';

@Injectable()
export class PayloadService {
  public generateUserTokenPayload(
    user: User,
    activeAccountSecureId: string,
  ): UserTokenPayload {
    return PayloadMapper.toUserTokenPayload(user, activeAccountSecureId);
  }
}
