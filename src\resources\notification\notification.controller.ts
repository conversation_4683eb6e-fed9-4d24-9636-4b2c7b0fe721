import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';

@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT', 'BACKOFFICE'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @UserIdExtractorDecorator() userId: number,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.notificationService.findAll(
      userId,
      accountId,
      paginationQuery,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT', 'BACKOFFICE'])
  @Get('read/:secureId')
  async readNotification(
    @Param('secureId') secureId: string,
    @UserIdExtractorDecorator() userId: number,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.notificationService.readNotification(
      secureId,
      userId,
      accountId,
    );
  }
}
