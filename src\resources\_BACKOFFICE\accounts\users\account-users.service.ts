import { Injectable } from '@nestjs/common';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { AccountUsersFactory } from './factory/account.users.factory';
import { AccountUserOutputDto } from './dto/get-account-user.dto';

@Injectable()
export class AccountUsersService {
  constructor(private readonly prismaService: PrismaService) { }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountUsersQuery: any
  ): Promise<any> {
    const whereClause = {
      usersAccounts: {
        some: {
          accountId: accountUsersQuery.accountId,
          user: {
            name: accountUsersQuery.userName
          }
        },
      },
      isActive: isActiveIsDeletedQuery.isActive,
    };

    const paginationHelper = new PaginationHelper<'Users'>({
      modelDelegate: this.prismaService.users,
      paginationQuery,
      whereClause: whereClause as never,
    });

    const usersModel = await this.prismaService.users.findMany({
      take: paginationHelper.take,
      skip: paginationHelper.skip,

      where: {
        ...whereClause,
      },

      include: {
        usersAccounts: {
          select: {
            isOwner: true,
            roleId: true,
            accountsPermissions: {
              select: {
                permission: {
                  select: {
                    secureId: true,
                    slug: true,
                  }
                }
              }
            }
          }
        },
      },
    });

    const meta = await paginationHelper.getPaginationMeta();
    const data = !usersModel
      ? ([] as AccountUserOutputDto[])
      : await AccountUsersFactory.createAccountUserOutputFromAccountUserWithUsersAccountBatch(
        usersModel
      );

    return {
      meta,
      data,
    }
  }

  findOne(id: number) {
    return `This action returns a #${id} accountUser`;
  }
}
