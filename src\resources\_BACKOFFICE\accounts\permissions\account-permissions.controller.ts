import { Controller, Get, UseGuards } from '@nestjs/common';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { AccountPermissionsService } from './account-permissions.service';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';

@Controller('account-permissions')
export class AccountPermissionsController {
  constructor(
    private readonly accountsPermissionService: AccountPermissionsService,
  ) {}

  @UseGuards(JwtGuard)
  @Get()
  async getUserAccountPermissions(
    @AccountIdExtractor() accountId: number,
    @UserIdExtractorDecorator() userId: number,
  ) {
    return await this.accountsPermissionService.getUserAccountPermissions(
      accountId,
      userId,
    );
  }
}
