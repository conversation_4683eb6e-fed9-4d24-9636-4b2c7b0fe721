import { CPFMask } from 'src/@shared/helpers/mask/cpf.mask';
import { Contacts as ContactModel } from '@prisma/client';
import { PhoneMask } from 'src/@shared/helpers/phone/phone.mask';
import { ContactOutputDto } from '../dto/contact-output.dto';

export class GetAllContactFactory {
  static createFromContactModelBatchToOutputDtoBatch(
    models: ContactModel[],
  ): ContactOutputDto[] {
    return models.map((contact) => {
      return {
        secureId: contact.secureId,
        name: contact.name,
        email: contact.email,
        phone: contact.phone ? PhoneMask.apply(contact.phone) : null,
        document: contact.document ? CPFMask.apply(contact.document) : null,
        isActive: contact.isActive,

        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
      };
    });
  }
}
