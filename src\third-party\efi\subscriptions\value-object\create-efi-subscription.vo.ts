import { Plans as PlanModel, Users as UserModel } from "@prisma/client";

type CreateEFISubscriptionProps = {
  planModel: PlanModel;
  userModel: UserModel;
  paymentToken: string;
  subscriptionSecureId: string;
}

export class CreateEFISubscriptionVO {
  plan: {
    name: string;
    value: number;
    trialDays: number;
    efiPlanId: number;
  }

  paymentToken: string;
  subscriptionSecureId: string;

  user: {
    name: string;
    cpf: string;
    email: string;
    phoneNumber?: string;
  };

  constructor(props: CreateEFISubscriptionProps) {
    const planValue = props.planModel.price.replace(/\D/g, '');
    this.plan = {
      name: props.planModel.name,
      value: Number(planValue),
      trialDays: props.planModel.trialDays,
      efiPlanId: props.planModel.efiPlanId,
    };

    this.user = {
      name: props.userModel.name,
      cpf: props.userModel.cpf,
      email: props.userModel.email,
      phoneNumber: props.userModel.cellPhone,
    };

    this.paymentToken = props.paymentToken;
    this.subscriptionSecureId = props.subscriptionSecureId;
  }
}