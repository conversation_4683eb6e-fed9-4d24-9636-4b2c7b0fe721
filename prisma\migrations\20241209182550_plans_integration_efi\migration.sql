-- AlterTable
ALTER TABLE `permissions` MODIFY `name` <PERSON>NUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete', 'Account View', 'Account Create', 'Account Edit', 'Account Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete', 'account_view', 'account_create', 'account_edit', 'account_delete') NOT NULL,
    MODIFY `group` ENUM('backoffice', 'app', 'account') NOT NULL;

-- AlterTable
ALTER TABLE `plans` ADD COLUMN `details` TEXT NULL,
    ADD COLUMN `efi_is_deleted` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `efi_plan_id` INTEGER NULL,
    ADD COLUMN `interval` INTEGER NOT NULL DEFAULT 1;
