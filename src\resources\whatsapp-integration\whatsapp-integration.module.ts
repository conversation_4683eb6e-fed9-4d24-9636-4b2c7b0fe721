import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { WhatsappIntegrationService } from './whatsapp-integration.service';
import { WhatsappIntegrationController } from './whatsapp-integration.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule],
  controllers: [WhatsappIntegrationController],
  providers: [WhatsappIntegrationService],
})
export class WhatsappIntegrationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('whatsapp-integration');
  }
}
