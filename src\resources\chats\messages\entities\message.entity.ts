import { Entity } from 'src/@shared/contracts/entity/interface';

import { v4 as uuidV4 } from 'uuid';

type userAccount = {
  secureId: string;
  user: User;
};

type User = {
  name: string;
};

type upload = {
  secureId: string;
  urlCdn: string;
  fileType: string;
  type: string;
  fileName: string;
};

type chatSession = {
  secureId: string;
  senderId: string;
  senderName: string;
};

type replyTo = {
  secureId: string;
  message: string;
  messageDirection: string;
  messageJid: string;
};

type MessagesConstructorProps = {
  id?: number;
  secureId?: string;
  chatBotId?: number;
  userAccountId?: number;
  chatSessionId: number;

  receiveMessage: string;
  sendMessage: string;
  messageDirection?: string;
  messageType?: string;

  isActive?: boolean;
  isDeleted?: boolean;

  inputToken?: number;
  outputToken?: number;

  userAccount?: userAccount;
  upload?: upload;
  chatSession?: chatSession;
  replyTo?: replyTo;

  createdAt?: Date;
  updatedAt?: Date;
};

type MessagesJson = {
  secureId: string;

  receiveMessage: string;
  sendMessage: string;
  messageType: string;

  messageDirection: string;
  userAccount?: userAccount;
  upload?: upload;
  chatSession?: chatSession;
  replyTo?: replyTo;

  createdAt: Date;
  updateAt: Date;
};

export class ChatMessages implements Entity {
  id: number;
  secureId: string;
  chatBotId: number;
  userAccountId: number;
  chatSessionId: number;

  receiveMessage: string;
  sendMessage: string;
  messageType: string;

  isActive: boolean;
  isDeleted: boolean;
  messageDirection: string;

  inputToken: number | null;
  outputToken: number | null;

  userAccount?: userAccount;
  upload?: upload;
  chatSession?: chatSession;
  replyTo?: replyTo;

  createdAt: Date;
  updatedAt: Date;

  constructor(props: MessagesConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props.secureId ? props.secureId : uuidV4();
    this.chatBotId = props.chatBotId ? props.chatBotId : undefined;
    this.userAccountId = props.userAccountId ? props.userAccountId : undefined;
    this.chatSessionId = props.chatSessionId ? props.chatSessionId : undefined;

    this.messageDirection = props?.messageDirection
      ? props.messageDirection
      : 'received';

    this.messageType = props?.messageType ? props.messageType : 'text';

    this.receiveMessage = props.receiveMessage;
    this.sendMessage = props.sendMessage;

    this.isActive = props?.isActive === false ? false : true;
    this.isDeleted = props?.isDeleted === true ? true : false;

    this.inputToken = props?.inputToken ? props.inputToken : undefined;
    this.outputToken = props?.outputToken ? props.outputToken : undefined;

    this.userAccount = props?.userAccount ? props.userAccount : undefined;
    this.chatSession = props?.chatSession ? props.chatSession : undefined;
    this.upload = props?.upload ? props.upload : undefined;
    this.replyTo = props?.replyTo ? props.replyTo : undefined;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  delete(): void {
    this.isDeleted = true;
  }

  restore(): void {
    this.isDeleted = false;
  }

  deactivate(): void {
    this.isActive = false;
  }

  active(): void {
    this.isActive = true;
  }

  toJSON(): MessagesJson {
    return {
      secureId: this.secureId,
      receiveMessage: this.receiveMessage,
      sendMessage: this.sendMessage,
      messageDirection: this.messageDirection,
      messageType: this.messageType,
      userAccount: this.userAccount,
      upload: this.upload,
      replyTo: this.replyTo,
      chatSession: this.chatSession,
      createdAt: this.createdAt,
      updateAt: this.updatedAt,
    };
  }
}
