import { ConfigType } from '@nestjs/config';
import { Inject, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { UserTokenPayload } from '../payload/entities/user-token-payload.entity';

import jwtEnvs from './env.config';

// import type { UserTokenPayload } from 'src/@shared/types/token/user-token-payload';

@Injectable()
export class JWTStrategyService extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    @Inject(jwtEnvs.KEY)
    private readonly jwtEnvsModule: ConfigType<typeof jwtEnvs>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtEnvsModule.secret,
      // secretOrKey: authConfig().auth.secret,
    });
  }

  // public async validate(payload: UserTokenPayload): Promise<UserTokenPayload> {
  public async validate(payload: UserTokenPayload): Promise<UserTokenPayload> {
    return payload;
  }
}
