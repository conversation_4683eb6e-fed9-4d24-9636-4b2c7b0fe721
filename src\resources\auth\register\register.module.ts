import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';

import { RegisterService } from './register.service';

import { RegisterController } from './register.controller';

import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { HashModule } from '../jwt/hash/hash.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { LoginModule } from '../login/login.module';

@Module({
  imports: [PrismaModule, HashModule, LoginModule],
  controllers: [RegisterController],
  providers: [RegisterService],
})
export class RegisterModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('register');
  }
}
