import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AccountUsersService } from './account-users.service';
import { AccountUsersController } from './account-users.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule],
  controllers: [AccountUsersController],
  providers: [AccountUsersService],
})
export class AccountUsersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('accounts-users');
  }
}
