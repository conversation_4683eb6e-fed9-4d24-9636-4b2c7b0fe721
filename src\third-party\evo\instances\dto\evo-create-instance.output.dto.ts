import { InstanceQRCode } from '../entities/instance-qrcode.entity';

import { EVOIntegrationValues } from 'src/@shared/types/evo/integration';

/**
 * @description Pequeno gafanhoto esse DTO é a resposta enviada pelo EVO quando uma nova instância é criada.
 */
export class EvoCreateInstanceOutputDto {
  instance: {
    instanceName: string;
    instanceId: string;
    integration: EVOIntegrationValues;
    webhookWaBusiness: string | null;
    accessTokenWaBusiness: string;

    // Infelizmente não está tipado no código fonte. Não da pra saber o que vem. Por isso string.
    status: string;
  };

  hash: string;
  // 'webhook': {};
  // 'websocket': {};
  // 'rabbitmq': {};
  // 'sqs': {};

  // settings: {
  //   rejectCall: boolean;
  //   msgCall: string;
  //   groupsIgnore: boolean;
  //   alwaysOnline: boolean;
  //   readMessages: boolean;
  //   readStatus: boolean;
  //   syncFullHistory: boolean;
  // };

  qrcode?: InstanceQRCode;
}
