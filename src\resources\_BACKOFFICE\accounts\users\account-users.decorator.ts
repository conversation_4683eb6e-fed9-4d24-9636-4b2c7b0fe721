import {
  createParamDecorator,
  ExecutionContext,
  NotFoundException,
} from '@nestjs/common';


import { AccountUserDecoratorOutput } from './account-users.contract';

interface AccountUsersDecoratorProps extends Headers {
  userName?: string;
  accountSecureId?: string;
}

export const AccountUsersDecorator = createParamDecorator(
  async (
    data: any,
    ctx: ExecutionContext,
  ): Promise<AccountUserDecoratorOutput> => {
    const request = ctx.switchToHttp().getRequest();
    const query: AccountUsersDecoratorProps = request.query;

    const prisma = request.prismaService;

    let userName: string | undefined;
    let accountId: number | undefined;

    if (!query?.['userName']) {
      userName = undefined;
    } else {
      const user = await prisma.users.findMany({
        where: {
          name: { equals: query['userName'] },
        },

        select: {
          id: true,
        },
      });
      if (!userName) {
        throw new NotFoundException('Usuário não encontrado!');
      }

      userName = user.name;
    }

    if (!query?.['accountSecureId']) {
      accountId = undefined;
    } else {
      const account = await prisma.accounts.findFirst({
        where: {
          secureId: { equals: query['accountSecureId'] },
        },

        select: {
          id: true,
        },
      });
      if (!account) {
        throw new NotFoundException('Conta não encontrada!');
      }

      accountId = account.id;
    }

    return {
      userName,
      accountId,
    };
  },
);
