import { AccountSubscriptionWithPlanAndAccount } from '../account-subscriptions.contracts';
import { AccountSubscriptionOutputDto } from '../dto/account-subscription.output.dto';

export class AccountSubscriptionMapper {
  static fromAccountSubscriptionWithPlanToAccountSubscriptionOutputDto(
    accountSubscriptionWithPlan: AccountSubscriptionWithPlanAndAccount,
  ): AccountSubscriptionOutputDto {
    return {
      secureId: accountSubscriptionWithPlan.secureId,
      cycle: accountSubscriptionWithPlan.cycle,
      status: accountSubscriptionWithPlan.status,
      type: accountSubscriptionWithPlan.type,

      plan: {
        secureId: accountSubscriptionWithPlan.plan.secureId,
        name: accountSubscriptionWithPlan.plan.name,
        isActive: accountSubscriptionWithPlan.plan.isActive,
        createdAt: accountSubscriptionWithPlan.plan.createdAt,
        updatedAt: accountSubscriptionWithPlan.plan.updatedAt,
      },

      account: {
        secureId: accountSubscriptionWithPlan.account.secureId,
        companyName: accountSubscriptionWithPlan.account.companyName,
        isActive: accountSubscriptionWithPlan.account.isActive,
        isDeleted: accountSubscriptionWithPlan.account.isDeleted,
        createdAt: accountSubscriptionWithPlan.account.createdAt,
        updatedAt: accountSubscriptionWithPlan.account.updatedAt,
      },

      gatewaySubscriptionId: accountSubscriptionWithPlan.gatewaySubscriptionId,
      startsAt: accountSubscriptionWithPlan.startsAt,
      endsAt: accountSubscriptionWithPlan.endsAt,
      canceledAt: accountSubscriptionWithPlan.canceledAt,
      createdAt: accountSubscriptionWithPlan.createdAt,
      updatedAt: accountSubscriptionWithPlan.updatedAt,
    };
  }
}
