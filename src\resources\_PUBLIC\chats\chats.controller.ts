import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Res,
} from '@nestjs/common';
import { ChatsService } from './chats.service';
import { Response } from 'express';
import { ChatConversationInputDto } from './dto/chat-conversation.dto';
import { IterableReadableStream } from '@langchain/core/utils/stream';

@Controller('chats')
export class ChatsController {
  constructor(private readonly chatsService: ChatsService) {}

  @Post(':secureId')
  @HttpCode(200)
  async chat(
    @Param('secureId') chatSecureId: string,
    @Body() chatConversationInputDto: ChatConversationInputDto,
    @Res() res: Response,
  ) {
    try {
      res.setHeader('X-Vercel-AI-Data-Stream', 'v1');
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');

      // Get the latest message sent by the user
      const lastMessage =
        chatConversationInputDto.messages[
          chatConversationInputDto.messages.length - 1
        ];

      // Ensure session exists, create if it doesn't
      const { isAIResponder, sessionSecureId } =
        await this.chatsService.ensureSession({
          chatSecureId,
          sessionSecureId: chatConversationInputDto.sessionSecureId,
          userSecureId: chatConversationInputDto.userSecureId,
        });

      // Create user message and send to websocket for real-time updates
      await this.chatsService.createMessage({
        receiveMessage: lastMessage.content,
        messageDirection: 'received',
        sessionSecureId,
      });

      // Handle AI responder
      if (isAIResponder) {
        const { generatedAIAnswer, attachmentMessage, customerInfoRequest } =
          await this.chatsService.sendToProcessAIMessage({
            chatSessionSecureId: sessionSecureId,
            message: lastMessage.content,
          });

        const tokenUsage = {
          inputToken: 0,
          outputToken: 0,
        };

        // Send event to signal the start of AI response

        let aiMessage = '';
        // Stream AI response with typing effect
        if (generatedAIAnswer) {
          for await (const chunk of generatedAIAnswer) {
            res.write(`0:${JSON.stringify(chunk.content)}\n`);
            aiMessage += chunk.content;
            if (chunk.usage_metadata) {
              tokenUsage.inputToken += chunk.usage_metadata.input_tokens;
              tokenUsage.outputToken += chunk.usage_metadata.output_tokens;
            }
            await new Promise((resolve) => setTimeout(resolve, 60));
          }
          res.write(`e:{"finishReason":"other"}\n`);
        }

        // Send attachment message if available
        if (attachmentMessage) {
          res.write(`0:${JSON.stringify(attachmentMessage)}\n`);
          //adicionar o message anotattion part para informar no front q é mensagem de anexo, e nao ficar com avatar
          res.write(`e:{"finishReason":"other"}\n`);
        }

        const customerRequestTokenUsage = {
          inputToken: 0,
          outputToken: 0,
        };

        // Send customer info request if available
        let customerRequestMessage = '';
        if (customerInfoRequest) {
          if (customerInfoRequest instanceof IterableReadableStream) {
            for await (const chunk of customerInfoRequest) {
              res.write(`0:${JSON.stringify(chunk.content)}\n`);
              customerRequestMessage += chunk.content;
              if (chunk.usage_metadata) {
                customerRequestTokenUsage.inputToken +=
                  chunk.usage_metadata.input_tokens;
                customerRequestTokenUsage.outputToken +=
                  chunk.usage_metadata.output_tokens;
              }
              await new Promise((resolve) => setTimeout(resolve, 60));
            }

            res.write(`e:{"finishReason":"other"}\n`);
          } else {
            customerRequestMessage = customerInfoRequest;
            res.write(`0:"${customerInfoRequest}"\n`);
            res.write(`e:{"finishReason":"other"}\n`);
          }
        }

        await this.chatsService.createMessage({
          sendMessage: aiMessage,
          messageDirection: 'sent',
          sessionSecureId,
          inputToken: tokenUsage.inputToken,
          outputToken: tokenUsage.outputToken,
        });

        if (attachmentMessage) {
          await this.chatsService.createMessage({
            sendMessage: attachmentMessage,
            messageDirection: 'sent',
            sessionSecureId,
          });
        }

        if (customerInfoRequest) {
          await this.chatsService.createMessage({
            sendMessage: customerRequestMessage,
            messageDirection: 'sent',
            sessionSecureId,
            inputToken: customerRequestTokenUsage.inputToken,
            outputToken: customerRequestTokenUsage.outputToken,
          });
        }

        await this.chatsService.saveTokenUsageInDb(
          chatSecureId,
          tokenUsage.inputToken + customerRequestTokenUsage.inputToken,
          tokenUsage.outputToken + customerRequestTokenUsage.outputToken,
        );

        res.end();
        return;
      }

      res.write('event: error\n');
      res.write(
        'data: O tipo de chatbot está indisponível no momento. Por favor, tente novamente mais tarde.\n\n',
      );
      res.end();
      return;
    } catch (e) {
      console.error('Chat error:', e);
      res.write('event: error\n');
      res.write(
        `data: ${e.message || 'Ocorreu um erro ao receber a mensagem, entre em contato com o suporte!'}\n\n`,
      );
      res.end();
    }
  }

  @Get(':secureId')
  @HttpCode(200)
  async findOne(@Param('secureId') secureId: string) {
    return await this.chatsService.findOne(secureId);
  }
}
