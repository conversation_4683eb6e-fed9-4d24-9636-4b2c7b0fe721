import { Prisma, ChatSessions as SessionModel } from '@prisma/client';
import { Session } from '../entities/session.entity';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';
import { SessionOutputDTO } from '../dto/chat-session-output';
import { SessionsMessagesOutputDTO } from '../dto/chat-sessions-messages-output';

type SessionModelWithRelations = Prisma.ChatSessionsGetPayload<{
  include: {
    chat: true;
    account: true;
  };
}>;

type SessionModelWithMessages = Prisma.ChatSessionsGetPayload<{
  include: {
    chatMessages: {
      orderBy: {
        createdAt: 'asc';
      };
      select: {
        secureId: true;
        sendMessage: true;
        receiveMessage: true;
        createdAt: true;
        messageDirection: true;
        messageType: true;
        upload: {
          select: {
            secureId: true;
            urlCdn: true;
          };
        };
        userAccount: {
          select: {
            user: {
              select: {
                name: true;
              };
            };
          };
        };
        replyTo: {
          select: {
            secureId: true;
          };
        };
      };
    };
    chat: {
      include: {
        chatbot: true;
      };
    };
  };
}>;

export class SessionFactory {
  static convertModelToOutput(
    model: SessionModelWithRelations,
  ): SessionOutputDTO {
    return {
      isAIResponder: model.isAIResponder,
      secureId: model.secureId,
      chatSecureId: model.chat.secureId,
      accountSecureId: model.account.secureId,
      isFinalized: model.isFinalized,
    };
  }

  static convertModelToMessagesOutput(
    model: SessionModelWithMessages,
  ): SessionsMessagesOutputDTO[] {
    return model.chatMessages.map((message) => ({
      secureId: message.secureId,
      sendMessage: message.sendMessage,
      type: message.messageType,
      urlFile: message.upload?.urlCdn,
      messageDirection: message.messageDirection,
      receiveMessage: message.receiveMessage,
      createdAt: message.createdAt,
      attendantName:
        message.userAccount?.user?.name || model.chat?.chatbot?.name || null,
      replyTo: message.replyTo?.secureId || null,
    }));
  }
}
