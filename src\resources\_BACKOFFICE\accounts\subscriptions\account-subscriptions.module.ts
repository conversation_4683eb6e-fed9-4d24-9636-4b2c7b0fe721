import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AccountSubscriptionsService } from './account-subscriptions.service';
import { AccountSubscriptionsController } from './account-subscriptions.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { EfiSubscriptionsModule } from 'src/third-party/efi/subscriptions/efi-subscriptions.module';

@Module({
  imports: [PrismaModule, EfiSubscriptionsModule],
  controllers: [AccountSubscriptionsController],
  providers: [AccountSubscriptionsService],
})
export class AccountSubscriptionsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('accounts-subscriptions');
  }
}
