import { BadRequestException, Inject, Injectable } from '@nestjs/common';

import hasEnvs from './env.config';
import { ConfigType } from '@nestjs/config';
import { compare, genSalt, hash } from 'bcrypt';

@Injectable()
export class HashService {
  private saltRounds: number;

  constructor(
    @Inject(hasEnvs.KEY)
    private readonly hashEnvModule: ConfigType<typeof hasEnvs>,
  ) {
    this.saltRounds = this.hashEnvModule.rounds;
  }

  public async generate(password: string): Promise<string> {
    if (!password) {
      throw new BadRequestException('A senha não pode ser vazia');
    }

    const salt = await genSalt(this.saltRounds);
    const hashedPassword = await hash(password, salt);

    return hashedPassword;
  }

  public async compare(password: string, hash: string): Promise<boolean> {
    if (!password) {
      throw new BadRequestException('A senha não pode ser vazia');
    }

    if (!hash) {
      throw new BadRequestException('O hash não pode ser vazio');
    }

    return await compare(password, hash);
  }
}
