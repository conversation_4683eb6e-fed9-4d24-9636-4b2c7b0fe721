import {
  CanActivate,
  ConflictException,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o usuário pode alterar o seu CPF, verificando se o CPF não está sendo usado por outro usuário.
 */
@Injectable()
export class DoesUserCanChangeCPFGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;
    const body = context.switchToHttp().getRequest().body;
    if (!body.cpf) {
      return true;
    }

    const userWithSameCPF = await this.prismaService.users.findFirst({
      where: {
        cpf: { equals: body.cpf.replace(/\D/g, '') },
        secureId: { not: user.subject },
      },
    });
    if (userWithSameCPF) {
      throw new ConflictException(
        'CPF já está sendo utilizado por outro usuário',
      );
    }

    return true;
  }
}
