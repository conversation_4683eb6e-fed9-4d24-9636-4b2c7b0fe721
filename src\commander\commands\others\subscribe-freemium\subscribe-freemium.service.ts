import { Injectable } from "@nestjs/common";
import { PrismaClient } from "@prisma/client";
import { v4 as uuid } from 'uuid';

@Injectable()
export class SubscribeFreemiumService {
  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Assinando plano freemium:`);

    const accounts = await prisma.accounts.findMany({
      where: {
        isDeleted: false,
        isActive: true,
        subscriptions: {
          none: {
            isActive: true,
          },
        },
      },
      select: {
        id: true,
        secureId: true,
        subscriptions: {
          select: {
            id: true,
            isActive: true,
          },
        },
      },
    });

    const totalAccounts = accounts.length;

    if (totalAccounts === 0) {
      console.log('\x1b[32m', `Nenhuma conta elegível encontrada.`);
      return;
    }

    let processedAccounts = 0;

    const freePlan = await prisma.plans.findFirst({
      where: {
        slug: 'freemium',
        isActive: true,
      },
    });

    if (!freePlan) {
      console.log('\x1b[32m', `Plano gratuito não encontrado.`);
      return;
    }

    // Initial progress bar
    this.updateProgressBar(processedAccounts, totalAccounts);

    for (const account of accounts) {
      await prisma.subscriptions.create({
        data: {
          secureId: uuid(),
          accountId: account.id,
          planId: freePlan.id,
          remainingSessions: freePlan.iaMessagesLimit,
          cycle: 1,
          status: 'active',
          type: 'free',
          gatewaySubscriptionId: null,
          startsAt: new Date(),
          endsAt: new Date(
            new Date().setFullYear(new Date().getFullYear() + 10),
          ),
          trialEndsAt: null,
          isActive: true,
        },
      });

      // Update progress after each account is processed
      processedAccounts++;
      this.updateProgressBar(processedAccounts, totalAccounts);
    }

    console.log('\n\x1b[32m', `Concluído! ${totalAccounts} contas atualizadas.`);
  }

  private updateProgressBar(current: number, total: number): void {
    const percentage = Math.floor((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.floor((percentage * barLength) / 100);
    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(barLength - filledLength);

    process.stdout.write(
      `\r[${filledBar}${emptyBar}] ${percentage}% (${current}/${total})`,
    );
  }
}