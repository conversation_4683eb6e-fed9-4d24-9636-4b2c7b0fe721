import { CPFMask } from "src/@shared/helpers/mask/cpf.mask";
import { AccountUserWithUserAccount } from "../account-users.contract";
import { AccountUserOutputDto } from "../dto/get-account-user.dto";
import { PhoneMask } from "src/@shared/helpers/phone/phone.mask";

export class AccountUsersFactory {
  static createAccountUserOutputFromAccountUserWithUsersAccountBatch(
    input: AccountUserWithUserAccount[],
  ): AccountUserOutputDto[] {
    return input.map((accountUserWithUsersAccount) => {
      const userAccount = accountUserWithUsersAccount.usersAccounts[0];
      
      // Verificar permissões
      const hasAllPermissions = userAccount.accountsPermissions?.some(
        (accPer) =>
          [
            'app_view',
            'app_create',
            'app_edit',
            'app_delete',
          ].includes(accPer.permission.slug),
      ) || false;

      const permissions = userAccount.accountsPermissions?.map(
        (per) => per.permission.secureId,
      ) || [];

      return {
        secureId: accountUserWithUsersAccount.secureId,
        name: accountUserWithUsersAccount.name,
        email: accountUserWithUsersAccount.email,
        cpf: CPFMask.apply(accountUserWithUsersAccount.cpf),
        isOwner: userAccount.isOwner,
        cellphone: accountUserWithUsersAccount.cellPhone ? PhoneMask.apply(accountUserWithUsersAccount.cellPhone) : null,
        isActive: accountUserWithUsersAccount.isActive,
        isDeleted: accountUserWithUsersAccount.isDeleted,
        hasAllPermissions: hasAllPermissions,
        permissions: !hasAllPermissions ? permissions : undefined,
        createdAt: accountUserWithUsersAccount.createdAt,
        updatedAt: accountUserWithUsersAccount.updatedAt,
      };
    });
  }
}
