import { Entity } from 'src/@shared/contracts/entity/interface';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';

import { v4 as uuidV4 } from 'uuid';

type Messages = {
  secureId: string;
  sendMessage: string;
  receiveMessage: string;
  createdAt: Date;
  userAccounts: userAccount;
};

type User = {
  name: string;
};

type userAccount = {
  secureId: string;
  user: User;
};

type Account = {
  secureId: string;
};

type Chat = {
  secureId: string;
};

type WhatsApp = {
  secureId: string;
  phoneNumber: string;
  instanceName: string;
  instanceId: string;
};

type SessionsConstructosProps = {
  id?: number;
  secureId?: string;
  accountId: number;

  chatId?: number;
  whatsappId?: number;

  source: number;
  customerId: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  customerDocument?: string;

  sessionDescription?: string;

  isLeadCaptured?: boolean;
  isAIResponder?: boolean;
  isActive?: boolean;
  isDeleted?: boolean;

  createdAt?: Date;
  updatedAt?: Date;

  chatMessages?: Messages[];
  account?: Account;
  chat?: Chat;
  whatsapp?: WhatsApp;
};

type SessionJson = {
  secureId: string;

  customerId: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  source: string;
  customerDocument?: string;

  sessionDescription?: string;

  isAIResponder: boolean;
  isLeadCaptured?: boolean;
  isActive: boolean;
  isDeleted: boolean;

  chatMessages?: Messages[];
  account?: Account;
  chat?: Chat;
  whatsapp?: WhatsApp;

  createdAt: Date;
  updatedAt: Date;
};

export class Session implements Entity {
  id: number;
  secureId: string;
  accountId: number;
  chatId: number | null;
  whatsappId: number | null;

  senderId: string;
  senderName: string;

  source: number;
  customerId: string;
  customerName: string | null;
  customerEmail: string | null;
  customerPhone: string | null;
  customerDocument: string | null;

  sessionDescription: string | null;

  isAIResponder: boolean | null;
  isLeadCaptured: boolean;
  isActive: boolean;
  isDeleted: boolean;

  chatMessages?: Messages[];
  account?: Account;
  chat?: Chat;
  whatsapp?: WhatsApp;

  createdAt: Date;
  updatedAt: Date;

  constructor(props: SessionsConstructosProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.accountId = props.accountId;
    this.chatId = props?.chatId ? props.chatId : undefined;
    this.whatsappId = props?.whatsappId ? props.whatsappId : undefined;

    this.customerId = props.customerId;
    this.source = props.source;
    this.customerName = props?.customerName ? props.customerName : null;
    this.customerEmail = props?.customerEmail ? props.customerEmail : null;
    this.customerPhone = props?.customerPhone ? props.customerPhone : null;
    this.customerDocument = props?.customerDocument
      ? props.customerDocument
      : null;

    this.sessionDescription = props?.sessionDescription
      ? props.sessionDescription
      : null;

    this.isAIResponder = props?.isAIResponder === true ? true : false;
    this.isLeadCaptured = props?.isLeadCaptured === true ? true : false;
    this.isActive = props?.isActive === false ? false : true;
    this.isDeleted = props?.isDeleted === true ? true : false;

    this.chatMessages = props?.chatMessages ? props.chatMessages : undefined;
    this.account = props?.account ? props.account : undefined;
    this.chat = props?.chat ? props.chat : undefined;
    this.whatsapp = props?.whatsapp ? props.whatsapp : undefined;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  delete(): void {
    this.isDeleted = true;
  }

  restore(): void {
    this.isDeleted = false;
  }

  deactivate(): void {
    this.isActive = false;
  }

  active(): void {
    this.isActive = true;
  }

  toJSON(): SessionJson {
    return {
      secureId: this.secureId,
      customerId: this.customerId,
      customerName: this.customerName,
      customerEmail: this.customerEmail,
      customerDocument: this.customerDocument,
      customerPhone: this.customerPhone,
      sessionDescription: this.sessionDescription,
      source: ChatSourceEnum[this.source],

      isAIResponder: this.isAIResponder,
      isLeadCaptured: this.isLeadCaptured,
      isActive: this.isActive,
      isDeleted: this.isDeleted,

      chatMessages: this.chatMessages,
      account: this.account,
      chat: this.chat,
      whatsapp: this.whatsapp,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
