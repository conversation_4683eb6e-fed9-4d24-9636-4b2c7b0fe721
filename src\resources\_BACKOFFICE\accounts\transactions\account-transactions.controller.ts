import { Controller, Get, Param, UseGuards } from '@nestjs/common';

import { AccountTransactionsService } from './account-transactions.service';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';

import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { AccountTransactionDecorator } from './account-transaction.decorator';
import { AccountTransactionDecoratorOutput } from './account-transaction.contract';

@Controller('accounts-transactions')
export class AccountTransactionsController {
  constructor(
    private readonly transactionsService: AccountTransactionsService,
  ) { }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountTransactionDecorator()
    accountTransactionQuery: AccountTransactionDecoratorOutput,
  ) {
    return await this.transactionsService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountTransactionQuery,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Get(':secureId')
  async findOne(@Param('secureId') secureId: string) {
    return await this.transactionsService.findOne(secureId);
  }
}
