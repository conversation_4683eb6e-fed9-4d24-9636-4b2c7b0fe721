import { Injectable, NotFoundException } from '@nestjs/common';
import { format } from 'date-fns';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { SubscriptionSessionService } from 'src/@shared/services/subscription-session/subscription-session.service';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';
import { ChatsGateway } from 'src/resources/chats/chats.gateway';
import { ChatMapper } from 'src/resources/chats/mapper/chat.mapper';
import { CreateMessageInputDTO } from 'src/resources/chats/messages/dto/create-message.dto';
import { MessageFactory } from 'src/resources/chats/messages/factory/message.factory';
import { CreateSessionInputDTO } from 'src/resources/chats/sessions/dto/create-session.dto';
import { SessionFactory } from 'src/resources/chats/sessions/factory/session.factory';
import { AIMessageService } from 'src/resources/ia/ai-message/ai-message.service';
import ProcessMessageInputDTO from 'src/resources/ia/ai-message/dto/process-message-input.dto';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class ChatsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly aiMessageService: AIMessageService,
    private readonly chatGateway: ChatsGateway,
    private readonly subscriptionSessionService: SubscriptionSessionService,
  ) {}

  async findOne(id: string) {
    const chat = await this.prismaService.chats.findFirst({
      where: {
        secureId: id,
      },
      include: {
        account: {
          select: {
            secureId: true,
          },
        },
        upload: {
          select: {
            secureId: true,
            urlCdn: true,
          },
        },
        chatbot: {
          select: {
            secureId: true,
            name: true,
            isAI: true,
          },
        },
      },
    });

    if (!chat) {
      throw new NotFoundException('Chat não encontrado');
    }

    return ChatMapper.toEntity(chat);
  }

  async ensureSession({
    sessionSecureId,
    userSecureId,
    chatSecureId,
  }: {
    sessionSecureId: string;
    userSecureId: string;
    chatSecureId: string;
  }) {
    const searchChat = await this.prismaService.chats.findUnique({
      where: {
        secureId: chatSecureId,
      },
      include: {
        account: {
          select: {
            secureId: true,
          },
        },
        chatbot: {
          select: {
            secureId: true,
            isAI: true,
          },
        },
      },
    });

    const {
      chatbot: { isAI },
      account,
    } = searchChat;

    if (!searchChat) {
      throw new NotFoundException('Identificador da conversa inválido');
    }

    const session = await this.prismaService.chatSessions.findUnique({
      where: {
        secureId: sessionSecureId,
      },
    });

    // If session doesn't exist OR session is finalized, create a new session
    if (!session || session.isFinalized) {
      const newSession = await this.startNewSession({
        secureId: sessionSecureId,
        accountId: account.secureId,
        chatId: chatSecureId,
        customerId: userSecureId,
        isAI,
      });

      if (!newSession) {
        throw new Error('Erro ao criar nova sessão');
      }
      return {
        sessionSecureId: newSession.secureId,
        isAIResponder: newSession.isAIResponder,
      };
    }

    return {
      sessionSecureId: session.secureId,
      isAIResponder: session.isAIResponder,
    };
  }

  async startNewSession(createSessionInputDTO: CreateSessionInputDTO) {
    const senderName = createSessionInputDTO.customerName
      ? createSessionInputDTO.customerName
      : `Usuário ${format(new Date(), 'dd/MM/yy HH:mm')}`;

    if (createSessionInputDTO.isAI) {
      const hasRemainingIASessions =
        await this.subscriptionSessionService.hasRemainingSessions({
          accountSecureId: createSessionInputDTO.accountId,
        });

      const hasRemainingFreemiumTrialDays =
        await this.subscriptionSessionService.hasRemainingFreemiumTrialDays({
          accountSecureId: createSessionInputDTO.accountId,
        });

      if (!hasRemainingIASessions || !hasRemainingFreemiumTrialDays) {
        createSessionInputDTO.isAI = false;
      }
    }

    const session = await this.prismaService.chatSessions.create({
      data: {
        source: ChatSourceEnum.webchat,
        secureId: createSessionInputDTO.secureId,
        account: {
          connect: {
            secureId: createSessionInputDTO.accountId,
          },
        },
        customerId: createSessionInputDTO.customerId,
        customerName: senderName,
        isAIResponder: createSessionInputDTO.isAI,
        chat: {
          connect: {
            secureId: createSessionInputDTO.chatId,
          },
        },
      },
    });

    if (session.isAIResponder) {
      await this.subscriptionSessionService.decrementRemainingSession({
        accountSecureId: createSessionInputDTO.accountId,
      });
    }

    return SessionFactory.convertFromModelToSession(session);
  }

  async createMessage(createMessageInputDTO: CreateMessageInputDTO) {
    var chatBot: any;

    // Se chatBotId foi fornecido explicitamente, use-o
    if (!!createMessageInputDTO.chatBotId) {
      chatBot = {
        connect: {
          secureId: createMessageInputDTO.chatBotId,
        },
      };
    } else {
      // Caso contrário, busque automaticamente baseado na sessão
      const chatBotId = await this.getChatBotIdFromSession(createMessageInputDTO.sessionSecureId);
      if (chatBotId) {
        chatBot = {
          connect: {
            id: chatBotId,
          },
        };
      }
    }

    const message = await this.prismaService.chatMessages.create({
      data: {
        secureId: uuidV4(),
        receiveMessage: createMessageInputDTO.receiveMessage,
        sendMessage: createMessageInputDTO.sendMessage,
        inputToken: createMessageInputDTO.inputToken,
        messageDirection: createMessageInputDTO.messageDirection,
        outputToken: createMessageInputDTO.outputToken,
        chatSession: {
          connect: {
            secureId: createMessageInputDTO.sessionSecureId,
          },
        },
        messageType: 'text',
        chatBot,
      },
      include: {
        userAccount: {
          select: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        replyTo: true,
      },
    });

    await this.prismaService.chatSessions.update({
      where: {
        secureId: createMessageInputDTO.sessionSecureId,
      },
      data: {
        isRead: createMessageInputDTO.receiveMessage ? true : false,
      },
    });

    this.chatGateway.broadcastChatMessageUpdate({
      createdAt: message.createdAt,
      receiveMessage: message.receiveMessage,
      sendMessage: message.sendMessage,
      messageDirection: message.messageDirection,
      secureId: message.secureId,
      sessionId: createMessageInputDTO.sessionSecureId,
    });

    return MessageFactory.convertFromModelToMessage(message);
  }

  /**
   * Busca o chatBotId baseado no sessionSecureId seguindo a relação:
   * chat_session_id > chat_id > chatbot_id
   */
  private async getChatBotIdFromSession(sessionSecureId: string): Promise<number | null> {
    const session = await this.prismaService.chatSessions.findUnique({
      where: { secureId: sessionSecureId },
      select: {
        chat: {
          select: {
            chatbotId: true,
          },
        },
        whatsapp: {
          select: {
            chatbotId: true,
          },
        },
      },
    });

    if (!session) {
      return null;
    }

    // Prioriza chatbot do chat, depois do whatsapp
    return session.chat?.chatbotId || session.whatsapp?.chatbotId || null;
  }

  async sendToProcessAIMessage({
    chatSessionSecureId,
    message,
  }: ProcessMessageInputDTO) {
    return this.aiMessageService.processMessage({
      chatSessionSecureId,
      message,
    });
  }

  async saveTokenUsageInDb(
    chatSecureId: string,
    inputToken: number,
    outputToken: number,
  ) {
    const chat = await this.prismaService.chats.findFirst({
      where: {
        secureId: chatSecureId,
      },
      select: {
        chatbot: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!chat) {
      throw new NotFoundException('Chat não encontrado');
    }

    const updateChatBot = await this.prismaService.chatBots.update({
      data: {
        inputToken: {
          increment: inputToken,
        },
        outputToken: {
          increment: outputToken,
        },
      },
      where: {
        id: chat.chatbot.id,
      },
    });
  }
}
