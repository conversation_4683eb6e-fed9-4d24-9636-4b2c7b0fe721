import {
  CanActivate,
  ConflictException,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { UpdateUserDto } from '../dto/update-user.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

@Injectable()
export class BackofficeUsersUpdateCustomGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const updateUserSecureId = request.params.secureId;
    const body: UpdateUserDto = request.body;

    if (body?.email) {
      const emailExists = await this.prismaService.users.findFirst({
        where: {
          email: { equals: body.email },
          secureId: { not: updateUserSecureId },
        },
      });

      if (emailExists) {
        throw new ConflictException(
          'Este e-mail já está em uso por outro usuário',
        );
      }
    }

    if (body?.cpf) {
      const cpfExists = await this.prismaService.users.findFirst({
        where: {
          cpf: { equals: body.cpf.replace(/\D/g, '') },
          secureId: { not: updateUserSecureId },
        },
      });

      if (cpfExists) {
        throw new ConflictException(
          'Este CPF já está em uso por outro usuário',
        );
      }
    }

    return true;
  }
}
