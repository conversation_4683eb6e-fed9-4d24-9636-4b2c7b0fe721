import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PermissionSlug } from 'src/@shared/types/permissions';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

type CustomAccountPermissionSlug = {
  accountPermissionSlug: PermissionSlug;
};

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly prismaService: PrismaService,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const permissions = this.reflector.get<string>(
      'permissions',
      context.getHandler(),
    );
    if (!permissions || permissions.length < 1) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user; // Payload do token JWT

    // NOTE: <PERSON><PERSON><PERSON><PERSON>, deixei o código comentado para fins de histórico, uma vez que esse código não subiu para o git. Criei uma única chamada SQL para buscar os slugs das permissões do usuário ativo.
    // const accountModel = await this.prisma.accounts.findFirst({
    //   where: { secureId: { equals: user.activeAccount.accountSecureId } },
    //   select: { id: true },
    // });

    // const userModel = await this.prisma.users.findFirst({
    //   where: {
    //     secureId: user.subject,
    //   },

    //   select: {
    //     usersAccounts: {
    //       where: {
    //         accountId: accountModel.id,
    //       },

    //       select: {
    //         accountsPermissions: {
    //           select: {
    //             permission: {
    //               select: {
    //                 slug: true,
    //               },
    //             },
    //           },
    //         },
    //       },
    //     },
    //   },
    // });

    const accountPermissionsSlugModel: CustomAccountPermissionSlug[] =
      await this.prismaService.$queryRaw`
      SELECT permissions.slug as accountPermissionSlug
        FROM users

        JOIN users_accounts ON users.id = users_accounts.id
        JOIN accounts_permissions ON users_accounts.id = accounts_permissions.user_account_id
        JOIN permissions ON accounts_permissions.permission_id = permissions.id

        WHERE users.secure_id = ${user.subject}
    `;

    const accountPermissionsSlugs = accountPermissionsSlugModel.map(
      (permission: any) => permission.accountPermissionSlug,
    );

    // NOTE: Aqui rola um bypass para a role MASTER
    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }
    return accountPermissionsSlugs.some((permission: PermissionSlug) =>
      permissions.includes(permission),
    );
  }
}
