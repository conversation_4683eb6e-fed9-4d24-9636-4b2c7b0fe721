@apiBaseUrl = http://localhost:3333
@loginEndPoint = /login
@loginApi = {{apiBaseUrl}}{{loginEndPoint}}

@knowledgeEndPoint = /knowledge-base

@chatbotApi = {{apiBaseUrl}}{{knowledgeEndPoint}}

### Login
# @name login
POST {{loginApi}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "3z2io23m"
}

### Create Knowledge Base
#  @name post_knowledge
GET {{chatbotApi}}/de0a879d-0391-4c20-a67d-cd0dcbce82e9
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

