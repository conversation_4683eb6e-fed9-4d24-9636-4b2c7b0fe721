import {
  BadRequestException,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

@Injectable()
export class PreventUserSelfAccountDeletionGuard {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;
    const params = context.switchToHttp().getRequest().params;
    if (!params.secureId) {
      throw new BadRequestException('Parâmetro secureId não encontrado');
    }

    if (params.secureId !== user.subject) {
      throw new ForbiddenException('Você não pode excluir sua própria conta');
    }

    return true;
  }
}
