const secureId = {
  description: 'SecureId do usuário',
  uniqueItems: true,
  type: String,
  example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
};

const name = {
  description: 'Nome do usuário',
  uniqueItems: false,
  type: String,
  example: '<PERSON> Doe',
};

const email = {
  description: 'Email do usuário',
  uniqueItems: false,
  type: String,
  example: 'john.marston@blackwaterbank.rdr2',
};

const cpf = {
  description: 'CPF do usuário',
  uniqueItems: false,
  type: String,
  example: '123.456.789-01',
};

const isActive = {
  description: 'Define se o usuário está ativo ou não',
  type: Boolean,
  example: true,
};

const createdAt = {
  description: 'Data de criação do usuário',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const updatedAt = {
  description: 'Data da última atualização do usuário',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const account = {
  secureId: {
    description: 'SecureId da conta',
    uniqueItems: true,
    type: String,
    example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
  },

  companyName: {
    description: 'Nome da empresa',
    uniqueItems: true,
    type: String,
    example: 'BlackWater Bank',
  },

  createdAt: {
    description: 'Data de criação da conta',
    type: Date,
    example: '2021-07-01T00:00:00.000Z',
  },

  updatedAt: {
    description: 'Data da última atualização da conta',
    type: Date,
    example: '2021-07-01T00:00:00.000Z',
  },

  permissions: {
    accountPermissionSecureId: {
      description:
        'SecureId da permissão da conta. (NÃO é o SecureId da permissão)',
      uniqueItems: true,
      type: String,
      example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
    },

    name: {
      description: 'Nome da permissão',
      type: String,
      example: 'user_edit',
    },

    slug: {
      description: 'Slug da permissão',
      type: String,
      example: 'USER_EDIT',
    },

    group: {
      description: 'Grupo da permissão',
      type: String,
      example: 'user',
    },

    description: {
      description: 'Descrição da permissão',
      type: String,
      example: 'Permite editar usuários',
    },

    createdAt: {
      description: 'Data de criação da permissão',
      type: Date,
      example: '2021-07-01T00:00:00.000Z',
    },

    updatedAt: {
      description: 'Data da última atualização da permissão',
      type: Date,
      example: '2021-07-01T00:00:00.000Z',
    },
  },
};

export const getUser = {
  secureId,
  name,
  email,
  cpf,
  isActive,

  account,

  createdAt,
  updatedAt,
};
