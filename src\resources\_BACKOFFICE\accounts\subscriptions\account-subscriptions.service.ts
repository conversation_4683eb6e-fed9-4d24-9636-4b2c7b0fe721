import { Injectable, NotFoundException } from '@nestjs/common';

import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';

import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';

import { AccountSubscriptionOutputDto } from './dto/account-subscription.output.dto';

import { AccountSubscriptionMapper } from './mapper/account-subscription.mapper';
import { AccountSubscriptionFactory } from './factory/account-subscription.factory';

import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { AccountSubscriptionDecoratorOutput } from './account-subscriptions.contracts';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { CreateAccountSubscriptionDto } from './dto/create-account-subscription.dto';

import { v4 as uuidV4 } from 'uuid';
import { EfiSubscriptionsService } from 'src/third-party/efi/subscriptions/efi-subscriptions.service';

@Injectable()
export class AccountSubscriptionsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly efiSubscriptionsService: EfiSubscriptionsService,
  ) {}

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountSubscriptionQuery: AccountSubscriptionDecoratorOutput,
  ): Promise<IWithPagination<AccountSubscriptionOutputDto>> {
    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,

      status: accountSubscriptionQuery.subscriptionStatus,
      type: accountSubscriptionQuery.subscriptionType,

      accountId: accountSubscriptionQuery.accountId,
      planId: accountSubscriptionQuery.planId,
    };

    const paginationHelper = new PaginationHelper<'Subscriptions'>({
      modelDelegate: this.prismaService.subscriptions,
      paginationQuery,
      whereClause: whereClause as never,
    });

    const subscriptionsModel = await this.prismaService.subscriptions.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      take: paginationHelper.take,
      skip: paginationHelper.skip,
      where: {
        ...whereClause,
      },

      include: {
        plan: {
          select: {
            secureId: true,
            name: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
        },

        account: {
          select: {
            secureId: true,
            companyName: true,
            isActive: true,
            isDeleted: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    const meta = await paginationHelper.getPaginationMeta();
    const data = !subscriptionsModel
      ? ([] as AccountSubscriptionOutputDto[])
      : AccountSubscriptionFactory.createAccountSubscriptionOutputDtoFromAccountWithPlanBatch(
          subscriptionsModel,
        );

    return {
      meta: meta,
      data: data,
    };
  }

  async findOne(
    subscriptionSecureId: string,
  ): Promise<AccountSubscriptionOutputDto> {
    const subscriptionModel = await this.prismaService.subscriptions.findFirst({
      where: {
        secureId: subscriptionSecureId,
      },

      include: {
        plan: {
          select: {
            secureId: true,
            name: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
        },

        account: {
          select: {
            secureId: true,
            companyName: true,
            isActive: true,
            isDeleted: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });
    if (!subscriptionModel) {
      throw new NotFoundException('Inscrição não encontrada!');
    }

    return AccountSubscriptionMapper.fromAccountSubscriptionWithPlanToAccountSubscriptionOutputDto(
      subscriptionModel,
    );
  }

  async create(createAccountSubscriptionDto: CreateAccountSubscriptionDto) {
    const planModel = await this.prismaService.plans.findFirst({
      where: {
        secureId: createAccountSubscriptionDto.planSecureId[0],
        isActive: true,
      },
    });

    if (!planModel) {
      throw new NotFoundException('Plano não encontrado!');
    }

    const accountModel = await this.prismaService.accounts.findFirst({
      where: {
        secureId: createAccountSubscriptionDto.accountSecureId,
        isActive: true,
      },
    });

    if (!accountModel) {
      throw new NotFoundException('Conta não encontrada!');
    }

    const activeSubscriptions = await this.prismaService.subscriptions.findMany({
      where: {
        accountId: accountModel.id,
        isActive: true,
      },
    });

    await this.prismaService.$transaction(async (prisma) => {
      for (const subscription of activeSubscriptions) {
        await prisma.subscriptions.update({
          where: {
            secureId: subscription.secureId,
          },
          data: {
            isActive: false,
            status: 'canceled',
            canceledAt: new Date(),
          },
        });
      }

      await prisma.subscriptions.create({
        data: {
          secureId: uuidV4(),
          cycle: 1,
          status: 'active',
          type: planModel.slug === 'freemium' ? 'free' : 'sponsored',
          plan: { connect: { id: planModel.id } },
          account: { connect: { id: accountModel.id } },
          remainingSessions: planModel.iaMessagesLimit,
          gatewaySubscriptionId: null,
          startsAt: new Date(),
          endsAt: new Date(
            new Date().setFullYear(new Date().getFullYear() + 10),
          ),
          isActive: true,
        },
      });
    });

    return;
  }

  async cancel(secureId: string) {
    const subscriptionModel = await this.prismaService.subscriptions.findFirst({
      where: {
        secureId: secureId,
      },
    });

    if (!subscriptionModel) {
      throw new NotFoundException('Assinatura não encontrada!');
    }

    if (subscriptionModel.gatewaySubscriptionId) {
      try {
        await this.efiSubscriptionsService.cancelSubscription(
          subscriptionModel.gatewaySubscriptionId,
        );
        console.log(`Assinatura ${subscriptionModel.gatewaySubscriptionId} cancelada na EFI com sucesso`);
      } catch (error) {
        console.error('Erro ao cancelar assinatura na EFI:', error);
      }
    }

    await this.prismaService.subscriptions.update({
      where: {
        secureId: secureId,
      },
      data: {
        isActive: false,
        status: 'canceled',
        canceledAt: new Date(),
      },
    });

    return {
      message: 'Assinatura cancelada com sucesso',
      canceledInGateway: !!subscriptionModel.gatewaySubscriptionId,
    };
  }
}
