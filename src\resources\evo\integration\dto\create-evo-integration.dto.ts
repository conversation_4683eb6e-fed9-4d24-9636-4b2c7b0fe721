import { Transform } from 'class-transformer';
import { HttpException } from '@nestjs/common';
import {
  IsBoolean,
  isNotEmpty,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateEvoIntegrationDto {
  @Transform(({ value }) => removeCharactersAndValidatePhone(value))
  @IsString({ message: 'instanceName deve ser uma string!' })
  @IsNotEmpty({ message: 'phoneNumber é obrigatório!' })
  phoneNumber: string;

  @IsBoolean({ message: 'isBusiness deve ser um valor booleano!' })
  @IsOptional()
  isBusiness?: boolean;

  @IsString({ message: 'token deve ser uma string!' })
  @IsOptional()
  token?: string;

  @IsString({ message: 'numberId deve ser uma string!' })
  @IsOptional()
  numberId?: string;

  @IsString({ message: 'businessId deve ser uma string!' })
  @IsOptional()
  businessId?: string;
}

function removeCharactersAndValidatePhone(value: string): string {
  const formattedValue = value.replace(/[^0-9]/g, '');

  if (formattedValue.length < 12 || formattedValue.length > 13) {
    throw new HttpException(
      {
        message: [
          {
            property: 'phoneNumber',
            message:
              'phoneNumber deve conter o código do país e o DDD, sendo entre 12 e 13 caracteres.',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
      400,
    );
  }

  return formattedValue;
}
