@apiBaseUrl = http://localhost:3333
@loginEndPoint = /login
@loginApi = {{apiBaseUrl}}{{loginEndPoint}}
@sessionsEndPoint = /chat-sessions
@sessionsAPI = {{apiBaseUrl}}{{sessionsEndPoint}}



### Login
# @name login
POST {{loginApi}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "3z2io23m"
}


### Get All Sessions
# @name getSessions
# GET {{sessionsAPI}}
# GET {{apiBaseUrl}}{{sessionsEndPoint}}
GET {{sessionsAPI}}?search=&page=1&limit=2
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}


### Get Session By Id
# @name getSessionById
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}
GET {{sessionsAPI}}/6fc92d04-d190-4d88-b836-38146d793c12

### Put Update Session
# @name putUpdateSession
PUT {{sessionsAPI}}/6fc92d04-d190-4d88-b836-38146d793c12
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

{
	"customerEmail": "<EMAIL>"
}
