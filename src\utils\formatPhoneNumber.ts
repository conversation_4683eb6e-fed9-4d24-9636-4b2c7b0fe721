/**
 * Formata um número de telefone para um padrão comum.
 * Prioriza formatos brasileiros com DDI 55, DDD e o nono dígito para celulares.
 * Exemplos:
 * - 5514999000283  -> +55 (14) 99900-0283
 * - 14999000283    -> (14) 99900-0283
 * - 999000283      -> 99900-0283
 * - 551432230000   -> +55 (14) 3223-0000 (8 dígitos)
 * - 1432230000     -> (14) 3223-0000 (8 dígitos)
 * - 32230000       -> 3223-0000 (8 dígitos)
 * @param phoneNumber String do número de telefone
 * @returns Número de telefone formatado ou o número limpo se o formato não for reconhecido.
 */
export function formatPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) {
    return '';
  }

  const cleaned = phoneNumber.replace(/\D/g, '');

  const length = cleaned.length;

  if (length === 13 && cleaned.startsWith('55')) {
    // +55 (XX) YYYYY-YYYY (celular com 9 dígitos)
    return `+${cleaned.substring(0, 2)} (${cleaned.substring(
      2,
      4,
    )}) ${cleaned.substring(4, 9)}-${cleaned.substring(9)}`;
  }
  if (length === 12 && cleaned.startsWith('55')) {
    // +55 (XX) XXXX-YYYY (fixo com 8 dígitos)
    return `+${cleaned.substring(0, 2)} (${cleaned.substring(
      2,
      4,
    )}) ${cleaned.substring(4, 8)}-${cleaned.substring(8)}`;
  }
  if (length === 11) {
    // (XX) YYYYY-YYYY (celular com 9 dígitos)
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(
      2,
      7,
    )}-${cleaned.substring(7)}`;
  }
  if (length === 10) {
    // (XX) XXXX-YYYY (fixo com 8 dígitos)
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(
      2,
      6,
    )}-${cleaned.substring(6)}`;
  }
  if (length === 9) {
    // YYYYY-YYYY (celular com 9 dígitos, sem DDD)
    return `${cleaned.substring(0, 5)}-${cleaned.substring(5)}`;
  }
  if (length === 8) {
    // XXXX-YYYY (fixo com 8 dígitos, sem DDD)
    return `${cleaned.substring(0, 4)}-${cleaned.substring(4)}`;
  }

  // Retorna o número limpo se não corresponder a nenhum formato esperado
  return cleaned;
}

export default formatPhoneNumber;
