import { Entity } from 'src/@shared/contracts/entity/interface';
import { v4 as uuid } from 'uuid';

type ContactConstructorProps = {
  id?: number;
  secureId?: string;
  accountId: number;
  name?: string;
  email?: string;
  phone?: string;
  document?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
};

type ContactJson = {
  secureId?: string;
  accountId: number;
  name?: string;
  email?: string;
  phone?: string;
  document?: string;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
};

export class Contact implements Entity {
  id?: number;
  secureId: string;
  accountId: number;
  name?: string;
  email?: string;
  phone?: string;
  document?: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(props: ContactConstructorProps) {
    this.id = props.id;
    this.secureId = props.secureId ?? uuid();
    this.accountId = props.accountId;
    this.name = props.name;
    this.email = props.email;
    this.phone = props.phone;
    this.document = props.document;
    this.isActive = props.isActive ?? true;
    this.isDeleted = props.isDeleted ?? false;
    this.createdAt = props.createdAt ?? new Date();
    this.updatedAt = props.updatedAt ?? new Date();
  }

  toJSON() {
    return {
      secureId: this.secureId,
      accountId: this.accountId,
      name: this.name,
      email: this.email,
      phone: this.phone,
      document: this.document,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    } as ContactJson;
  }
}
