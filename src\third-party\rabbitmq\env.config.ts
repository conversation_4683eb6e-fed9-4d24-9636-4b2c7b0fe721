import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export interface IRabbitMQEnvs {
  uri: string;
}

export default registerAs('rabbitmq-envs', () => {
  const values: IRabbitMQEnvs = {
    uri: process.env.RABBITMQ_URI,
  };

  const schema = Joi.object<IRabbitMQEnvs>({
    uri: Joi.string().required().messages({
      'any.required': 'ENV: RABBITMQ_URI is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });
  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?\n${error.message}`,
    );
  }

  return values;
});
