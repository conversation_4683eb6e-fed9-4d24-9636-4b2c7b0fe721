import { IsEmail, IsOptional, <PERSON><PERSON>ength, <PERSON><PERSON>ength } from 'class-validator';
import { IsCPF } from 'src/@shared/decorators/validation/cpf/cpf.decorator';

export class UpdateSessionDto {
  @MaxLength(255, {
    message: 'O campo name deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O campo name deve ter no mínimo 3 caracteres',
  })
  @IsOptional()
  customerName?: string;

  @IsEmail({}, { message: 'O email informado é inválido' })
  @IsOptional()
  customerEmail?: string;

  @MaxLength(20, { message: 'O telefone deve ter no máximo 20 caracteres' })
  @IsOptional()
  customerPhone?: string;

  @IsCPF({ message: 'O CPF informado é inválido' })
  @MaxLength(14, { message: 'O CPF deve ter no máximo 14 caracteres' })
  @MinLength(11, { message: 'O CPF deve ter no mínimo 11 caracteres' })
  @IsOptional()
  customerDocument?: string;

  @IsOptional()
  sessionDescription?: string;

  @IsOptional()
  isLeadCaptured?: boolean;

  @IsOptional()
  newAttendantSecureId?: string;

  @IsOptional()
  isArchived?: boolean;

  @IsOptional()
  isFinalized?: boolean;
}
