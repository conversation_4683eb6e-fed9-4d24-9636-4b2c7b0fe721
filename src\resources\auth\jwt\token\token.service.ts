import { JwtService } from '@nestjs/jwt';
import { ConfigType } from '@nestjs/config';
import { Injectable, Inject, ForbiddenException } from '@nestjs/common';

import { UserTokenPayload } from '../payload/entities/user-token-payload.entity';

import jwtEnvs from './env.config';

@Injectable()
export class JwtTokenService {
  constructor(
    private jwtService: JwtService,

    @Inject(jwtEnvs.KEY)
    private readonly jwtEnvsModule: ConfigType<typeof jwtEnvs>,
  ) {}

  // XXX: Nesse contexto o tempo de expiração do token é calculado a partir dos dados obtidos.
  // Por exemplo caso o usuário tenha apenas uma conta atribuída a ele ela será utilizada na construção do token.
  // Caso ele tenha mais de uma conta atribuída a ele, será criado o token temporário de 5 minutos.
  // Isso tudo com base em accounts ativas
  public generate(payload: UserTokenPayload) {
    const expiresIn = payload.activeAccount
      ? this.jwtEnvsModule.expiresIn
      : '5m';

    return this.jwtService.sign(payload, {
      secret: this.jwtEnvsModule.secret,
      expiresIn: expiresIn,
    });
  }

  public verify(token: string): UserTokenPayload {
    try {
      return this.jwtService.verify<UserTokenPayload>(token, {
        secret: this.jwtEnvsModule.secret,
      });
    } catch (error) {
      throw new ForbiddenException('Token inválido ou expirado!');
    }
  }
}
