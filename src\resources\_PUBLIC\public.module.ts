import { Modu<PERSON> } from '@nestjs/common';
import { PublicPlansModule } from './plans/plans.module';
import { RouterModule } from '@nestjs/core';
import { ChatsModule } from './chats/chats.module';
import { CheckoutModule } from './checkout/checkout.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { WebhookEfiNotificationModule } from './webhook-efi-notification/webhook-efi-notification.module';
import { ChatSessionsModule } from './chat-sessions/chat-sessions.module';
import { MetaModule } from './meta/meta.module';
import { UploadsModule } from './uploads/uploads.module';

@Module({
  imports: [
    PublicPlansModule,
    ChatsModule,
    SubscriptionsModule,
    WebhookEfiNotificationModule,
    CheckoutModule,
    ChatSessionsModule,
    UploadsModule,
    RouterModule.register([
      { path: 'public', module: ChatsModule },
      {
        path: 'public',
        module: ChatSessionsModule,
      },
      {
        path: 'public',
        module: UploadsModule,
      },
    ]),
    MetaModule,
  ],
})
export class PublicModule {}
