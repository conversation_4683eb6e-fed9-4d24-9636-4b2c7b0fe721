-- DropIndex
DROP INDEX `users_cell_phone_key` ON `users`;

-- CreateTable
CREATE TABLE `accounts_permissions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `user_account_id` INTEGER NOT NULL,
    `permission_id` INTEGER NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `accounts_permissions_id_key`(`id`),
    UNIQUE INDEX `accounts_permissions_secure_id_key`(`secure_id`),
    INDEX `accounts_permissions_id_secure_id_idx`(`id`, `secure_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `accounts_permissions` ADD CONSTRAINT `accounts_permissions_user_account_id_fkey` FOR<PERSON><PERSON><PERSON> KEY (`user_account_id`) REFERENCES `users_accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `accounts_permissions` ADD CONSTRAINT `accounts_permissions_permission_id_fkey` FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
