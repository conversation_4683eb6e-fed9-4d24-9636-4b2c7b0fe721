import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { NotificationMapper } from './mapper/notification.mapper';
import { OneSignalService } from 'src/third-party/one-signal/one-signal.service';
import { CreateNotificationDTO } from './dto/notification-create.dto';
import { NotificationEntity } from './entity/notification.entity';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { NotificationOutputDTO } from './dto/notification-output.dto';

@Injectable()
export class NotificationService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly oneSignalService: OneSignalService,
  ) {}

  async findAll(
    userId: number,
    accountId: number,
    paginationQuery: PaginationQueryType,
  ): Promise<IWithPagination<NotificationOutputDTO>> {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    const userAccount = await this.prisma.usersAccounts.findFirst({
      where: {
        userId,
        accountId,
      },
    });

    const whereClause = {
      userAccountId: userAccount?.id,
      isRead: false,
      title: {
        contains: paginationQuery?.search || '',
      },
    };

    const notifications = await this.prisma.notifications.findMany({
      where: whereClause,
      skip,
      take: paginationQuery.limit,
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        secureId: true,
        title: true,
        message: true,
        createdAt: true,
        session: {
          select: {
            secureId: true,
          },
        },
      },
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prisma.notifications,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    return {
      meta: meta,
      data:
        notifications.length === 0
          ? ([] as NotificationOutputDTO[])
          : NotificationMapper.toNotificationResponse(notifications),
    };
  }

  async createNotification(createNotificationData: CreateNotificationDTO) {
    const userAccount = await this.prisma.usersAccounts.findFirst({
      where: {
        userId: createNotificationData.userId,
        accountId: createNotificationData.accountId,
      },
    });

    if (!userAccount) {
      throw new Error('User account not found');
    }

    await this.createNotificationInDB({
      title: createNotificationData.title,
      message: createNotificationData.message,
      userAccountId: userAccount.id,
      sessionId: createNotificationData.sessionId,
      accountId: createNotificationData.accountId,
    });
  }

  async createNotificationInDB(data: {
    title: string;
    message: string;
    userAccountId?: number;
    sessionId?: number;
    accountId: number;
  }) {
    const newNotification = new NotificationEntity({
      ...data,
    });

    const { userAccountId, accountId, sessionId, ...notificationScalarData } =
      newNotification;

    const prismaCreateData: any = {
      ...notificationScalarData,
    };

    if (data.userAccountId !== undefined) {
      prismaCreateData.userAccount = {
        connect: { id: data.userAccountId },
      };
    }
    if (data.sessionId !== undefined) {
      prismaCreateData.session = {
        connect: { id: data.sessionId },
      };
    }
    if (data.accountId !== undefined) {
      prismaCreateData.Accounts = {
        connect: { id: data.accountId },
      };
    }

    await this.prisma.notifications.create({
      data: prismaCreateData,
    });
  }

  async sendEmail({
    to,
    subject,
    body,
  }: {
    to: string;
    subject: string;
    body: string;
  }) {
    return this.oneSignalService.sendEmail({ to, subject, body });
  }

  async readNotification(
    secureId: string,
    userId: number,
    accountId: number,
  ): Promise<void> {
    const notification = await this.prisma.notifications.findFirst({
      where: { secureId, userAccount: { userId, accountId } },
    });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    await this.prisma.notifications.update({
      where: { secureId },
      data: { isRead: true },
    });
  }

  async sendToAllUsersInAccount(data: {
    accountId: number;
    title: string;
    message: string;
  }) {
    const { accountId, title, message } = data;

    const usersAccounts = await this.prisma.usersAccounts.findMany({
      where: { accountId },
      select: { userId: true },
    });

    if (usersAccounts.length === 0) {
      throw new NotFoundException('No users found for this account');
    }

    for (const userAccount of usersAccounts) {
      await this.createNotificationInDB({
        userAccountId: userAccount.userId,
        accountId,
        title,
        message,
      });
    }
  }
}
