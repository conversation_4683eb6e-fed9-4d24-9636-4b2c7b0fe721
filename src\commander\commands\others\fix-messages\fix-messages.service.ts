import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';

@Injectable()
export class FixMessagesService {
  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Arrumando mensagens:`);

    const messages = await prisma.chatMessages.findMany({});
    const totalMessages = messages.length;
    let processedMessages = 0;

    // Initial progress bar
    this.updateProgressBar(processedMessages, totalMessages);

    for await (const message of messages) {
      await prisma.chatMessages.update({
        where: {
          id: message.id,
        },
        data: {
          messageDirection: !message.sendMessage ? 'received' : 'sent',
          messageType: message.messageType || 'text',
        },
      });

      // Update progress after each message is processed
      processedMessages++;
      this.updateProgressBar(processedMessages, totalMessages);
    }

    console.log(
      '\n\x1b[32m',
      `Concluído! ${totalMessages} mensagens atualizadas.`,
    );
  }

  private updateProgressBar(current: number, total: number): void {
    const percentage = Math.floor((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.floor((percentage * barLength) / 100);

    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(barLength - filledLength);

    process.stdout.write(
      `\r[${filledBar}${emptyBar}] ${percentage}% (${current}/${total})`,
    );
  }
}
