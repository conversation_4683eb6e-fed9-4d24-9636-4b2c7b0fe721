import { Users as UserModel } from '@prisma/client';
import { GetAllUsersOutputDto } from '../dto/get-all-users-output.dto';
import { CPFMask } from 'src/@shared/helpers/mask/cpf.mask';
import { UserWithAccount, UserWithAccountAndAccountPermissionsModel } from '../users.contract';

export class BackofficeUsersGetAllUsersFactory {
  static createFromUserModelBatchToOutputDtoBatch(
    models: UserWithAccount[],
  ): GetAllUsersOutputDto[] {
    return models.map((user) => {
      const backofficeAccount = user.usersAccounts.find(
        (userAccount) => userAccount.role?.slug === 'BACKOFFICE',
      );

      return {
        secureId: user.secureId,
        email: user.email,
        name: user.name,
        cpf: CPFMask.apply(user.cpf),
        isActive: backofficeAccount?.isActive ?? false,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    });
  }
}
