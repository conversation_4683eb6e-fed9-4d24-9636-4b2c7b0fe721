/*
  Warnings:

  - You are about to drop the column `upload_id` on the `knowledge_base` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE `knowledge_base` DROP FOREIGN KEY `knowledge_base_upload_id_fkey`;

-- DropIndex
DROP INDEX `knowledge_base_upload_id_key` ON `knowledge_base`;

-- AlterTable
ALTER TABLE `knowledge_base` DROP COLUMN `upload_id`;

-- CreateTable
CREATE TABLE `ingested_data` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `file_name` VARCHAR(255) NOT NULL,
    `file_type` VARCHAR(255) NOT NULL,
    `file_size` INTEGER NOT NULL,
    `total_characters` INTEGER NOT NULL,
    `knowledge_base_id` INTEGER NULL,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ingested_data_id_key`(`id`),
    UNIQUE INDEX `ingested_data_secure_id_key`(`secure_id`),
    INDEX `ingested_data_id_secure_id_knowledge_base_id_idx`(`id`, `secure_id`, `knowledge_base_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ingested_data` ADD CONSTRAINT `ingested_data_knowledge_base_id_fkey` FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_base`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
