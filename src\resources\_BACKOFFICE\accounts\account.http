@apiBaseUrl = http://localhost:3333
@accountEndPoint = /api/v1/accounts
@accountAPI = {{apiBaseUrl}}{{accountEndPoint}}



### Cria uma account
# @name createNewAccount
POST {{accountAPI}}
Content-Type: application/json

{
  "companyName": "plyrchat",
  "isDeleted": false,
  "isActive": true
}



### Get All Accounts
# @name getAllAccounts
# GET {{accountAPI}}
GET {{apiBaseUrl}}{{accountEndPoint}}
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10&isActive=false
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10&isActive=true
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10&isDeleted=false
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10&isDeleted=true
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10&isDeleted=false&isActive=false
# GET {{apiBaseUrl}}{{accountEndPoint}}?search=plyrtech&page=2&limit=10&isDeleted=true&isActive=true
# GET {{apiBaseUrl}}{{accountEndPoint}}?page=1&limit=10&isActive=true



### Get by secureId
# @name getOneBySecureId
@firstItemSecureId = {{getAllAccounts.response.body.data[0].secureId}}
GET {{accountAPI}}/{{firstItemSecureId}}



### Delete by secureId
# @name deleteBySecureId
@firstItemSecureId = {{getAllAccounts.response.body.data[0].secureId}}
DELETE {{accountAPI}}/{{firstItemSecureId}}



### Update Account
# @name updateAccountBySecureId
@firstItemSecureId = {{getAllAccounts.response.body.data[0].secureId}}
PUT {{accountAPI}}/{{firstItemSecureId}}
Content-Type: application/json

{
  "companyName": "plyrflococo",
  "isDeleted": true,
  "isActive": true
}
