// import { PartialType } from '@nestjs/swagger';

import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from 'class-validator';
import { IsCPF } from 'src/@shared/decorators/validation/cpf/cpf.decorator';
import { profileSwaggerDto } from '../docs/dto';

export class UpdateProfileDto {
  @ApiProperty(profileSwaggerDto.profileUpdateDto.name)
  @MaxLength(255, { message: 'O nome deve ter no máximo 255 caracteres' })
  @MinLength(3, { message: 'O nome deve ter no mínimo 3 caracteres' })
  @IsString({ message: 'O nome deve ser uma string' })
  @IsNotEmpty({
    message: 'Se o nome estiver presente ele não pode estar vazio',
  })
  @IsOptional()
  name?: string;

  @ApiProperty(profileSwaggerDto.profileUpdateDto.email)
  @IsEmail({}, { message: 'O email informado é inválido' })
  @IsNotEmpty({
    message: 'Se o email estiver presente ela não pode estar vazio',
  })
  @IsOptional()
  email?: string;

  @ApiProperty(profileSwaggerDto.profileUpdateDto.password)
  @MaxLength(255, { message: 'A senha deve ter no máximo 255 caracteres' })
  @MinLength(6, { message: 'A senha deve ter no mínimo 6 caracteres' })
  @IsNotEmpty({
    message: 'Se a senha estiver presente ela não pode estar vazio',
  })
  @IsOptional()
  password?: string;

  @ApiProperty(profileSwaggerDto.profileUpdateDto.cpf)
  @IsCPF({ message: 'O CPF informado é inválido' })
  @MaxLength(14, { message: 'O CPF deve ter no máximo 14 caracteres' })
  @MinLength(11, { message: 'O CPF deve ter no mínimo 11 caracteres' })
  @IsNotEmpty({ message: 'Se o CPF estiver presente ele não pode estar vazio' })
  @IsOptional()
  cpf?: string;

  @ApiProperty(profileSwaggerDto.profileUpdateDto.isActive)
  @IsBoolean({ message: 'O campo isActive deve ser um booleano' })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty(profileSwaggerDto.profileUpdateDto.cellPhone)
  @MaxLength(20, { message: 'O telefone deve ter no máximo 20 caracteres' })
  @IsNotEmpty({
    message: 'Se o Telefone estiver presente ele não pode estar vazio',
  })
  @IsOptional()
  cellPhone?: string;

  @ApiProperty(profileSwaggerDto.profileUpdateDto.companyName)
  @MaxLength(255, {
    message: 'O nome da empresa deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O nome da empresa deve ter no mínimo 3 caracteres',
  })
  @IsNotEmpty({
    message: 'Se o nome da empresa estiver presente ele não pode estar vazio!',
  })
  @IsOptional()
  companyName?: string;
}
