import { Body, Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';

import { AccountSubscriptionsService } from './account-subscriptions.service';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';

import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { AccountSubscriptionDecorator } from './account-subscriptions.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';

import { AccountSubscriptionOutputDto } from './dto/account-subscription.output.dto';

import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { AccountSubscriptionDecoratorOutput } from './account-subscriptions.contracts';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { CreateAccountSubscriptionDto } from './dto/create-account-subscription.dto';

@Controller('accounts-subscriptions')
export class AccountSubscriptionsController {
  constructor(
    private readonly subscriptionsService: AccountSubscriptionsService,
  ) {}

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountSubscriptionDecorator()
    accountSubscriptionQuery: AccountSubscriptionDecoratorOutput,
  ): Promise<IWithPagination<AccountSubscriptionOutputDto>> {
    return await this.subscriptionsService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountSubscriptionQuery,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Get(':secureId')
  async findOne(@Param('secureId') secureId: string) {
    return await this.subscriptionsService.findOne(secureId);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Post()
  async create(@Body() createAccountSubscriptionDto: CreateAccountSubscriptionDto) {
    return await this.subscriptionsService.create(createAccountSubscriptionDto);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Delete(':secureId')
  async cancel(@Param('secureId') secureId: string) {
    return await this.subscriptionsService.cancel(secureId);
  }
}
