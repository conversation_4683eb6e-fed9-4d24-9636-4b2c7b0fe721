import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { IEVOInstancesEnvs } from './env.contract';

export default registerAs('evo-instance-envs', () => {
  const values: IEVOInstancesEnvs = {
    webhookUrl: process.env.SERVER_BASE_URL + '/whatsapp/webhook',

    createInstanceEP: '/instance/create',
    getAllInstancesEP: '/instance/fetchInstances',
    deleteInstanceEP: '/instance/delete',
    logoutInstanceEP: '/instance/logout',
    connectInstanceEP: '/instance/connect',
    getInstanceState: '/instance/connectionState',
  };

  const schema = Joi.object<IEVOInstancesEnvs>({
    webhookUrl: Joi.string().required().messages({
      'any.required': 'ENV: SERVER_BASE_URL is required',
    }),

    createInstanceEP: Joi.string().required().messages({
      'any.required': 'EVO Third Party value is required. "createInstanceEP"',
    }),
    getAllInstancesEP: Joi.string().required().messages({
      'any.required': 'EVO Third Party value is required. "getAllInstancesEP"',
    }),
    deleteInstanceEP: Joi.string().required().messages({
      'any.required': 'EVO Third Party value is required. "deleteInstanceEP"',
    }),
    connectInstanceEP: Joi.string().required().messages({
      'any.required': 'EVO Third Party value is required. "connectInstanceEP"',
    }),
    logoutInstanceEP: Joi.string().required().messages({
      'any.required': 'EVO Third Party value is required. "logoutInstanceEP"',
    }),
    getInstanceState: Joi.string().required().messages({
      'any.required': 'EVO Third Party value is required. "getInstanceState"',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
