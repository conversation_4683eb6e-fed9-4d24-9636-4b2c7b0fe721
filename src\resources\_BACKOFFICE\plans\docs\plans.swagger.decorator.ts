import { applyDecorators } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { CreatePlanDto } from '../dto/create-plan.dto';

import { swaggerPlans } from './index';
import { swaggerShared } from 'src/@shared/docs/swagger';

function Create() {
  return applyDecorators(
    ApiOperation({
      summary:
        'Criação de um novo plano. Plano esse que será utilizado pelo usuário para contratação',
      description:
        'Cria um novo plano. Esta ação requer permissões de usuário com a role MASTER. Assim que o plano for criado, automaticamente ele irá criar um plano dentro do EFI. Existe uma lógica aqui! Antes de criar um plano em nossas tabelas ele cria o plano no EFI, assim que criado ele cria em nossa tabela, caso algo de errado ele deleta o plano criado no EFI, dessa maneira contorna possíveis erros.',
    }),
    ApiExtraModels(CreatePlanDto),
    ApiCreatedResponse(),
    ApiConflictResponse(swaggerPlans.api.status.create.conflict),
    ApiBadRequestResponse(swaggerPlans.api.status.create.badRequest),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

function FindAll() {
  return applyDecorators(
    ApiOperation({
      summary: 'Lista todos os planos.',
      description:
        'Lista todos os planos disponíveis. Esta ação requer permissões de usuário com a role MASTER',
    }),
    ApiQuery(swaggerShared.query.page),
    ApiQuery(swaggerShared.query.limit),
    ApiQuery(swaggerShared.query.search),
    ApiQuery(swaggerShared.query.isActive),
    ApiOkResponse(swaggerPlans.api.status.findAll.okResponse),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

function FindOne() {
  return applyDecorators(
    ApiOperation({
      summary: 'Busca um plano pelo secureId.',
      description:
        'Busca um plano pelo secureId. Esta ação requer permissões de usuário com a role MASTER',
    }),
    ApiParam(swaggerShared.params.secureId),
    ApiNotFoundResponse(swaggerPlans.api.status.findOne.notFound),
    ApiOkResponse(swaggerPlans.api.status.findOne.okResponse),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

function Update() {
  return applyDecorators(
    ApiOperation({
      summary: 'Atualiza um plano pelo secureId.',
      description:
        'Atualiza um plano pelo secureId. Esta ação requer permissões de usuário com a role MASTER',
    }),
    ApiParam(swaggerShared.params.secureId),
    ApiNotFoundResponse(swaggerPlans.api.status.update.notFound),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
    ApiNoContentResponse({
      description: 'Plano atualizado com sucesso. Sem corpo na resposta',
    }),
    ApiBadRequestResponse(swaggerPlans.api.status.update.badRequest),
    ApiConflictResponse(swaggerPlans.api.status.update.conflict),
  );
}

function Remove() {
  return applyDecorators(
    ApiOperation({
      summary: 'Inativa um plano pelo secureId. E exclui ele do EFI',
      description:
        'Inativa um plano pelo secureId. E exclui ele do EFI. Esta ação requer permissões de usuário com a role MASTER. Essa ação fará com que o nome e o slug do plano sejam alterados para um valor único e que o plano seja excluído do EFI. Essa ação não tem retorno',
    }),
    ApiParam(swaggerShared.params.secureId),
    ApiNotFoundResponse(swaggerPlans.api.status.delete.notFound),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
    ApiNoContentResponse(),
  );
}

export const SwaggerPlan = {
  Create,
  FindAll,
  FindOne,
  Update,
  Remove,
};
