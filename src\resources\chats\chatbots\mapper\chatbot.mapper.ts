import { Chatbot } from '../entities/chatbot.entity';
import { ChatBots as ChatbotModel } from '@prisma/client';

export class ChatBotMapper {
  static fromEntityToModel(entity: Chatbot): ChatbotModel {
    const chatbotModel = {
      secureId: entity.secureId,

      accountId: entity.accountId,

      name: entity.name,
      isAI: entity.isAI,
      isLeadCaptureActive: entity.isLeadCaptureActive,
      leadTriggerMessageLimit: entity.leadTriggerMessageLimit,
      leadCaptureMessage: entity.leadCaptureMessage,
      leadCaptureThankYouMessage: entity.leadCaptureThankYouMessage,

      inputToken: entity.inputToken,
      outputToken: entity.outputToken,

      isActive: entity.isActive,
      isDeleted: entity.isDeleted,
    };

    return chatbotModel as ChatbotModel;
  }
}
