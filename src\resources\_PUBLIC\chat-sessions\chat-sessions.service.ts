import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { SessionFactory } from './factory/session.factory';

@Injectable()
export class ChatSessionsService {
  constructor(private readonly prismaService: PrismaService) {}

  async findOne(secureId: string) {
    const session = await this.prismaService.chatSessions.findFirst({
      where: { secureId: { equals: secureId } },
      include: {
        chatMessages: {
          orderBy: {
            createdAt: 'asc',
          },
          select: {
            secureId: true,
            sendMessage: true,
            receiveMessage: true,
            createdAt: true,
            userAccount: {
              select: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
        account: true,
        chat: true,
      },
    });

    if (!session) {
      return null;
    }

    return SessionFactory.convertModelToOutput(session);
  }

  async findMessages(secureId: string) {
    const session = await this.prismaService.chatSessions.findFirst({
      where: { secureId: { equals: secureId } },
      include: {
        chatMessages: {
          orderBy: {
            createdAt: 'asc',
          },
          select: {
            secureId: true,
            sendMessage: true,
            receiveMessage: true,
            createdAt: true,
            messageDirection: true,
            messageType: true,
            upload: {
              select: {
                secureId: true,
                urlCdn: true,
              },
            },
            userAccount: {
              select: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            replyTo: {
              select: {
                secureId: true,
              },
            },
          },
        },
        chat: {
          include: {
            chatbot: true,
          },
        },
      },
    });

    if (!session) {
      return [];
    }

    return SessionFactory.convertModelToMessagesOutput(session);
  }
}
