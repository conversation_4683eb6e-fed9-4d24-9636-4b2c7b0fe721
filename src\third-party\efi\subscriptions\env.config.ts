import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { IEFISubscriptionsEnvs } from './efi-subscriptions.contract';

export default registerAs('efi-subscriptions-envs', () => {
  const values: IEFISubscriptionsEnvs = {
    baseUrl: process.env.EFI_BASE_URL,
    subscriptionEP: '/subscription',
    planEP: '/v1/plan',
    serverBaseUrl: process.env.SERVER_BASE_URL,
    webhookEP: '/webhook-efi-notification',
  };

  const schema = Joi.object<IEFISubscriptionsEnvs>({
    baseUrl: Joi.string().required().messages({
      'any.required': 'ENV: EFI_BASE_URL is required',
    }),
    subscriptionEP: Joi.string().required().messages({
      'any.required': 'subscriptionEP declaration: is required',
    }),
    planEP: Joi.string().required().messages({
      'any.required': 'DECLARATION: planEP is not declared and is required',
    }),
    serverBaseUrl: Joi.string().required().messages({
      'any.required': 'ENV: SERVER_BASE_URL is required',
    }),
    webhookEP: Joi.string().required().messages({
      'any.required': 'webhookEP declaration: is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
