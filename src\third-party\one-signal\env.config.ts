import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';
import { IOneSignalEnvs } from './one-signal.contract';


export default registerAs('one-signal-envs', () => {
  const values: IOneSignalEnvs = {
    oneSignalAppId: process.env.ONE_SIGNAL_APP_ID,
    oneSignalApiKey: process.env.ONE_SIGNAL_REST_API_KEY,
    baseUrl: "https://api.onesignal.com",
  };

  const schema = Joi.object<IOneSignalEnvs>({
    oneSignalAppId: Joi.string().required().messages({
      'any.required': 'ENV: ONE_SIGNAL_APP_ID is required',
    }),
    oneSignalApiKey: Joi.string().required().messages({
      'any.required': 'ENV: ONE_SIGNAL_REST_API_KEY is required',
    }),
    baseUrl: Joi.string().required().messages({
      'any.required': 'baseUrl is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
