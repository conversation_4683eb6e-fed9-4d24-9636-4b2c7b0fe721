import { v4 as uuidV4 } from 'uuid';

import { Entity } from 'src/@shared/contracts/entity/interface';

type PlanConstructorProps = {
  id?: number;
  secureId?: string;

  attendantsLimit: number;
  whatsappNumberLimit: number;
  chatbotsLimit: number;
  knowledgeBaseLimit: number;
  iaMessagesLimit: number;
  interval?: number;
  trialDays?: number;

  name: string;
  slug: string;
  description?: string;
  price: string;
  details?: string;

  isActive: boolean;

  efiPlanId?: number;
  efiIsDeleted?: boolean;

  createdAt?: Date;
  updatedAt?: Date;
};

type PlanJson = {
  secureId: string;

  attendantsLimit: number;
  whatsappNumberLimit: number;
  chatbotsLimit: number;
  knowledgeBaseLimit: number;
  iaMessagesLimit: number;
  interval: number;
  trialDays: number;

  name: string;
  slug: string;
  description?: string;
  price: string;
  details?: string;

  efiPlanId?: number;
  efiIsDeleted?: boolean;

  isActive: boolean;

  createdAt: Date;
  updatedAt: Date;
};

export class Plan implements Entity {
  id?: number;
  secureId?: string;

  attendantsLimit: number;
  whatsappNumberLimit: number;
  chatbotsLimit: number;
  knowledgeBaseLimit: number;
  iaMessagesLimit: number;
  interval: number;
  trialDays: number;

  name: string;
  slug: string;
  description?: string;
  price: string;
  details?: string;

  efiPlanId?: number;
  efiIsDeleted?: boolean;

  isActive: boolean;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: PlanConstructorProps) {
    const efiIsDeleted =
      props?.efiIsDeleted === undefined
        ? false
        : props.efiIsDeleted === false
          ? false
          : true;

    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.attendantsLimit = props.attendantsLimit;
    this.whatsappNumberLimit = props.whatsappNumberLimit;
    this.chatbotsLimit = props.chatbotsLimit;
    this.knowledgeBaseLimit = props.knowledgeBaseLimit;
    this.iaMessagesLimit = props.iaMessagesLimit;
    this.interval = props?.interval ? props.interval : 1;
    this.trialDays = props?.trialDays ? props.trialDays : 0;

    this.name = props.name;
    this.slug = props.slug;
    this.description = props?.description ? props.description : undefined;
    this.price = props.price;
    this.details = props?.details ? props.details : undefined;

    this.efiPlanId = props?.efiPlanId ? props.efiPlanId : undefined;
    this.efiIsDeleted = efiIsDeleted;

    this.isActive = props?.isActive === false ? false : true;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  deactivate(): void {
    this.isActive = false;
  }

  active(): void {
    this.isActive = true;
  }

  efiDelete(): void {
    this.efiIsDeleted = true;
  }

  efiRestore(): void {
    this.efiIsDeleted = false;
  }

  toJSON(): PlanJson {
    return {
      secureId: this.secureId,

      attendantsLimit: this.attendantsLimit,
      whatsappNumberLimit: this.whatsappNumberLimit,
      chatbotsLimit: this.chatbotsLimit,
      knowledgeBaseLimit: this.knowledgeBaseLimit,
      iaMessagesLimit: this.iaMessagesLimit,
      interval: this.interval,
      trialDays: this.trialDays,

      name: this.name,
      slug: this.slug,
      description: this.description,
      price: this.price,
      details: this.details,

      efiPlanId: this.efiPlanId,
      efiIsDeleted: this.efiIsDeleted,

      isActive: this.isActive,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
