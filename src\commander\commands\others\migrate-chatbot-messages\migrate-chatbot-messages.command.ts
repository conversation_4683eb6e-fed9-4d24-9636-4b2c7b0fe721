import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { MigrateChatbotMessagesService } from './migrate-chatbot-messages.service';

@Command({
  name: 'migrate-chatbot-messages',
  description:
    'Preenche o campo chat_bot_id nas mensagens antigas baseado na relação chat_session_id > chat_id > chatbot_id',
})
@Injectable()
export class MigrateChatbotMessagesCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(
    private readonly migrateChatbotMessagesService: MigrateChatbotMessagesService,
  ) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    console.log('\x1b[34m', 'Iniciando migração de chat_bot_id nas mensagens...\n');

    try {
      await this.migrateChatbotMessagesService.execute(this.prisma);
      console.log('\x1b[32m', '\nMigração concluída com sucesso!\n');
    } catch (error) {
      console.error('\x1b[31m', 'Erro durante a migração:', error);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}
