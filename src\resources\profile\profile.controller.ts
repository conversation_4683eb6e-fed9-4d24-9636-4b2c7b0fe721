import {
  Controller,
  Get,
  Body,
  Delete,
  UseGuards,
  Put,
  Res,
} from '@nestjs/common';
import { ProfileService } from './profile.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { OwnProfileGuard } from 'src/@shared/guards/profile/own-profile.guard';
import { CheckEmptyBodyGuard } from 'src/@shared/guards/body/body.guard';
import { DoesUserCanChangeEmailGuard } from 'src/@shared/guards/profile/can-change-email.guard';
import { DoesUserCanChangeCPFGuard } from 'src/@shared/guards/profile/can-change-cpf.guard';
import { DoesUserCanChangeCompanyNameGuard } from 'src/@shared/guards/profile/can-change-company-name.guard';
import { Response } from 'express';
import { ProfileSwagger } from './decorators/swagger.decorator';
import { ApiBearerAuth } from '@nestjs/swagger';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { TypeProfileOutputDto } from './dto/profile-output.dto';

@ApiBearerAuth()
@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) { }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT', 'BACKOFFICE'])
  @Get()
  @ProfileSwagger.FindOne()
  async findOne(
    @UserIdExtractorDecorator() userId: number,
    @AccountIdExtractor() accountId: number,
  ): Promise<TypeProfileOutputDto> {
    return await this.profileService.findOne(userId, accountId);
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    DoesUserCanChangeEmailGuard,
    DoesUserCanChangeCPFGuard,
    DoesUserCanChangeCompanyNameGuard,

    new CheckEmptyBodyGuard(
      'Para atualizar o perfil, é necessário enviar ao menos um campo para atualização.',
    ),
  )
  @Roles(['APP', 'ATTENDANT', 'BACKOFFICE'])
  @Put()
  @ProfileSwagger.Update()
  async update(
    @UserIdExtractorDecorator() userId: number,
    @Body() updateProfileDto: UpdateProfileDto,
    @Res() response: Response,
  ) {
    await this.profileService.update(userId, updateProfileDto);

    response.status(204).send();
  }

  @UseGuards(JwtGuard, RoleGuard, OwnProfileGuard)
  @Roles(['APP', 'ATTENDANT', 'BACKOFFICE'])
  @Delete()
  @ProfileSwagger.Delete()
  async remove(
    @UserIdExtractorDecorator() userId: number,
    @Res() response: Response,
  ) {
    await this.profileService.remove(userId);

    response.status(204).send();
  }
}
