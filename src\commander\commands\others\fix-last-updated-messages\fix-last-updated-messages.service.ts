import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';

@Injectable()
export class FixLastUpdatedMessageService {
  async execute(prisma: PrismaClient) {
    console.log('Starting update of lastMessageAt for chat sessions...');
    const sessions = await prisma.chatSessions.findMany({
      select: {
        id: true,
        secureId: true,
      },
    });

    const totalSessions = sessions.length;
    console.log(`Found ${totalSessions} sessions to process.`);

    for (let i = 0; i < totalSessions; i++) {
      const session = sessions[i];
      const lastMessage = await prisma.chatMessages.findFirst({
        where: {
          chatSessionId: session.id,
        },
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          createdAt: true,
        },
      });

      if (lastMessage) {
        await prisma.chatSessions.update({
          where: {
            id: session.id,
          },
          data: {
            lastMessageAt: lastMessage.createdAt,
          },
        });
      }
      this.updateProgressBar(i + 1, totalSessions);
    }

    process.stdout.write('\n'); // Add a newline after the progress bar finishes
    console.log('Finished updating lastMessageAt for all sessions.');
  }

  private updateProgressBar(current: number, total: number): void {
    const percentage = Math.floor((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.floor((percentage * barLength) / 100);

    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(barLength - filledLength);

    process.stdout.write(
      `\r[${filledBar}${emptyBar}] ${percentage}% (${current}/${total})`,
    );
  }
}
