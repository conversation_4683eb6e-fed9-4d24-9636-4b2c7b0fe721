import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PaymentRequiredException } from 'src/@shared/exceptions/payment-required.exception';

@Injectable()
export class SubscriptionSessionService {
  private readonly logger = new Logger(SubscriptionSessionService.name);

  constructor(private readonly prismaService: PrismaService) {}

  async decrementRemainingSession(data: {
    accountId?: number;
    accountSecureId?: string;
  }): Promise<void> {
    try {
      const { accountSecureId, accountId } = data;

      if (!accountSecureId && !accountId) {
        throw new Error('accountSecureId or accountId is required');
      }

      let whereClause = {};
      if (accountSecureId) {
        whereClause = {
          secureId: accountSecureId,
        };
      } else if (accountId) {
        whereClause = {
          id: accountId,
        };
      }

      const account = await this.prismaService.accounts.findFirst({
        where: whereClause,
        include: {
          subscriptions: {
            where: {
              isActive: true,
              endsAt: { gte: new Date() },
            },
          },
          usersAccounts: {
            where: {
              isOwner: true,
            },
            select: {
              role: {
                select: {
                  slug: true,
                },
              },
            },
          },
        },
      });

      if (!account) {
        throw new PaymentRequiredException('Account não encontrada');
      }

      if (account.usersAccounts[0].role.slug === 'MASTER') {
        return;
      }

      if (account.companyName === 'PlyrChat') {
        return;
      }

      if (!account.subscriptions.length) {
        throw new PaymentRequiredException(
          'Nenhuma inscrição ativa encontrada',
        );
      }

      const subscription = account.subscriptions[0];

      if (subscription.remainingSessions <= 0) {
        throw new PaymentRequiredException('Limite de sessões atingido');
      }

      await this.prismaService.subscriptions.update({
        where: {
          id: subscription.id,
        },
        data: {
          remainingSessions: {
            decrement: 1,
          },
        },
      });
    } catch (error) {
      // Don't catch PaymentRequiredException as it's an intentional HTTP status code
      if (error instanceof PaymentRequiredException) {
        throw error;
      }

      this.logger.error(
        `Error in decrementRemainingSession: ${error.message}`,
        error.stack,
      );
      throw error; // The AllExceptionsFilter will handle sending the email
    }
  }

  async hasRemainingSessions(data: {
    accountId?: number;
    accountSecureId?: string;
  }): Promise<boolean> {
    try {
      const { accountSecureId, accountId } = data;

      if (!accountSecureId && !accountId) {
        throw new Error('accountSecureId or accountId is required');
      }

      let whereClause = {};
      if (accountSecureId) {
        whereClause = {
          secureId: accountSecureId,
        };
      } else if (accountId) {
        whereClause = {
          id: accountId,
        };
      }

      const account = await this.prismaService.accounts.findFirst({
        where: whereClause,
        include: {
          subscriptions: {
            where: {
              isActive: true,
              endsAt: { gte: new Date() },
            },
          },
          usersAccounts: {
            where: {
              isOwner: true,
            },
            select: {
              role: {
                select: {
                  slug: true,
                },
              },
            },
          },
        },
      });

      if (!account) {
        throw new PaymentRequiredException('Account não encontrada');
      }

      if (account.usersAccounts[0].role.slug === 'MASTER') {
        return true;
      }

      if (account.companyName === 'PlyrChat') {
        return;
      }

      if (!account.subscriptions.length) {
        throw new PaymentRequiredException(
          'Nenhuma inscrição ativa encontrada',
        );
      }

      const subscription = account.subscriptions[0];

      if (subscription.remainingSessions <= 0) {
        return false;
      }
      return true;
    } catch (error) {
      // Don't catch PaymentRequiredException as it's an intentional HTTP status code
      if (error instanceof PaymentRequiredException) {
        throw error;
      }

      this.logger.error(
        `Error in hasRemainingSessions: ${error.message}`,
        error.stack,
      );
      throw error; // The AllExceptionsFilter will handle sending the email
    }
  }

  async hasRemainingFreemiumTrialDays(data: {
    accountId?: number;
    accountSecureId?: string;
  }): Promise<boolean> {
    try {
      const { accountSecureId, accountId } = data;

      if (!accountSecureId && !accountId) {
        throw new Error('accountSecureId or accountId is required');
      }

      let whereClause = {};
      if (accountSecureId) {
        whereClause = {
          secureId: accountSecureId,
        };
      } else if (accountId) {
        whereClause = {
          id: accountId,
        };
      }

      const account = await this.prismaService.accounts.findFirst({
        where: whereClause,
        include: {
          subscriptions: {
            where: {
              isActive: true,
              endsAt: { gte: new Date() },
              plan: {
                slug: 'freemium',
              },
            },
          },
          usersAccounts: {
            where: {
              isOwner: true,
            },
            select: {
              role: {
                select: {
                  slug: true,
                },
              },
            },
          },
        },
      });

      if (!account) {
        throw new PaymentRequiredException('Account não encontrada');
      }

      if (account.usersAccounts[0].role.slug === 'MASTER') {
        return true;
      }

      if (!account.subscriptions.length) {
        return true;
      }

      const subscription = account.subscriptions[0];

      if (subscription.trialEndsAt < new Date()) {
        return false;
      }
      return true;
    } catch (error) {
      // Don't catch PaymentRequiredException as it's an intentional HTTP status code
      if (error instanceof PaymentRequiredException) {
        throw error;
      }

      this.logger.error(
        `Error in hasRemainingFreemiumTrialDays: ${error.message}`,
        error.stack,
      );
      throw error; // The AllExceptionsFilter will handle sending the email
    }
  }
}
