import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PaymentRequiredException } from 'src/@shared/exceptions/payment-required.exception';
import { FreemiumSubscriptionService } from '../freemium-subscription/freemium-subscription.service';

@Injectable()
export class SubscriptionSessionService {
  private readonly logger = new Logger(SubscriptionSessionService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly freemiumSubscriptionService: FreemiumSubscriptionService,
  ) {}

  async decrementRemainingSession(data: {
    accountId?: number;
    accountSecureId?: string;
  }): Promise<void> {
    try {
      const { accountSecureId, accountId } = data;

      if (!accountSecureId && !accountId) {
        throw new Error('accountSecureId or accountId is required');
      }

      let whereClause = {};
      if (accountSecureId) {
        whereClause = {
          secureId: accountSecureId,
        };
      } else if (accountId) {
        whereClause = {
          id: accountId,
        };
      }

      const account = await this.prismaService.accounts.findFirst({
        where: whereClause,
        include: {
          subscriptions: {
            where: {
              isActive: true,
              endsAt: { gte: new Date() },
            },
          },
          usersAccounts: {
            where: {
              isOwner: true,
            },
            select: {
              role: {
                select: {
                  slug: true,
                },
              },
            },
          },
        },
      });

      if (!account) {
        throw new PaymentRequiredException('Account não encontrada');
      }

      if (account.usersAccounts[0].role.slug === 'MASTER') {
        return;
      }

      if (account.companyName === 'PlyrChat') {
        return;
      }

      if (!account.subscriptions.length) {
        throw new PaymentRequiredException(
          'Nenhuma inscrição ativa encontrada',
        );
      }

      const subscription = account.subscriptions[0];

      if (subscription.remainingSessions <= 0) {
        throw new PaymentRequiredException('Limite de sessões atingido');
      }

      await this.prismaService.subscriptions.update({
        where: {
          id: subscription.id,
        },
        data: {
          remainingSessions: {
            decrement: 1,
          },
        },
      });
    } catch (error) {
      // Don't catch PaymentRequiredException as it's an intentional HTTP status code
      if (error instanceof PaymentRequiredException) {
        throw error;
      }

      this.logger.error(
        `Error in decrementRemainingSession: ${error.message}`,
        error.stack,
      );
      throw error; // The AllExceptionsFilter will handle sending the email
    }
  }

  async hasRemainingSessions(data: {
    accountId?: number;
    accountSecureId?: string;
  }): Promise<boolean> {
    try {
      const { accountSecureId, accountId } = data;

      if (!accountSecureId && !accountId) {
        throw new Error('accountSecureId or accountId is required');
      }

      let whereClause = {};
      if (accountSecureId) {
        whereClause = {
          secureId: accountSecureId,
        };
      } else if (accountId) {
        whereClause = {
          id: accountId,
        };
      }

      const account = await this.prismaService.accounts.findFirst({
        where: whereClause,
        include: {
          subscriptions: {
            where: {
              isActive: true,
              endsAt: { gte: new Date() },
            },
          },
          usersAccounts: {
            where: {
              isOwner: true,
            },
            select: {
              role: {
                select: {
                  slug: true,
                },
              },
            },
          },
        },
      });

      if (!account) {
        throw new PaymentRequiredException('Account não encontrada');
      }

      if (account.usersAccounts[0].role.slug === 'MASTER') {
        return true;
      }

      if (account.companyName === 'PlyrChat') {
        return;
      }

      if (!account.subscriptions.length) {
        throw new PaymentRequiredException(
          'Nenhuma inscrição ativa encontrada',
        );
      }

      const subscription = account.subscriptions[0];

      if (subscription.remainingSessions <= 0) {
        return false;
      }
      return true;
    } catch (error) {
      // Don't catch PaymentRequiredException as it's an intentional HTTP status code
      if (error instanceof PaymentRequiredException) {
        throw error;
      }

      this.logger.error(
        `Error in hasRemainingSessions: ${error.message}`,
        error.stack,
      );
      throw error; // The AllExceptionsFilter will handle sending the email
    }
  }

  async hasRemainingFreemiumTrialDays(data: {
    accountId?: number;
    accountSecureId?: string;
  }): Promise<boolean> {
    try {
      const { accountSecureId, accountId } = data;

      if (!accountSecureId && !accountId) {
        throw new Error('accountSecureId or accountId is required');
      }

      let resolvedAccountId = accountId;

      // If we only have secureId, resolve to accountId
      if (!resolvedAccountId && accountSecureId) {
        const account = await this.prismaService.accounts.findFirst({
          where: { secureId: accountSecureId },
          select: { id: true, companyName: true },
        });

        if (!account) {
          throw new PaymentRequiredException('Account não encontrada');
        }

        // Bypass for PlyrChat company
        if (account.companyName === 'PlyrChat') {
          return true;
        }

        resolvedAccountId = account.id;
      }

      // Check if user is MASTER (bypass all restrictions)
      const account = await this.prismaService.accounts.findFirst({
        where: { id: resolvedAccountId },
        include: {
          usersAccounts: {
            where: { isOwner: true },
            select: {
              role: { select: { slug: true } },
            },
          },
        },
      });

      if (account?.usersAccounts[0]?.role?.slug === 'MASTER') {
        return true;
      }

      // Check freemium trial status using the dedicated service
      const trialStatus = await this.freemiumSubscriptionService.getFreemiumTrialStatus(resolvedAccountId);

      // For freemium users, check if trial is still active
      if (trialStatus.hasActiveTrial) {
        return true;
      }

      // For non-freemium users, check if they have any active subscription
      const activeSubscription = await this.prismaService.subscriptions.findFirst({
        where: {
          accountId: resolvedAccountId,
          isActive: true,
          endsAt: { gte: new Date() },
          type: { not: 'freemium_expired' },
        },
      });

      return !!activeSubscription;
    } catch (error) {
      if (error instanceof PaymentRequiredException) {
        throw error;
      }

      this.logger.error(
        `Error in hasRemainingFreemiumTrialDays: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
