import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { ContactService } from './contact.service';
import { ContactController } from './contact.controller';

@Module({
  imports: [PrismaModule],
  controllers: [ContactController],
  providers: [ContactService],
})
export class ContactModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('contact');
  }
}
