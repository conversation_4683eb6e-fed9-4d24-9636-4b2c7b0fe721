import { CreateEFISubscriptionDto } from "../dto/create-efi-subscription.dto";
import { CreateEFISubscriptionVO } from "../value-object/create-efi-subscription.vo";

export class EFISubscriptionMapper {
  static fromCreateEfiSubscriptionVOToEfiSubscriptionDto(createEfiSubscriptionVO: CreateEFISubscriptionVO, holderName: string, notificationUrl: string): CreateEFISubscriptionDto {
    const phoneNumber = createEfiSubscriptionVO.user.phoneNumber ? createEfiSubscriptionVO.user.phoneNumber.slice(2) : '';
    const trial_days = createEfiSubscriptionVO.plan.trialDays !== 0 ? createEfiSubscriptionVO.plan.trialDays : undefined;
    return {
      items: [
        {
          name: createEfiSubscriptionVO.plan.name,
          value: createEfiSubscriptionVO.plan.value,
          amount: 1,
        }
      ],

      payment: {
        credit_card: {
          customer: {
            // name: createEfiSubscriptionVO.user.name,
            name: holderName,
            cpf: createEfiSubscriptionVO.user.cpf,
            email: createEfiSubscriptionVO.user.email,
            phone_number: phoneNumber,
          },
          payment_token: createEfiSubscriptionVO.paymentToken,
          trial_days: trial_days
        },
      },
      metadata: {
        custom_id: createEfiSubscriptionVO.subscriptionSecureId,
        notification_url: notificationUrl
      }
    }
  }
}