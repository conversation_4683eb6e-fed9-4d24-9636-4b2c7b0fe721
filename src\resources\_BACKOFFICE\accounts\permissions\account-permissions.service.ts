import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { AccountPermissionsMapper } from './mappers/account-permissions.mapper';

@Injectable()
export class AccountPermissionsService {
  constructor(private readonly prismaService: PrismaService) {}

  async getUserAccountPermissions(accountId: number, userId: number) {
    const userModel = await this.prismaService.users.findFirst({
      where: {
        id: userId,
      },

      select: {
        usersAccounts: {
          where: {
            accountId: accountId,
            userId: userId,
          },

          select: {
            accountsPermissions: {
              select: {
                permission: true,
              },
            },
          },
        },
      },
    });

    const permissions =
      AccountPermissionsMapper.fromModelToPermissionEntity(userModel);

    return permissions;
  }
}
