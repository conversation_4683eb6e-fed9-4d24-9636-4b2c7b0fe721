@apiBaseUrl = http://localhost:3333
@loginEndPoint = /login
@loginApi = {{apiBaseUrl}}{{loginEndPoint}}



### Cria um usuário
# @name createUser
POST {{apiBaseUrl}}/register
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "companyName": "Scola Holding",
    "password": "3z2io23m",
    "cpf": "***********",
    "cellPhone" : "***********"
}



### Login
# @name login
POST {{loginApi}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}



### TEste
# @name testeToken
POST {{loginApi}}/change-account
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8skQt8lvTHhQnbNQIwS8FDOP-wO3Ms8cUVnAjZnIJsA

{
  "accountSecureId": "f1980d59-8779-4a92-9cf5-ffb1889f60f6"
}



### Teste fulltoken
# @name testeFullToken
GET  {{loginApi}}/test
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6krpXCLGU85B5rJWRfU3tG3JBRHfuikmoiq8Unm4Cno
# Authorization: Bearer {{testeToken.response.body.accessToken}}

{}
