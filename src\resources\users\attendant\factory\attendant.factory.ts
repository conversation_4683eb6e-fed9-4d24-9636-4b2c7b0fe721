import { Prisma } from '@prisma/client';

import { AttendantOutputDto } from '../dto/attendant-output.dto';

type AttendantWithUsersAccountModel = Prisma.UsersGetPayload<{
  include: {
    usersAccounts: {
      where: {
        accountId: number;
      };

      select: {
        participatesInRotation: true;
        secureId: true;
        isActive: true;
      };
    };
  };
}>;

type AttendantWithUsersAccountAndPermissionsModel = Prisma.UsersGetPayload<{
  include: {
    usersAccounts: {
      where: {
        accountId: number;
      };

      select: {
        participatesInRotation: true;
        secureId: true;
        isActive: true;
        accountsPermissions?: {
          select: {
            permission: {
              select: {
                secureId: true;
                slug: true;
              };
            };
          };
        };
      };
    };
  };
}>;

export class AttendantFactory {
  static createBatchAttendantOutputDtoFromBatchModel(
    models: AttendantWithUsersAccountModel[],
  ): AttendantOutputDto[] {
    return models.map((model) => {
      return {
        secureId: model.secureId,

        name: model.name,
        email: model.email,
        cellPhone: model.cellPhone,

        isActive: model.isActive,

        currentAccount: {
          secureId: model.usersAccounts[0].secureId,
          isActive: model.usersAccounts[0].isActive,
          participatesInRotation: model.usersAccounts[0].participatesInRotation,
        },

        createdAt: model.createdAt,
        updatedAt: model.updatedAt,
      };
    });
  }

  static createOneBatchAttendantOutputDtoFromBatchModel(
    model: AttendantWithUsersAccountAndPermissionsModel,
  ): AttendantOutputDto {
    const hasAllPermissions = model.usersAccounts[0].accountsPermissions.some(
      (accPer) =>
        ['app_view', 'app_create', 'app_edit', 'app_delete'].includes(
          accPer.permission.slug,
        ),
    );

    const permissions = model.usersAccounts[0].accountsPermissions.map(
      (permission) => permission.permission.secureId,
    );

    return {
      secureId: model.secureId,

      name: model.name,
      email: model.email,
      cellPhone: model.cellPhone,
      cpf: model.cpf,

      isActive: model.isActive,

      currentAccount: {
        secureId: model.usersAccounts[0].secureId,
        isActive: model.usersAccounts[0].isActive,
        participatesInRotation: model.usersAccounts[0].participatesInRotation,
      },

      hasAllPermissions: hasAllPermissions,

      permissions: !hasAllPermissions ? permissions : undefined,

      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
