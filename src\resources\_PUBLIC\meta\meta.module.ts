import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { MetaService } from './meta.service';
import { MetaController } from './meta.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

import { MetaModule as ThirdPartyMetaModule } from 'src/third-party/meta/meta.module';

import { S3Module } from 'src/third-party/s3/s3.module';
import { OpenIAModule } from 'src/third-party/openIA/openIA.module';
import { ChatsModule } from 'src/resources/chats/chats.module';
import { AIModule } from 'src/resources/ia/ai.module';
import { RabbitMQModule } from 'src/third-party/rabbitmq/rabbitmq.module';
import { RabbitMQService } from 'src/third-party/rabbitmq/rabbitmq.service';
import { ConfigModule } from '@nestjs/config';
import { SubscriptionSessionModule } from 'src/@shared/services/subscription-session/subscription-session.module';
import { NotificationModule } from 'src/resources/notification/notification.module';

@Module({
  imports: [
    PrismaModule,
    ThirdPartyMetaModule,
    ChatsModule,
    S3Module,
    AIModule,
    OpenIAModule,
    RabbitMQModule,
    ConfigModule,
    SubscriptionSessionModule,
    NotificationModule,
  ],
  controllers: [MetaController],
  providers: [MetaService],
})
export class MetaModule implements NestModule {
  constructor(
    private readonly rabbitMQService: RabbitMQService,
    private readonly metaService: MetaService,
  ) {}

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('meta');
  }

  async onModuleInit() {
    await this.rabbitMQService.addConsumer(
      'meta-incoming-messages',
      async (msg) => {
        await this.metaService.processIncomingMetaMessage(msg);
      },
    );
  }
}
