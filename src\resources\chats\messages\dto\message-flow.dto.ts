export class MessageFlowInputDTO {
  userId?: string;
  sessionId: string;
  content: string;
  isAttendant: boolean;
  chatId?: string;
  customerName?: string;
  direction?: 'sent' | 'received';
  type?: 'audio' | 'text' | 'image' | 'file';
  fileSecureId?: string;
  urlFile?: string;
  customerId?: string;
  replyTo?: string;
}

export class MessageFlowOutputDTO {
  error?: string;
  success?: boolean;
  messageToSend?: {
    secureId: string;
    sendMessage: string;
    receiveMessage: string;
    messageType: string;
    urlFile?: string;
    messageDirection: string;
    sessionId: string;
    createdAt: Date;
    attendantName?: string;
  };
}
