// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider          = "mysql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

enum RoleSlug {
  MASTER
  BACKOFFICE
  APP
  ATTENDANT
}

enum RoleName {
  Master
  Backoffice
  App
  Attendant
}

enum PermissionSlug {
  backoffice_view
  backoffice_create
  backoffice_edit
  backoffice_delete

  app_view
  app_create
  app_edit
  app_delete

  attendant_chat_view
  attendant_chat_create
  attendant_chat_edit

  attendant_contact_view
  attendant_contact_create
  attendant_contact_edit
  attendant_contact_delete

  attendant_dashboard_view

  attendant_report_view

  attendant_configuration_view

  attendant_whatsapp_view
  attendant_whatsapp_create
  attendant_whatsapp_edit
  attendant_whatsapp_delete

  attendant_webchat_view
  attendant_webchat_create
  attendant_webchat_edit
  attendant_webchat_delete

  attendant_chatbot_view
  attendant_chatbot_create
  attendant_chatbot_edit
  attendant_chatbot_delete

  attendant_knowledge_base_create
  attendant_knowledge_base_delete

  attendant_attendant_view
  attendant_attendant_create
  attendant_attendant_edit
  attendant_attendant_delete

  backoffice_dashboard_view

  backoffice_account_view
  backoffice_account_edit

  backoffice_plans_view
  backoffice_plans_create
  backoffice_plans_edit
  backoffice_plans_delete

  backoffice_user_view
  backoffice_user_create
  backoffice_user_edit
  backoffice_user_delete
}

enum PermissionName {
  Backoffice_View   @map("Backoffice View")
  Backoffice_Create @map("Backoffice Create")
  Backoffice_Edit   @map("Backoffice Edit")
  Backoffice_Delete @map("Backoffice Delete")

  App_View   @map("App View")
  App_Create @map("App Create")
  App_Edit   @map("App Edit")
  App_Delete @map("App Delete")

  Attendant_Chat_View   @map("Attendant Chat View")
  Attendant_Chat_Create @map("Attendant Chat Create")
  Attendant_Chat_Edit   @map("Attendant Chat Edit")

  Attendant_Contact_View   @map("Attendant Contact View")
  Attendant_Contact_Create @map("Attendant Contact Create")
  Attendant_Contact_Edit   @map("Attendant Contact Edit")
  Attendant_Contact_Delete @map("Attendant Contact Delete")

  Attendant_Dashboard_View @map("Attendant Dashboard View")

  Attendant_Report_View @map("Attendant Report View")

  Attendant_Configuration_View @map("Attendant Configuration View")

  Attendant_WhatsApp_View   @map("Attendant WhatsApp View")
  Attendant_WhatsApp_Create @map("Attendant WhatsApp Create")
  Attendant_WhatsApp_Edit   @map("Attendant WhatsApp Edit")
  Attendant_WhatsApp_Delete @map("Attendant WhatsApp Delete")

  Attendant_WebChat_View   @map("Attendant WebChat View")
  Attendant_WebChat_Create @map("Attendant WebChat Create")
  Attendant_WebChat_Edit   @map("Attendant WebChat Edit")
  Attendant_WebChat_Delete @map("Attendant WebChat Delete")

  Attendant_ChatBot_View   @map("Attendant ChatBot View")
  Attendant_ChatBot_Create @map("Attendant ChatBot Create")
  Attendant_ChatBot_Edit   @map("Attendant ChatBot Edit")
  Attendant_ChatBot_Delete @map("Attendant ChatBot Delete")

  Attendant_Knowledge_Base_Create @map("Attendant Knowledge Base Create")
  Attendant_Knowledge_Base_Delete @map("Attendant Knowledge Base Delete")

  Attendant_Attendant_View   @map("Attendant Attendant View")
  Attendant_Attendant_Create @map("Attendant Attendant Create")
  Attendant_Attendant_Edit   @map("Attendant Attendant Edit")
  Attendant_Attendant_Delete @map("Attendant Attendant Delete")

  Backoffice_Dashboard_View @map("Backoffice Dashboard View")

  Backoffice_Account_View @map("Backoffice Account View")
  Backoffice_Account_Edit @map("Backoffice Account Edit")

  Backoffice_Plans_View   @map("Backoffice Plans View")
  Backoffice_Plans_Create @map("Backoffice Plans Create")
  Backoffice_Plans_Delete @map("Backoffice Plans Delete")
  Backoffice_Plans_Edit   @map("Backoffice Plans Edit")

  Backoffice_User_View   @map("Backoffice User View")
  Backoffice_User_Create @map("Backoffice User Create")
  Backoffice_User_Edit   @map("Backoffice User Edit")
  Backoffice_User_Delete @map("Backoffice User Delete")
}

enum PermissionGroup {
  backoffice

  app

  attendant
}

enum SubscriptionStatus {
  new
  active
  new_charge
  canceled
  expired
}

enum SubscriptionType {
  paid
  trial
  sponsored
  free
  freemium_trial
  freemium_expired
}

enum TransactionStatus {
  new
  waiting
  identified
  approved
  paid
  unpaid
  refunded
  contested
  canceled
  settled
  link
  expired
}

model Roles {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  name        RoleName @unique
  slug        RoleSlug @unique
  description String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  usersAccounts UsersAccounts[]

  @@index([id, secureId])
  @@map("roles")
}

model Permissions {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  name        PermissionName  @unique
  slug        PermissionSlug  @unique
  group       PermissionGroup
  description String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  accountsPermissions AccountsPermissions[]

  @@index([id, secureId, name, slug, group])
  @@map("permissions")
}

model Accounts {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  companyName  String  @unique @map("company_name") @db.VarChar(255)
  openaiApiKey String? @map("openai_api_key") @db.VarChar(255)
  prompt       String? @map("prompt") @db.Text

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  usersAccounts       UsersAccounts[]
  Chats               Chats[]
  ChatBots            ChatBots[]
  ChatSessions        ChatSessions[]
  subscriptions       Subscriptions[]
  whatsAppIntegration WhatsAppIntegration[]
  transactions        Transactions[]
  KnowledgeBase       KnowledgeBase[]
  Contact             Contacts[]
  Notifications       Notifications[]

  @@index([id, secureId, companyName, isActive])
  @@map("accounts")
}

model Users {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  name     String @map("name") @db.VarChar(255)
  email    String @unique @map("email") @db.VarChar(255)
  password String @map("password") @db.VarChar(255)

  cpf       String  @unique @map("cpf") @db.VarChar(20)
  cellPhone String? @map("cell_phone") @db.VarChar(20)

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  usersAccounts UsersAccounts[]
  userKeys      UserKey[]

  @@index([id, secureId, name, email, cpf, isActive])
  @@map("users")
}

model UsersAccounts {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  userId Int   @map("user_id") @db.Int
  user   Users @relation(fields: [userId], references: [id])

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  roleId Int   @map("role_id") @db.Int
  role   Roles @relation(fields: [roleId], references: [id])

  participatesInRotation Boolean @default(false) @map("participates_in_rotation")

  isOwner Boolean @default(false) @map("is_owner")

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  accountsPermissions AccountsPermissions[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  chatMessages  ChatMessages[]
  chatSessions  ChatSessions[]
  Notifications Notifications[]

  @@index([id, secureId, accountId, userId, isActive])
  @@map("users_accounts")
}

model UserKey {
  id Int @id @unique @default(autoincrement()) @db.Int

  userId Int   @map("user_id") @db.Int
  user   Users @relation(fields: [userId], references: [id])

  token     String    @map("token") @db.LongText
  expiresAt DateTime  @map("expired_at")
  usedAt    DateTime? @map("used_at")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([id, userId])
  @@map("user_keys")
}

model AccountsPermissions {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  userAccountId Int           @map("user_account_id") @db.Int
  userAccount   UsersAccounts @relation(fields: [userAccountId], references: [id])

  permissionId Int         @map("permission_id") @db.Int
  permission   Permissions @relation(fields: [permissionId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([id, secureId, userAccountId])
  @@map("accounts_permissions")
}

model Plans {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  name        String  @unique @db.VarChar(100)
  slug        String  @unique @db.VarChar(100)
  description String? @db.Text
  details     String? @db.Text
  price       String  @db.VarChar(100)
  interval    Int     @default(1) @map("interval")
  trialDays   Int     @default(0) @map("trial_days")

  attendantsLimit     Int @map("attendants_limit")
  whatsappNumberLimit Int @map("whatsapp_numbers_limit")
  chatbotsLimit       Int @map("chatbots_limit")
  knowledgeBaseLimit  Int @map("knowledge_base_limit")
  iaMessagesLimit     Int @map("ia_messages_limit")

  isActive Boolean @default(true) @map("is_active")

  efiPlanId    Int?    @map("efi_plan_id")
  efiIsDeleted Boolean @default(false) @map("efi_is_deleted")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  Subscriptions Subscriptions[]

  @@index([id, secureId])
  @@map("plans")
}

model Chats {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  uploadId Int?     @unique @map("upload_id") @db.Int
  upload   Uploads? @relation(fields: [uploadId], references: [id])

  name           String  @db.VarChar(255)
  welcomeMessage String? @map("welcome_message") @db.Text
  description    String? @db.Text

  webchatButtonBgColor        String? @map("webchat_button_bg_color") @db.VarChar(255)
  avatarBgColor               String? @map("avatar_bg_color") @db.VarChar(255)
  chatBgColor                 String? @map("chat_bg_color") @db.VarChar(255)
  inputChatBgColor            String? @map("input_chat_bg_color") @db.VarChar(255)
  inputChatTextColor          String? @map("input_chat_text_color") @db.VarChar(255)
  chatButtonColor             String? @map("chat_button_color") @db.VarChar(255)
  chatIconColor               String? @map("chat_icon_color") @db.VarChar(255)
  customerMessageBubbleColor  String? @map("customer_message_bubble_color") @db.VarChar(255)
  customerMessageTextColor    String? @map("customer_message_text_color") @db.VarChar(255)
  attendantMessageBubbleColor String? @map("attendant_message_bubble_color") @db.VarChar(255)
  attendantMessageTextColor   String? @map("attendant_message_text_color") @db.VarChar(255)
  messageTimeColor            String? @map("message_time_color") @db.VarChar(255)
  messageStatusColor          String? @map("message_status_color") @db.VarChar(255)

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  chatbotId Int?      @map("chatbot_id") @db.Int
  chatbot   ChatBots? @relation(fields: [chatbotId], references: [id])

  chatSessions ChatSessions[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([id, secureId, accountId, isActive])
  @@map("chats")
}

model ChatSessions {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  chatId     Int?                 @map("chat_id") @db.Int
  chat       Chats?               @relation(fields: [chatId], references: [id])
  whatsappId Int?                 @map("whatsapp_id") @db.Int
  whatsapp   WhatsAppIntegration? @relation(fields: [whatsappId], references: [id])

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  attendantId Int?           @map("attendant_id") @db.Int
  attendant   UsersAccounts? @relation(fields: [attendantId], references: [id])

  source             Int     @default(0) @db.Int
  customerId         String  @map("customer_id") @db.VarChar(255)
  customerName       String? @map("customer_name") @db.VarChar(255)
  customerEmail      String? @map("customer_email") @db.VarChar(255)
  customerPhone      String? @map("customer_phone") @db.VarChar(255)
  customerDocument   String? @map("customer_document") @db.VarChar(255)
  sessionDescription String? @map("session_description") @db.Text

  isAIResponder            Boolean @default(false) @map("is_ai_responder")
  isLeadCaptureMessageSent Boolean @default(false) @map("is_lead_capture_message_sent")
  isLeadCaptured           Boolean @default(false) @map("is_lead_captured")
  isActive                 Boolean @default(true) @map("is_active")
  isRead                   Boolean @default(false) @map("is_read")
  isDeleted                Boolean @default(false) @map("is_deleted")
  isArchived               Boolean @default(false) @map("is_archived")
  isFinalized              Boolean @default(false) @map("is_finalized")

  lastMessageAt DateTime? @map("last_message_at")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  chatMessages  ChatMessages[]
  Contacts      Contacts[]
  Notifications Notifications[]

  @@index([id, secureId, chatId, isActive, customerId, accountId, whatsappId, source])
  @@map("chat_sessions")
}

model Contacts {
  //EXISTE UMA TRIGGER RELACIONADA A ESSA TABELA NO BANCO DE DADOS, SEMPRE QUE ALTERAR ALGO, VERIFIQUE SE O FUNCOINAMENTO DA TRIGGER CONTINUA OK
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  name     String? @map("name") @db.VarChar(255)
  email    String? @map("email") @db.VarChar(255)
  phone    String? @map("phone") @db.VarChar(255)
  document String? @map("document") @db.VarChar(255)

  lastUpdatedSessionId Int?          @map("last_updated_session_id") @db.Int
  lastUpdatedSession   ChatSessions? @relation(fields: [lastUpdatedSessionId], references: [id])

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([accountId, email])
  @@index([accountId, phone])
  @@index([accountId, document])
  @@index([accountId, name])
  @@index([id, secureId, accountId])
  @@map("contacts")
}

enum Mood {
  neutral
  casual
  formal
  funny
  sarcastic
}

enum EmotionalTone {
  neutral
  empathetic
  motivational
  positive
  negative
  calm
}

enum ResponseStyle {
  objective
  detailed
}

enum ResponseSize {
  short
  medium
  long
}

model ChatBots {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)
  name     String @map("name") @db.VarChar(255)

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  isAI                    Boolean @default(false) @map("is_ai")
  isLeadCaptureActive     Boolean @default(false) @map("is_lead_capture_active")
  leadTriggerMessageLimit Int?    @map("lead_trigger_message_limit") @db.Int
  greetingMessage         String? @map("greeting_message") @db.Text

  IAPrompt                   String?        @map("ia_prompt") @db.Text
  leadCaptureJson            Json?          @map("lead_capture_json") @db.Json
  leadCaptureMessage         String?        @map("lead_capture_message") @db.Text
  leadCaptureThankYouMessage String?        @map("lead_capture_thank_you_message") @db.Text
  temperature                Int?           @map("temperature") @db.Int // 0 - 100 
  mood                       Mood?          @map("mood")
  emotionalTone              EmotionalTone? @map("emotional_tone")
  responseStyle              ResponseStyle? @map("response_style")
  responseSize               ResponseSize?  @map("response_size")

  inputToken  Int? @map("input_token") @db.Int
  outputToken Int? @map("output_token") @db.Int

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  chatMessages        ChatMessages[]
  Chats               Chats[]
  KnowledgeBase       KnowledgeBase?
  WhatsAppIntegration WhatsAppIntegration[]

  @@index([id, secureId, accountId, isActive])
  @@map("chat_bots")
}

model KnowledgeBase {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  collectionName String  @map("collection_name") @db.VarChar(255)
  content        String? @map("content") @db.LongText
  chunkSize      Int     @map("chunk_size")
  chunkOverlap   Int     @map("chunk_overlap")

  chatbotId Int?      @unique @map("chatbot_id") @db.Int
  chatbot   ChatBots? @relation(fields: [chatbotId], references: [id])

  isActive  Boolean @default(true) @map("is_active")
  isDeleted Boolean @default(false) @map("is_deleted")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([id, secureId, accountId])
  @@map("knowledge_base")
}

model ChatMessages {
  id         Int     @id @unique @default(autoincrement()) @db.Int
  messageJid String? @map("message_jid") @db.VarChar(255)
  secureId   String  @unique @map("secure_id") @db.VarChar(36)

  receiveMessage   String? @map("receive_message") @db.Text
  sendMessage      String? @map("send_message") @db.Text
  messageDirection String? @map("message_direction") @db.VarChar(255) // sent/received

  chatSessionId Int          @map("chat_session_id") @db.Int
  chatSession   ChatSessions @relation(fields: [chatSessionId], references: [id])

  userAccountId Int?           @map("user_account_id") @db.Int
  userAccount   UsersAccounts? @relation(fields: [userAccountId], references: [id])

  chatBotId Int?      @map("chat_bot_id") @db.Int
  chatBot   ChatBots? @relation(fields: [chatBotId], references: [id])

  uploadId Int?     @map("upload_id") @db.Int
  upload   Uploads? @relation(fields: [uploadId], references: [id])

  replyToId Int?           @map("reply_to_id") @db.Int
  replyTo   ChatMessages?  @relation("ReplyToRelation", fields: [replyToId], references: [id])
  replies   ChatMessages[] @relation("ReplyToRelation")

  inputToken  Int? @map("input_token") @db.Int
  outputToken Int? @map("output_token") @db.Int

  isActive    Boolean @default(true) @map("is_active")
  isDeleted   Boolean @default(false) @map("is_deleted")
  messageType String? @map("type") @db.VarChar(255) // audio/text/image/video

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([id, secureId, chatSessionId, chatBotId, isActive])
  @@map("chat_messages")
}

model Subscriptions {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  cycle  Int                @map("cycle") @db.Int
  status SubscriptionStatus @map("status")
  type   SubscriptionType   @map("type")

  planId            Int      @map("plan_id") @db.Int
  plan              Plans    @relation(fields: [planId], references: [id])
  accountId         Int      @map("account_id") @db.Int
  account           Accounts @relation(fields: [accountId], references: [id])
  remainingSessions Int?     @map("remaining_sessions") @db.Int

  gatewaySubscriptionId Int? @unique @map("gateway_subscription_id") @db.Int

  startsAt    DateTime  @map("starts_at")
  endsAt      DateTime  @map("ends_at")
  trialEndsAt DateTime? @map("trial_ends_at")
  canceledAt  DateTime? @map("canceled_at")

  isActive Boolean @default(true) @map("is_active")

  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")
  transactions Transactions[]

  @@index([id, secureId])
  @@map("subscriptions")
}

model WhatsAppIntegration {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  accountId Int       @map("account_id") @db.Int
  account   Accounts  @relation(fields: [accountId], references: [id])
  chatbotId Int?      @map("chatbot_id") @db.Int
  chatbot   ChatBots? @relation(fields: [chatbotId], references: [id])

  phoneNumber  String  @unique @map("phone_number") @db.VarChar(150)
  token        String? @map("token") @db.VarChar(255)
  numberId     String? @map("number_id") @db.VarChar(255)
  businessId   String? @map("business_id") @db.VarChar(255)
  instanceName String? @map("instance_name") @db.VarChar(36)
  instanceId   String? @map("instance_id") @db.VarChar(50)

  webhookToken String? @map("webhook_token") @db.VarChar(255)

  isBusiness Boolean @default(false) @map("is_business")
  isActive   Boolean @default(true) @map("is_active")
  isDeleted  Boolean @default(false) @map("is_deleted")

  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")
  ChatSessions ChatSessions[]

  @@index([id, secureId, isBusiness])
  @@map("whatsapp_integration")
}

model Uploads {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  fileName String @map("file_name") @db.VarChar(255)
  fileType String @map("file_type") @db.VarChar(255)

  type   String @map("type") @db.VarChar(255)
  urlCdn String @map("url_cdn") @db.VarChar(255)
  bucket String @map("bucket") @db.VarChar(255)

  isPrivate Boolean @default(false) @map("is_private")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  Chats        Chats?
  ChatMessages ChatMessages[]

  @@index([id, secureId, isPrivate])
  @@map("uploads")
}

model Transactions {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  accountId Int      @map("account_id") @db.Int
  account   Accounts @relation(fields: [accountId], references: [id])

  subscriptionId Int           @map("subscription_id") @db.Int
  subscription   Subscriptions @relation(fields: [subscriptionId], references: [id])

  gatewayId Int @unique @map("gateway_id") @db.Int

  amount  String            @map("amount") @db.VarChar(100)
  status  TransactionStatus @map("status")
  payedAt DateTime?         @map("payed_at")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([id, secureId])
  @@map("transactions")
}

model Notifications {
  id       Int    @id @unique @default(autoincrement()) @db.Int
  secureId String @unique @map("secure_id") @db.VarChar(36)

  userAccountId Int?           @map("user_account_id") @db.Int
  userAccount   UsersAccounts? @relation(fields: [userAccountId], references: [id])

  sessionId Int?          @map("session_id") @db.Int
  session   ChatSessions? @relation(fields: [sessionId], references: [id])

  title   String @map("title") @db.VarChar(255)
  message String @map("message") @db.Text

  isRead Boolean @default(false) @map("is_read")

  createdAt  DateTime  @default(now()) @map("created_at")
  Accounts   Accounts? @relation(fields: [accountsId], references: [id])
  accountsId Int?      @db.Int

  @@index([id, secureId])
  @@map("notifications")
}
