import * as Jo<PERSON> from 'joi';
import { registerAs } from '@nestjs/config';

import { IEVOApiEnvs } from './env.contract';

export default registerAs('evo-api-envs', () => {
  const values: IEVOApiEnvs = {
    apiKey: process.env.EVO_API_KEY,

    // baseUrl: process.env.SERVER_BASE_URL,
    baseUrl: process.env.EVO_BASE_URL,
  };

  const schema = Joi.object<IEVOApiEnvs>({
    apiKey: Joi.string().required().messages({
      'any.required': 'ENV: EVO_API_KEY is required',
    }),

    baseUrl: Joi.string().required().messages({
      'any.required': 'ENV: EVO_BASE_URL is required',
      // 'any.required': 'ENV: SERVER_BASE_URL is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
