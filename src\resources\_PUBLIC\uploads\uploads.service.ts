import { Inject, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { S3Service } from 'src/third-party/s3/s3.service';

@Injectable()
export class UploadsService {
  constructor(
    private readonly s3Service: S3Service,
    private readonly prismaService: PrismaService,
  ) {}

  async uploadFile(file: Express.Multer.File) {
    const uploadSecureId = await this.s3Service.uploadFile(file);
    const uploadedFile = await this.prismaService.uploads.findUnique({
      where: {
        secureId: uploadSecureId,
      },
      select: {
        secureId: true,
        urlCdn: true,
      },
    });

    return {
      secureId: uploadedFile.secureId,
      url: uploadedFile.urlCdn,
    };
  }
}
