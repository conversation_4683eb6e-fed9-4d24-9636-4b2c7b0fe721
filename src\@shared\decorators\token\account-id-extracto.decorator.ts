import {
  createParamDecorator,
  ExecutionContext,
  NotFoundException,
} from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

export const AccountIdExtractor = createParamDecorator(
  async (data: number | undefined, ctx: ExecutionContext): Promise<number> => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as ReceivedUserTokenPayload; // NOTE: A<PERSON>ho é onde o token é decodificado e o payload é retornado

    const prismaClient = request.prismaService;

    const currentAccountModel = await prismaClient.accounts.findFirst({
      where: {
        secureId: user?.activeAccount.accountSecureId,
        isActive: true,
        isDeleted: false,
      },
      select: { id: true },
    });
    if (!currentAccountModel) {
      throw new NotFoundException(
        'Nenhuma conta ativa foi encontrada para o usuário',
      );
    }

    return data ? user?.[data] : currentAccountModel.id;
  },
);
