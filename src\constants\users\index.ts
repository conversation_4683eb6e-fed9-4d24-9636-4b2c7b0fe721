import { v4 as uuidV4 } from 'uuid';

import { User } from 'src/resources/users/entities/user.entity';
import { accounts } from '../accounts';
import { roles } from '../roles';

import { GenerateUserData } from 'src/@shared/contracts/users/interface';

const plyrchatAccount = accounts.find(
  (account) => account.companyName === 'Plyrchat',
);

const masterRole = roles.find((role) => role.slug === 'MASTER');

// XXX: Cuidado ao alterar os dados contexto. Isso pode impactar diretamente no funcionamento do sistema.
// XXX: Ao criar o usuário é criado as users_permissions. Que são criadas comparando o email do usuário e a companyName da conta.
// XXX: Ou seja, se o email do usuário não for o mesmo da conta, as permissões serão duplicadas, não alterando a antiga.

// FIXME: Acredito que alterando o secureId para fixo ao invés de dinâmico, Em todos os contextos isso seria resolvido.

const rodrigoUser = new User({
  secureId: uuidV4(),
  name: '<PERSON>',
  email: '<EMAIL>',
  password: '3z2io23m',
  cpf: '***********',

  isActive: true,
  isDeleted: false,
}) as GenerateUserData;

Object.assign(rodrigoUser, {
  companyName: plyrchatAccount.companyName,
  roleSlug: masterRole.slug,
  isOwner: true,
});

export const users: GenerateUserData[] = [rodrigoUser];
