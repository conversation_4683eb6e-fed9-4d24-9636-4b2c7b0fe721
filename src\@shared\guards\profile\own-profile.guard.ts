import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o perfil pertence ao usuário logado.
 */
@Injectable()
export class OwnProfileGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;
    const params = context.switchToHttp().getRequest().params;
    if (!params.secureId) {
      throw new BadRequestException('Parâmetro secureId não encontrado');
    }

    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    if (params.secureId !== user.subject) {
      throw new ForbiddenException(
        'Você não pode acessar o perfil de outro usuário',
      );
    }

    return true;
  }
}
