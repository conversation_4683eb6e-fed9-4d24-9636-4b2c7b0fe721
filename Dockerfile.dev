
# Base image
FROM node:20.15.0

# Create app directory
WORKDIR /usr/src/app

# Expose port
EXPOSE 3333:3333

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package.json ./

# Install app dependencies
RUN yarn

# Bundle app source
COPY . .

ENV TZ="America/Sao_Paulo"
RUN date

# Creates a "dist" folder with the production build
RUN npx prisma generate && yarn build

# Start the server using the production build
# CMD tail -f /dev/null
# CMD env > /usr/src/app/.env && npx prisma generate && node dist/main.js

CMD node dist/main.js
