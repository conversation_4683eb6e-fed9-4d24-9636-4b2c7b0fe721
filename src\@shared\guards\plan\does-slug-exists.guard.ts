import {
  CanActivate,
  ExecutionContext,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

/**
 * @description Esse guard é responsável por verificar se o slug do plano existe.
 */
@Injectable()
export class DoesPlanSlugExistsGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const slug = request.params.slug;

    const plan = await this.prismaService.plans.findFirst({
      where: {
        slug: { equals: slug },
        isActive: true,
        efiIsDeleted: false,
      },
    });

    if (!plan) {
      throw new NotFoundException('Plano não encontrado');
    }

    return true;
  }
}
