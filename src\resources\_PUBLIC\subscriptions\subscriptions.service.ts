import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { EfiSubscriptionsService } from 'src/third-party/efi/subscriptions/efi-subscriptions.service';
import { ActiveSubscriptionDto } from './dto/find-active-subscription.dto';
import { SubscriptionStatus, SubscriptionType } from '@prisma/client';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class SubscriptionsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly efiSubscriptionService: EfiSubscriptionsService,
  ) { }

  async findActiveSubscription(
    accountId: number,
  ): Promise<ActiveSubscriptionDto> {
    const subscriptionModel = await this.prismaService.subscriptions.findFirst({
      where: {
        accountId: accountId,
        isActive: true,
        endsAt: { gte: new Date() },
      },
      select: {
        plan: {
          select: {
            secureId: true,
            name: true,
            slug: true,
            price: true,
            description: true,
            details: true,
          },
        }
      },
    })

    return {
      data: subscriptionModel,
    };
  }

  async cancelSubscription(accountId: number, userId: number) {
    try {
      const userAccountOwnerModel = await this.prismaService.usersAccounts.findFirst({
        where: {
          accountId: accountId,
          userId: userId,
          isOwner: true,
        }
      });

      if (!userAccountOwnerModel) {
        throw new NotFoundException('Apenas o proprietário da conta pode cancelar a assinatura');
      }

      const subscriptionModel = await this.prismaService.subscriptions.findFirst({
        where: {
          accountId: accountId,
          isActive: true,
        },
      });

      if (!subscriptionModel) {
        throw new NotFoundException('Assinatura não encontrada');
      }

      await this.prismaService.$transaction(async (prisma) => {
        // await this.efiSubscriptionService.cancelSubscription(subscriptionModel.gatewaySubscriptionId);

        await prisma.subscriptions.update({
          where: {
            secureId: subscriptionModel.secureId,
          },
          data: {
            isActive: false,
            status: 'canceled',
            canceledAt: new Date(),
          },
        });

        const freePlan = await prisma.plans.findFirst({
          where: {
            slug: 'freemium',
            isActive: true,
          },
        });

        if (!freePlan) {
          throw new InternalServerErrorException(
            'Plano gratuito não encontrado. Contate o administrador.',
          );
        }

        await prisma.subscriptions.create({
          data: {
            secureId: uuidV4(),
            account: {
              connect: {
                id: accountId
              }
            },
            plan: {
              connect: {
                id: freePlan.id
              }
            },
            remainingSessions: freePlan.iaMessagesLimit,
            cycle: 1,
            status: SubscriptionStatus.active,
            type: SubscriptionType.free,
            gatewaySubscriptionId: null,
            startsAt: new Date(),
            endsAt: new Date(
              new Date().setFullYear(new Date().getFullYear() + 10),
            ),
            isActive: true,
          },
        });
      });

      return;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw new InternalServerErrorException('Não foi possível cancelar a assinatura. Por favor, tente novamente mais tarde.');
    }
  }
}
