import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { EfiSubscriptionsService } from 'src/third-party/efi/subscriptions/efi-subscriptions.service';
import { ActiveSubscriptionDto } from './dto/find-active-subscription.dto';
import { SubscriptionStatus, SubscriptionType } from '@prisma/client';
import { FreemiumSubscriptionService } from 'src/@shared/services/freemium-subscription/freemium-subscription.service';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class SubscriptionsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly efiSubscriptionService: EfiSubscriptionsService,
    private readonly freemiumSubscriptionService: FreemiumSubscriptionService,
  ) { }

  async findActiveSubscription(
    accountId: number,
  ): Promise<ActiveSubscriptionDto> {
    const subscriptionModel = await this.prismaService.subscriptions.findFirst({
      where: {
        accountId: accountId,
        isActive: true,
        endsAt: { gte: new Date() },
      },
      select: {
        plan: {
          select: {
            secureId: true,
            name: true,
            slug: true,
            price: true,
            description: true,
            details: true,
          },
        }
      },
    })

    return {
      data: subscriptionModel,
    };
  }

  async cancelSubscription(accountId: number, userId: number) {
    try {
      const userAccountOwnerModel = await this.prismaService.usersAccounts.findFirst({
        where: {
          accountId: accountId,
          userId: userId,
          isOwner: true,
        }
      });

      if (!userAccountOwnerModel) {
        throw new NotFoundException('Apenas o proprietário da conta pode cancelar a assinatura');
      }

      const subscriptionModel = await this.prismaService.subscriptions.findFirst({
        where: {
          accountId: accountId,
          isActive: true,
        },
      });

      if (!subscriptionModel) {
        throw new NotFoundException('Assinatura não encontrada');
      }

      await this.prismaService.$transaction(async (prisma) => {
        // await this.efiSubscriptionService.cancelSubscription(subscriptionModel.gatewaySubscriptionId);

        await prisma.subscriptions.update({
          where: {
            secureId: subscriptionModel.secureId,
          },
          data: {
            isActive: false,
            status: 'canceled',
            canceledAt: new Date(),
          },
        });

        const freePlan = await prisma.plans.findFirst({
          where: {
            slug: 'freemium',
            isActive: true,
          },
        });

        if (!freePlan) {
          throw new InternalServerErrorException(
            'Plano gratuito não encontrado. Contate o administrador.',
          );
        }

        // Create freemium expired subscription (no AI features, only human attendant)
        await prisma.subscriptions.create({
          data: {
            secureId: uuidV4(),
            account: {
              connect: {
                id: accountId
              }
            },
            plan: {
              connect: {
                id: freePlan.id
              }
            },
            remainingSessions: 0, // No AI sessions for expired freemium
            cycle: 1,
            status: SubscriptionStatus.active,
            type: SubscriptionType.freemium_expired,
            gatewaySubscriptionId: null,
            startsAt: new Date(),
            endsAt: new Date(
              new Date().setFullYear(new Date().getFullYear() + 10),
            ),
            trialEndsAt: null, // No trial for expired freemium
            isActive: true,
          },
        });
      });

      return;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw new InternalServerErrorException('Não foi possível cancelar a assinatura. Por favor, tente novamente mais tarde.');
    }
  }

  async startFreemiumTrial(accountId: number, userId: number) {
    try {
      // Verify user is account owner
      const userAccountOwnerModel = await this.prismaService.usersAccounts.findFirst({
        where: {
          accountId: accountId,
          userId: userId,
          isOwner: true,
        }
      });

      if (!userAccountOwnerModel) {
        throw new NotFoundException('Apenas o proprietário da conta pode iniciar o trial freemium');
      }

      // Check if account already has an active freemium trial
      const hasActiveTrial = await this.freemiumSubscriptionService.hasActiveFreemiumTrial(accountId);
      if (hasActiveTrial) {
        throw new InternalServerErrorException('Esta conta já possui um trial freemium ativo');
      }

      // Check if account has any active subscription
      const activeSubscription = await this.prismaService.subscriptions.findFirst({
        where: {
          accountId: accountId,
          isActive: true,
        },
      });

      if (activeSubscription && activeSubscription.type !== 'freemium_expired') {
        throw new InternalServerErrorException('Esta conta já possui uma assinatura ativa');
      }

      // Create freemium trial subscription
      await this.freemiumSubscriptionService.createFreemiumTrialSubscription(accountId);

      return {
        message: 'Trial freemium iniciado com sucesso! Você tem 7 dias e 10 sessões de IA.',
      };
    } catch (error) {
      console.error('Erro ao iniciar trial freemium:', error);
      if (error instanceof NotFoundException || error instanceof InternalServerErrorException) {
        throw error;
      }
      throw new InternalServerErrorException('Não foi possível iniciar o trial freemium. Por favor, tente novamente mais tarde.');
    }
  }

  async getFreemiumTrialStatus(accountId: number) {
    try {
      const status = await this.freemiumSubscriptionService.getFreemiumTrialStatus(accountId);
      return {
        data: status,
      };
    } catch (error) {
      console.error('Erro ao buscar status do trial freemium:', error);
      throw new InternalServerErrorException('Não foi possível buscar o status do trial freemium.');
    }
  }
}
