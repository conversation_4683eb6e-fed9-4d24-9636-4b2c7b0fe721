import { Module } from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { AccountsController } from './accounts.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { AccountSubscriptionsModule } from './subscriptions/account-subscriptions.module';
import { AccountTransactionsModule } from './transactions/account-transactions.module';
import { AccountPermissionsModule } from './permissions/account-permissions.module';
import { AccountUsersModule } from './users/account-users.module';

@Module({
  imports: [
    PrismaModule,
    AccountSubscriptionsModule,
    AccountTransactionsModule,
    AccountPermissionsModule,
    AccountUsersModule,
  ],
  controllers: [AccountsController],
  providers: [AccountsService],
})
export class AccountsModule { }
