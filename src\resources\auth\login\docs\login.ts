import { ApiResponseNoStatusOptions } from '@nestjs/swagger';
import { LoginOutputDto } from '../dto/login-output.dto';

const created: ApiResponseNoStatusOptions = {
  type: LoginOutputDto,
  description: 'Retorna o access token',
  example: {
    accessToken: 'abc.abc.abc',
  },
  isArray: false,
  content: { 'application/json': {} },
};

const badRequest: ApiResponseNoStatusOptions = {
  examples: {
    emailRequired: {
      summary: 'E-mail não fornecido',
      value: {
        message: [
          {
            property: 'email',
            message: 'O email é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    emailInvalid: {
      summary: 'E-mail não é válido',
      value: {
        message: [
          {
            property: 'email',
            message: 'O email informado é inválido',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    passwordRequired: {
      summary: 'Senha não fornecida',
      value: {
        message: [
          {
            property: 'password',
            message: 'A senha é obrigatória',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },
  },
};

const notFound: ApiResponseNoStatusOptions = {
  examples: {
    userNotFound: {
      summary: 'Usuário não encontrado',
      value: {
        message: 'Usuário ou senha inválidos',
        error: 'Not Found',
        statusCode: 404,
      },
    },

    invalidPassword: {
      summary: 'Senha inválida',
      value: {
        message: 'Usuário ou senha inválidos',
        error: 'Not Found',
        statusCode: 404,
      },
    },
  },
};

export const login = {
  created,
  badRequest,
  notFound,
};
