import { Prisma } from '@prisma/client';
import { EvoIntegrationOutputDto } from '../dto/evo-integration-output.dto';

type WhatsAppIntegrationModelWithOptionalChatBot =
  Prisma.WhatsAppIntegrationGetPayload<{
    include: {
      chatbot?: true;
    };
  }>;

export class EvoIntegrationMapper {
  static fromModelToIntegrationDto(
    model: WhatsAppIntegrationModelWithOptionalChatBot,
  ): EvoIntegrationOutputDto {
    return {
      secureId: model.secureId,
      phoneNumber: model.phoneNumber,
      isBusiness: model.isBusiness,
      isActive: model.isActive,
      isDeleted: model.isDeleted,
      chatBotSecureId: model.chatbot?.secureId || null,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
