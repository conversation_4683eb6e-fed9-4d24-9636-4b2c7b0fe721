import { applyDecorators } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { backofficeUsersSwaggerApi } from '../docs/api';
import { swaggerShared } from 'src/@shared/docs/swagger';

function FindAll() {
  return applyDecorators(
    ApiOperation(backofficeUsersSwaggerApi.findAllUsers.apiOperation),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),

    ApiQuery(swaggerShared.query.page),
    ApiQuery(swaggerShared.query.limit),
    ApiQuery(swaggerShared.query.isActive),
    ApiQuery(swaggerShared.query.isDeleted),
    ApiQuery(backofficeUsersSwaggerApi.findAllUsers.apiQueryName),
    ApiQuery(backofficeUsersSwaggerApi.findAllUsers.apiQueryEmail),

    ApiOkResponse(backofficeUsersSwaggerApi.findAllUsers.apiOkResponse),
  );
}

function FindOne() {
  return applyDecorators(
    ApiOperation(backofficeUsersSwaggerApi.findUserBySecureId.apiOperation),

    ApiOkResponse(backofficeUsersSwaggerApi.findUserBySecureId.apiResponse),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),

    ApiQuery(backofficeUsersSwaggerApi.findUserBySecureId.apiQuerySecureId),

    ApiNotFoundResponse(
      backofficeUsersSwaggerApi.findUserBySecureId.apiNotFound,
    ),
  );
}

function Update() {
  return applyDecorators(
    ApiOperation(backofficeUsersSwaggerApi.userUpdate.apiOperation),

    ApiNoContentResponse(),

    ApiBadRequestResponse(backofficeUsersSwaggerApi.userUpdate.apiBadRequest),
    ApiNotFoundResponse(backofficeUsersSwaggerApi.userUpdate.apiNotFound),
    ApiConflictResponse(backofficeUsersSwaggerApi.userUpdate.apiConflict),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),

    ApiQuery(backofficeUsersSwaggerApi.userDelete.apiQuerySecureId),
  );
}

function Delete() {
  return applyDecorators(
    ApiOperation(backofficeUsersSwaggerApi.userDelete.apiOperation),

    ApiNoContentResponse(),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),

    ApiQuery(backofficeUsersSwaggerApi.userDelete.apiQuerySecureId),
  );
}

export const BackofficeUsersSwagger = {
  FindAll,
  FindOne,
  Update,
  Delete,
};
