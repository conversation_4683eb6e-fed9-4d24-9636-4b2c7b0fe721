import { v4 as uuidV4 } from 'uuid';

import { Permission } from 'src/resources/permissions/entities/permission.entity';

const appViewPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'App_View',
  slug: 'app_view',
  group: 'app',
  description: 'Usuário responsável pela conta pode ver qualquer coisa no app',
});

const appCreatePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'App_Create',
  slug: 'app_create',
  group: 'app',
  description:
    'Usuário responsável pela conta pode criar qualquer coisa no app',
});

const appEditPermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'App_Edit',
  slug: 'app_edit',
  group: 'app',
  description:
    'Usuário responsável pela conta pode editar qualquer coisa no app',
});

const appDeletePermission: Permission = new Permission({
  secureId: uuidV4(),
  name: 'App_Delete',
  slug: 'app_delete',
  group: 'app',
  description:
    'Usuário responsável pela conta pode apagar qualquer coisa no app',
});

export const appPermissions: Permission[] = [
  appViewPermission,
  appCreatePermission,
  appEditPermission,
  appDeletePermission,
];
