@baseUrl = http://localhost:3333

### SignIn
# @name GetToken
POST {{baseUrl}}/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

### Get All Plans
# @name GetAllPlans
GET {{baseUrl}}/plans
Authorization: Bearer {{GetToken.response.body.accessToken}}

### Create Plan
# @name CreatePlan
POST {{baseUrl}}/plans
Authorization: Bearer {{GetToken.response.body.accessToken}}
Content-Type: application/json

{
  "name": "Plano Teste",
  "slug": "plano-teste",
  "description": "Plano de teste",
  "value": 100.00,
  "attendantsLimit": 10,
  "whatsappNumberLimit": 10,
  "chatbotsLimit": 10,
  "knowledgeBaseLimit": 10,
  "iaMessagesLimit": 10,
  "isActive": true,
  "interval": 1,
  "details": "<ul><li>10 atendentes</li><li>10 números de whatsapp</li><li>10 chatbots</li><li>10 base de conhecimento</li><li>10 mensagens de IA</li></ul>"
}


###################################################################
############################################# TEST  ###############
###################################################################

### Get EFI TOKEN
GET {{baseUrl}}/plans/token
Authorization: Bearer {{GetToken.response.body.accessToken}}
