import {
  ApiOperationOptions,
  ApiResponseNoStatusOptions,
} from '@nestjs/swagger';
import { ProfileOutputDto } from '../../dto/profile-output.dto';

type ProfileFindOne = {
  apiOperation: ApiOperationOptions;
  apiOkResponse: ApiResponseNoStatusOptions;
};

export const profileFindOne: ProfileFindOne = {
  apiOperation: {
    summary: 'Busca dados do perfil do usuário logado.',
    description:
      'Utiliza os dados do token do usuário logado para puxas os dados do mesmo. Trazendo dados de User, UserAccount e Account de UserAccount',
  },

  apiOkResponse: {
    description: 'Retorna os dados do perfil do usuário logado.',
    type: ProfileOutputDto,
  },
};
