import { ChatOpenAI } from '@langchain/openai';
import { Inject } from '@nestjs/common';
import storageEnvs from '../env.config';
import { ConfigType } from '@nestjs/config';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { z } from 'zod';

export class AnalyzeSentimentService {
  private readonly llm: ChatOpenAI;

  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
  ) {
    this.llm = new ChatOpenAI({
      model: 'gpt-4o-mini',
      temperature: 0,
      apiKey: this.storageConfig.openApiKey,
    });
  }

  async analyzeConversationSentiment(
    messages: string[],
    alternativeApiKey?: string,
  ) {
    this.llm.apiKey = alternativeApiKey || this.storageConfig.openApiKey;

    const taggingPrompt = ChatPromptTemplate.fromTemplate(
      `Analyze the following conversation to extract the specified information.
	
			Focus on understanding the user's interest in the services and their overall sentiment.
	
			Only extract the properties mentioned in the 'Classification' function.
	
			Conversation:
			{input}
			`,
    );

    const classificationSchema = z.object({
      desire: z
        .number()
        .int()
        .min(1)
        .max(10)
        .describe(
          `Represents the level of interest or desire for services, where 1 indicates no interest and 10 indicates very high interest.`,
        ),
    });

    const llmWihStructuredOutput = this.llm.withStructuredOutput(
      classificationSchema,
      {
        name: 'analyze-sentiment',
      },
    );

    const extractionRunnable = taggingPrompt.pipe(llmWihStructuredOutput);

    const response = await extractionRunnable.invoke({
      input: messages.join('\n'),
    });

    return response;
  }
}
