type SubscriptionOrTransactionStatus =
  | 'new'
  | 'active'
  | 'new_charge'
  | 'canceled'
  | 'expired'
  | 'waiting'
  | 'identified'
  | 'approved'
  | 'paid'
  | 'unpaid'
  | 'refunded'
  | 'contested'
  | 'canceled'
  | 'settled'
  | 'link'
  | 'expired'

type SubscriptionData = {
  id: number;
  type: 'subscription' | 'subscription_charge';
  custom_id: string;
  status: {
    current: SubscriptionOrTransactionStatus;
    previous: SubscriptionOrTransactionStatus | null;
  };
  identifiers: {
    subscription_id: number;
    charge_id?: number;
  }
  created_at: string;

  value?: number;
  received_by_bank_at?: string;
}

export class EFIWebhookSubscriptionOutputDto {
  code: number;
  data: SubscriptionData[];
}
