import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class MigrateChatbotMessagesService {
  async execute(prisma: PrismaClient) {
    console.log('\x1b[33m', 'Buscando mensagens sem chat_bot_id...');

    // Busca todas as mensagens que não possuem chat_bot_id
    const messagesWithoutChatBotId = await prisma.chatMessages.findMany({
      where: {
        chatBotId: null,
        isDeleted: false,
      },
      select: {
        id: true,
        secureId: true,
        chatSessionId: true,
        chatSession: {
          select: {
            secureId: true,
            chat: {
              select: {
                chatbotId: true,
              },
            },
            whatsapp: {
              select: {
                chatbotId: true,
              },
            },
          },
        },
      },
    });

    console.log(
      '\x1b[33m',
      `Encontradas ${messagesWithoutChatBotId.length} mensagens para atualizar...`,
    );

    if (messagesWithoutChatBotId.length === 0) {
      console.log('\x1b[32m', 'Nenhuma mensagem precisa ser atualizada.');
      return;
    }

    let updatedCount = 0;
    let skippedCount = 0;

    // Processa as mensagens em lotes para melhor performance
    const batchSize = 100;
    for (let i = 0; i < messagesWithoutChatBotId.length; i += batchSize) {
      const batch = messagesWithoutChatBotId.slice(i, i + batchSize);
      
      console.log(
        '\x1b[36m',
        `Processando lote ${Math.floor(i / batchSize) + 1}/${Math.ceil(messagesWithoutChatBotId.length / batchSize)}...`,
      );

      for (const message of batch) {
        try {
          // Determina o chatBotId baseado na sessão
          const chatBotId = this.getChatBotIdFromSession(message.chatSession);

          if (chatBotId) {
            // Atualiza a mensagem com o chatBotId
            await prisma.chatMessages.update({
              where: { id: message.id },
              data: { chatBotId },
            });
            updatedCount++;
          } else {
            // Mensagem não possui chatbot associado
            skippedCount++;
            console.log(
              '\x1b[33m',
              `Mensagem ${message.secureId} não possui chatbot associado (sessão: ${message.chatSession.secureId})`,
            );
          }
        } catch (error) {
          console.error(
            '\x1b[31m',
            `Erro ao atualizar mensagem ${message.secureId}:`,
            error.message,
          );
          skippedCount++;
        }
      }
    }

    console.log('\x1b[32m', `\nResumo da migração:`);
    console.log('\x1b[32m', `- Mensagens atualizadas: ${updatedCount}`);
    console.log('\x1b[33m', `- Mensagens ignoradas: ${skippedCount}`);
    console.log('\x1b[32m', `- Total processado: ${updatedCount + skippedCount}`);
  }

  /**
   * Extrai o chatBotId da sessão seguindo a prioridade:
   * 1. chat.chatbotId (webchat)
   * 2. whatsapp.chatbotId (whatsapp)
   */
  private getChatBotIdFromSession(session: any): number | null {
    return session.chat?.chatbotId || session.whatsapp?.chatbotId || null;
  }
}
