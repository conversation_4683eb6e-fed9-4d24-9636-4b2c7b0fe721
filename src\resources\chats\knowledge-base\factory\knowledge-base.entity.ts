import { KnowledgeBase as KnowledgeBaseModel } from '@prisma/client';
import { KnowledgeBase } from '../entities/knowledge-base.entity';

export class KnowledgeBaseFactory {
  static convertBatchFromModelToKnowledgeBase(
    models: KnowledgeBaseModel[],
  ): KnowledgeBase[] {
    return models.map((model) => {
      const knowledgeBaseOutput = new KnowledgeBase({
        ...model,
      });

      return knowledgeBaseOutput;
    });
  }
}
