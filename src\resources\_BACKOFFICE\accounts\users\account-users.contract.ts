import { Prisma } from '@prisma/client';

export interface AccountUserDecoratorOutput {
  userName?: string;
  accountId?: number;
}

export type AccountUserWithUserAccount =
  Prisma.UsersGetPayload<{
    include: {
      usersAccounts: {
        select: {
          isOwner: true,
          roleId: true,
          accountsPermissions: {
            select: {
              permission: {
                select: {
                  secureId: true,
                  slug: true,
                }
              }
            }
          }
        }
      },
    },
  }>;
