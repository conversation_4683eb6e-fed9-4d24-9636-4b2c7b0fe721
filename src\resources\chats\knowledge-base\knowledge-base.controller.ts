import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Headers,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { KnowledgeBaseService } from './knowledge-base.service';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { IngestKnowledgeBaseDto } from './dto/ingest-knowledge-base.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('knowledge-base')
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_chatbot_view'])
  @Get(':secureId')
  async find(@Param('secureId') secureId: string) {
    return await this.knowledgeBaseService.find(secureId);
  }

  //Rota de alimentação da base de conhecimento
  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_create', 'attendant_knowledge_base_create'])
  @Post(':id')
  async create(@Body() body: IngestKnowledgeBaseDto, @Param('id') id: string) {
    await this.knowledgeBaseService.create(id, body.content);

    return { success: true, message: 'Knowledge base ingestion successful' };
  }
}
