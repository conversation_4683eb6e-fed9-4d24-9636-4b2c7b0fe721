import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { IEFIGetTokenEnvs } from './efi-get-token.contract';

export default registerAs('efi-get-token-envs', () => {
  const values: IEFIGetTokenEnvs = {
    clientId: process.env.EFI_CLIENT_ID,
    clientSecret: process.env.EFI_CLIENT_SECRET,

    baseUrl: process.env.EFI_BASE_URL,
    authorizeEP: '/v1/authorize',
  };

  const schema = Joi.object<IEFIGetTokenEnvs>({
    clientId: Joi.string().required().messages({
      'any.required': 'ENV: EFI_CLIENT_ID is required',
    }),
    clientSecret: Joi.string().required().messages({
      'any.required': 'ENV: EFI_CLIENT_SECRET is required',
    }),
    baseUrl: Joi.string().required().messages({
      'any.required': 'ENV: EFI_BASE_URL is required',
    }),
    authorizeEP: Joi.string().required().messages({
      'any.required':
        'Declaration: AuthorizeEP is not declared and is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
