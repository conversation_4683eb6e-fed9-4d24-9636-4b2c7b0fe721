## SERVER
SERVER_PORT=3333
SERVER_BASE_URL="https://localhost:3333"

# This was inserted by `prisma init`:
# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="mysql://root:root@localhost:3319/plyrchat"
SHADOW_DATABASE_URL="mysql://root:root@localhost:3320/plyrchat"

## SECURITY
HASH_ROUNDS=10
JWT_SECRET=secret
JWT_EXPIRES_IN=10d

## EFI
#### EFI SECRETS
EFI_CLIENT_ID="Client_Id_88_COMPLETE_ME"
EFI_CLIENT_SECRET="Client_Secret_25_COMPLETE_ME"

#### EFI ENDPOINTS
EFI_BASE_URL="https://cobrancas-h.api.efipay.com.br"

## EVO
#### EVO SECRETS
EVO_API_KEY="74e05e4a-d2b0-4e2b-ab24-0425829097e3"

#### EVO ENDPOINTS
EVO_BASE_URL="http://localhost:8080"

## S3
#### S3 ENVS
STORAGE_BUCKET_NAME="plyrchat"
STORAGE_REGION="us-east-1"
STORAGE_CDN_URL=""
STORAGE_ENDPOINT="https://nyc3.digitaloceanspaces.com"
STORAGE_ACCESS_KEY="QWERTYUIOPASDFGHJKL"
STORAGE_SECRET_KEY="QWERTYUIOPASDFGHJKLQWERTYUIOPASDFGHJKL"
STORAGE_ACL="public-read"
STORAGE_UPLOAD_DIR="teste"


### AI ENVS
QDRANT_BASE_URL="http://***************:6333"
QDRANT_API_KEY=""
QDRANT_BASE_PORT=""
OPENAI_API_KEY=""


META_API_URL="https://graph.facebook.com/v22.0/"

## ONE SIGNAL
ONE_SIGNAL_APP_ID="d6bcb167-ed54-4360-8009-30426aa5ac12"
ONE_SIGNAL_REST_API_KEY="os_v2_app_226lcz7nkrbwbaajgbbgvjnmciixianyxewu5yfkyblgyqxe6ltazkhfl7xvfjrxir7k3voklhcef7vcevt45pcrpvjxm6y4hz4i5jy"
ONE_SIGNAL_URL="https://plyrtech.com.br"

## APPLICATION MONITORING
ADMIN_EMAIL="<EMAIL>"

