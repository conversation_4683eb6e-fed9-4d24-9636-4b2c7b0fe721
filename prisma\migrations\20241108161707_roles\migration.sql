-- CreateTable
CREATE TABLE `roles` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `name` <PERSON>NU<PERSON>('Master', 'Backoffice', 'Partner', 'Insighter') NOT NULL,
    `slug` ENUM('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>RTNE<PERSON>', 'IN<PERSON><PERSON>HT<PERSON>') NOT NULL,
    `description` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `roles_id_key`(`id`),
    UNIQUE INDEX `roles_secure_id_key`(`secure_id`),
    UNIQUE INDEX `roles_name_key`(`name`),
    UNIQUE INDEX `roles_slug_key`(`slug`),
    INDEX `roles_id_secure_id_idx`(`id`, `secure_id`),
    PRIMAR<PERSON>EY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
