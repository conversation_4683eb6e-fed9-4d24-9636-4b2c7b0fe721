import {
  createParamDecorator,
  ExecutionContext,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

export const WppIntegrationInfosExtractor = createParamDecorator(
  async (
    data: number | undefined,
    ctx: ExecutionContext,
  ): Promise<{
    accountId: number;
    wppIntegrationId: number;
  }> => {
    const request = ctx.switchToHttp().getRequest();
    const wppIntegrationSecureId = request.query.secureId as string;
    const prismaClient = request.prismaService as PrismaClient;

    if (!wppIntegrationSecureId) {
      throw new NotFoundException('secureId não encontrado na requisição');
    }

    const currentWppIntegrationModel =
      await prismaClient.whatsAppIntegration.findFirst({
        where: {
          secureId: wppIntegrationSecureId,
          isActive: true,
          isDeleted: false,
          account: {
            isActive: true,
            isDeleted: false,
          },
        },
        select: { id: true, accountId: true },
      });

    if (!currentWppIntegrationModel) {
      throw new ForbiddenException(
        'Nenhuma integração ativa foi encontrada, ou a conta não está ativa',
      );
    }

    return {
      accountId: currentWppIntegrationModel.accountId,
      wppIntegrationId: currentWppIntegrationModel.id,
    };
  },
);
