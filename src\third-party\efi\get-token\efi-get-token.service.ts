import axios from 'axios';
import { ConfigType } from '@nestjs/config';
import {
  Inject,
  Injectable,
  ServiceUnavailableException,
} from '@nestjs/common';

import { EFIAuthenticatedToken } from '../entities/token/authenticated-token.entity';

import efiEnvs from './env.config';
import { EFIAuthenticationInputDto } from './dto/authentication-input.dto';

@Injectable()
export class EFIGetTokenService {
  constructor(
    @Inject(efiEnvs.KEY)
    private readonly efiEnvModule: ConfigType<typeof efiEnvs>,
  ) {}

  async getToken(): Promise<string> {
    const signInUrl = this.efiEnvModule.baseUrl + this.efiEnvModule.authorizeEP;
    const singInBody = {
      grant_type: 'client_credentials',
    };

    try {
      const response = await axios.post<EFIAuthenticationInputDto>(
        signInUrl,
        singInBody,
        {
          auth: {
            username: this.efiEnvModule.clientId,
            password: this.efiEnvModule.clientSecret,
          },
        },
      );

      const efiToken = new EFIAuthenticatedToken(response.data);

      return `Bearer ${efiToken.accessToken}`;
    } catch (error) {
      console.log(error);
      throw new ServiceUnavailableException('Erro ao tentar autenticar no EFI');
    }
  }
}
