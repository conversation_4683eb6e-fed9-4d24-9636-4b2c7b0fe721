-- DropForeign<PERSON>ey
ALTER TABLE `notifications` DROP FOREIGN KEY `notifications_user_account_id_fkey`;

-- AlterTable
ALTER TABLE `notifications` ADD COLUMN `accountsId` INTEGER NULL,
    MODIFY `user_account_id` INTEGER NULL;

-- AddForeignKey
ALTER TABLE `notifications` ADD CONSTRAINT `notifications_user_account_id_fkey` FOREIGN KEY (`user_account_id`) REFERENCES `users_accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `notifications` ADD CONSTRAINT `notifications_accountsId_fkey` FOREIGN KEY (`accountsId`) REFERENCES `accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
