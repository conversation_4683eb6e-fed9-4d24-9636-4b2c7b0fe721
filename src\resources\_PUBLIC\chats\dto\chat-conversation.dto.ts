import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class ChatMessage {
  @IsString()
  @IsNotEmpty()
  role: string;

  @IsString()
  @IsOptional()
  content: string;
}

export class ChatConversationInputDto {
  @IsNotEmpty({ message: 'As mensagens são obrigatórias' })
  @IsArray({ message: 'As mensagens devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => ChatMessage)
  messages: ChatMessage[];

  @IsNotEmpty({ message: 'O identificador da sessão é obrigatório' })
  @IsString({ message: 'O identificador da sessão deve ser uma string' })
  sessionSecureId: string;

  @IsNotEmpty({ message: 'O identificador do usuário é obrigatório' })
  @IsString({ message: 'O identificador do usuário deve ser uma string' })
  userSecureId: string;
}
