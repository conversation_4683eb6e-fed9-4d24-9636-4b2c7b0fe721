import { v4 as uuidV4 } from 'uuid';

import { Role } from 'src/resources/roles/entities/role.entity';
import { Account } from 'src/resources/_BACKOFFICE/accounts/entities/account.entity';
import { Permission } from 'src/resources/permissions/entities/permission.entity';

type UserAccount = {
  account: Account;
  role: Role;
  permissions: Permission[];
};

type UserConstructorProps = {
  id?: number;
  secureId?: string;

  name: string;
  email: string;
  password: string;
  cpf: string;
  cellPhone?: string;

  isActive?: boolean;
  isDeleted?: boolean;

  userAccounts?: UserAccount[];

  createdAt?: Date;
  updatedAt?: Date;
};

type UserJson = {
  secureId: string;

  name: string;
  email: string;
  cpf: string;
  cellPhone: string;

  isActive: boolean;
  isDeleted: boolean;

  userAccounts: UserAccount[];

  createdAt: Date;
  updatedAt: Date;
};

export class User {
  id?: number;
  secureId?: string;

  name: string;
  email: string;
  password: string;
  cpf: string;
  cellPhone?: string;

  isActive: boolean;
  isDeleted: boolean;

  userAccounts?: UserAccount[];

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: UserConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.name = props.name;
    this.email = props.email;
    this.password = props.password;
    this.cpf = props.cpf;
    this.cellPhone = props?.cellPhone ? props.cellPhone : undefined;

    this.isActive = props.isActive === false ? false : true;
    this.isDeleted = props.isDeleted === true ? true : false;

    this.userAccounts = props?.userAccounts ? props.userAccounts : undefined;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  changePassword(newPassword: string) {
    this.password = newPassword;
  }

  activate() {
    this.isActive = true;
  }

  deactivate() {
    this.isActive = false;
  }

  delete() {
    this.isDeleted = true;
  }

  restore() {
    this.isDeleted = false;
  }

  toJSON(): UserJson {
    return {
      secureId: this.secureId,

      name: this.name,
      email: this.email,
      cpf: this.cpf,
      cellPhone: this.cellPhone,

      isActive: this.isActive,
      isDeleted: this.isDeleted,

      userAccounts: this.userAccounts,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
