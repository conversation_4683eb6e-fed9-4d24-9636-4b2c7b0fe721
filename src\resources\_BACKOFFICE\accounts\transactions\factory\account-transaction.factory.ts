import { FormatMoney } from 'src/@shared/helpers/money/format-money';
import { AccountTransactionWithAccountAndSubscription } from '../account-transaction.contract';
import { AccountTransactionOutputDto } from '../dto/create-transaction.dto';

export class AccountTransactionsFactory {
  static createAccountTransactionOutputDtoFromAccountTransactionWithAccountAndSubscriptionBatch(
    input: AccountTransactionWithAccountAndSubscription[],
  ): AccountTransactionOutputDto[] {
    return input.map((accountTransactionWithAccountAndSubscription) => {
      return {
        secureId: accountTransactionWithAccountAndSubscription.secureId,

        amount: FormatMoney.fromModelToOutput(
          accountTransactionWithAccountAndSubscription.amount,
        ),
        status: accountTransactionWithAccountAndSubscription.status,

        account: {
          secureId:
            accountTransactionWithAccountAndSubscription.account.secureId,

          companyName:
            accountTransactionWithAccountAndSubscription.account.companyName,

          isActive:
            accountTransactionWithAccountAndSubscription.account.isActive,

          isDeleted:
            accountTransactionWithAccountAndSubscription.account.isDeleted,

          createdAt:
            accountTransactionWithAccountAndSubscription.account.createdAt,
          updatedAt:
            accountTransactionWithAccountAndSubscription.account.updatedAt,
        },

        subscription: {
          secureId:
            accountTransactionWithAccountAndSubscription.subscription.secureId,

          cycle:
            accountTransactionWithAccountAndSubscription.subscription.cycle,
          status:
            accountTransactionWithAccountAndSubscription.subscription.status,
          type: accountTransactionWithAccountAndSubscription.subscription.type,

          gatewaySubscriptionId:
            accountTransactionWithAccountAndSubscription.subscription
              .gatewaySubscriptionId,

          startsAt:
            accountTransactionWithAccountAndSubscription.subscription.startsAt,
          endsAt:
            accountTransactionWithAccountAndSubscription.subscription.endsAt,
          canceledAt:
            accountTransactionWithAccountAndSubscription.subscription
              .canceledAt,

          createdAt:
            accountTransactionWithAccountAndSubscription.subscription.createdAt,
          updatedAt:
            accountTransactionWithAccountAndSubscription.subscription.updatedAt,
        },

        payedAt: accountTransactionWithAccountAndSubscription.payedAt,
        createdAt: accountTransactionWithAccountAndSubscription.createdAt,
        updatedAt: accountTransactionWithAccountAndSubscription.updatedAt,
      };
    });
  }
}
