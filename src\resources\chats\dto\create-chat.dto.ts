import {
  IsBoolean,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON>ptional,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateChatInputDto {
  @MinLength(3, {
    message: 'O Nome da empresa deve ter no mínimo 3 caracteres',
  })
  @MaxLength(255, {
    message: 'O Nome da empresa deve ter no máximo 255 caracteres',
  })
  @IsString({ message: 'O nome do chat deve ser uma string' })
  @IsNotEmpty({ message: 'O nome do chat é obrigatório' })
  name: string;

  @IsString({ message: 'O account secure id deve ser uma string' })
  @IsNotEmpty({ message: 'O account secure id é obrigatório' })
  accountSecureId: string;

  @IsString({ message: 'O identificador do chatbot deve ser uma string' })
  @IsOptional()
  chatBotSecureId: string;

  @IsString({ message: 'A mensagem de boas-vindas deve ser uma string' })
  @IsOptional()
  welcomeMessage?: string;

  @IsString({ message: 'A descrição deve ser uma string' })
  @IsOptional()
  description?: string;

  @IsString({ message: 'A cor do chat deve ser uma string' })
  @IsOptional()
  inputChatBgColor?: string;

  @IsString({ message: 'A cor do texto do chat deve ser uma string' })
  @IsOptional()
  inputChatTextColor?: string;

  @IsString({
    message: 'A cor do balão de mensagem do atendente deve ser uma string',
  })
  @IsOptional()
  customerMessageBubbleColor?: string;

  @IsString({
    message: 'A cor do balão de mensagem do cliente deve ser uma string',
  })
  @IsOptional()
  attendantMessageBubbleColor?: string;

  @IsString({
    message:
      'A cor do texto do balão de mensagem do atendente deve ser uma string',
  })
  @IsOptional()
  attendantMessageTextColor?: string;

  @IsString({
    message:
      'A cor do texto do balão de mensagem do cliente deve ser uma string',
  })
  @IsOptional()
  customerMessageTextColor?: string;

  @IsString({
    message: 'A cor do fundo do chat deve ser uma string',
  })
  @IsOptional()
  chatBgColor?: string;

  @IsString({
    message: 'A cor do botão de chat deve ser uma string',
  })
  @IsOptional()
  chatButtonColor?: string;

  @IsString({
    message: 'A cor do ícone do chat deve ser uma string',
  })
  @IsOptional()
  chatIconColor?: string;

  @IsString({
    message: 'A cor do texto do chat deve ser uma string',
  })
  @IsOptional()
  webchatButtonBgColor?: string;

  @IsString({
    message: 'A cor do avatar deve ser uma string',
  })  
  @IsOptional()
  avatarBgColor?: string;

  @IsBoolean({ message: 'Deve estar no formato booleano.' })
  @IsOptional()
  isActive: boolean;
}
