import { v4 as uuidV4 } from 'uuid';

import { Entity } from 'src/@shared/contracts/entity/interface';
import { Plan } from 'src/resources/_BACKOFFICE/plans/entities/plan.entity';

import {
  SubscriptionStatus,
  SubscriptionType,
} from 'src/@shared/types/subscriptions';

type SubscriptionConstructorProps = {
  id?: number;
  secureId?: string;

  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;

  planId: number;

  gatewaySubscriptionId: number;

  startsAt: Date;
  endsAt: Date;
  canceledAt: Date | null;

  createdAt?: Date;
  updatedAt?: Date;
};

type SubscriptionJson = {
  secureId: string;

  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;

  plan: Plan;

  gatewaySubscriptionId: number;

  startsAt: Date;
  endsAt: Date;
  canceledAt: Date | null;

  createdAt: Date;
  updatedAt: Date;
};

export class Subscription implements Entity {
  id?: number;
  secureId: string;

  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;

  planId: number;
  plan: Plan;

  gatewaySubscriptionId: number;

  startsAt: Date;
  endsAt: Date;
  canceledAt: Date | null;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: SubscriptionConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.cycle = props.cycle;
    this.status = props.status;
    this.type = props.type;

    this.planId = props.planId;

    this.gatewaySubscriptionId = props.gatewaySubscriptionId;

    this.startsAt = props.startsAt;
    this.endsAt = props.endsAt;
    this.canceledAt = props.canceledAt;

    this.createdAt = props?.createdAt ? props.createdAt : undefined;
    this.updatedAt = props?.updatedAt ? props.updatedAt : undefined;
  }

  toJSON(): SubscriptionJson {
    return {
      secureId: this.secureId,

      cycle: this.cycle,
      status: this.status,
      type: this.type,

      plan: this.plan,

      gatewaySubscriptionId: this.gatewaySubscriptionId,

      startsAt: this.startsAt,
      endsAt: this.endsAt,
      canceledAt: this.canceledAt,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
