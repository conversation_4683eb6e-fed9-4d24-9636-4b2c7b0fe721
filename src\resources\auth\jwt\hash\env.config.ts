import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { IHashEnvs } from './hash.contract';

export default registerAs('hash-envs', () => {
  const values: IHashEnvs = {
    rounds: Number(process.env.HASH_ROUNDS),
  };

  const schema = Joi.object<IHashEnvs>({
    rounds: Joi.number().required().messages({
      'any.required': 'ENV: HASH_ROUNDS is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
