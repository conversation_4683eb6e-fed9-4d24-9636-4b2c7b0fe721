-- CreateTable
CREATE TABLE `transactions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `account_id` INTEGER NOT NULL,
    `subscription_id` INTEGER NOT NULL,
    `gateway_id` INTEGER NOT NULL,
    `amount` INTEGER NOT NULL,
    `status` ENUM('new', 'waiting', 'identified', 'approved', 'paid', 'unpaid', 'refunded', 'contested', 'canceled', 'settled', 'link', 'expired') NOT NULL,
    `payed_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `transactions_id_key`(`id`),
    UNIQUE INDEX `transactions_secure_id_key`(`secure_id`),
    INDEX `transactions_id_secure_id_idx`(`id`, `secure_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `transactions` ADD CONSTRAINT `transactions_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `transactions` ADD CONSTRAINT `transactions_subscription_id_fkey` FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
