import { Controller, Post, Body } from '@nestjs/common';
import { WebhookEfiNotificationService } from './webhook-efi-notification.service';

@Controller('webhook-efi-notification')
export class WebhookEfiNotificationController {
  constructor(
    private readonly webhookEfiNotificationService: WebhookEfiNotificationService,
  ) { }

  @Post()
  async create(@Body() notificationIdObject: { notification: string }) {
    const notificationToken = notificationIdObject.notification;
    return this.webhookEfiNotificationService.processNotification(notificationToken);
  }
}
