import { AccountSubscriptionWithPlanAndAccount } from '../account-subscriptions.contracts';
import { AccountSubscriptionOutputDto } from '../dto/account-subscription.output.dto';

export class AccountSubscriptionFactory {
  static createAccountSubscriptionOutputDtoFromAccountWithPlanBatch(
    accountSubscriptionWithPlan: AccountSubscriptionWithPlanAndAccount[],
  ): AccountSubscriptionOutputDto[] {
    return accountSubscriptionWithPlan.map((subscription) => {
      return {
        secureId: subscription.secureId,
        cycle: subscription.cycle,
        status: subscription.status,
        type: subscription.type,
        plan: {
          secureId: subscription.plan.secureId,
          name: subscription.plan.name,
          isActive: subscription.plan.isActive,
          createdAt: subscription.plan.createdAt,
          updatedAt: subscription.plan.updatedAt,
        },

        account: {
          secureId: subscription.account.secureId,
          companyName: subscription.account.companyName,
          isActive: subscription.account.isActive,
          isDeleted: subscription.account.isDeleted,
          createdAt: subscription.account.createdAt,
          updatedAt: subscription.account.updatedAt,
        },

        gatewaySubscriptionId: subscription.gatewaySubscriptionId,
        startsAt: subscription.startsAt,
        endsAt: subscription.endsAt,
        canceledAt: subscription.canceledAt,
        createdAt: subscription.createdAt,
        updatedAt: subscription.updatedAt,
      };
    });
  }
}
