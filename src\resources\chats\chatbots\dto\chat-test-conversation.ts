import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class ChatMessage {
  @IsString()
  @IsNotEmpty()
  role: string;

  @IsString()
  @IsNotEmpty()
  content: string;
}

export class ChatBotTestConversationInputDto {
  @IsNotEmpty({ message: 'As mensagens são obrigatórias' })
  @IsArray({ message: 'As mensagens devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => ChatMessage)
  messages: ChatMessage[];

  @IsOptional()
  chatbotSecureId: string;
}
