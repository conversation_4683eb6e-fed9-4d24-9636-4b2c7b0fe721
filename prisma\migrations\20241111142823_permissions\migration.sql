-- CreateTable
CREATE TABLE `permissions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'Partner View', 'Partner Create', 'Partner Edit', 'Partner Delete', 'Insighter View', 'Insighter Create', 'Insighter Edit', 'Insighter Delete') NOT NULL,
    `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'partner_view', 'partner_create', 'partner_edit', 'partner_delete', 'insighter_view', 'insighter_create', 'insighter_edit', 'insighter_delete') NOT NULL,
    `group` ENUM('backoffice', 'partner', 'insighter') NOT NULL,
    `description` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `permissions_id_key`(`id`),
    UNIQUE INDEX `permissions_secure_id_key`(`secure_id`),
    UNIQUE INDEX `permissions_name_key`(`name`),
    UNIQUE INDEX `permissions_slug_key`(`slug`),
    INDEX `permissions_id_secure_id_name_slug_group_idx`(`id`, `secure_id`, `name`, `slug`, `group`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
