import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import axios from 'axios';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { EFIGetTokenService } from 'src/third-party/efi/get-token/efi-get-token.service';
import { EFIWebhookSubscriptionOutputDto } from './dto/efi-webhook-notification-output.dto';
import { SubscriptionStatus, TransactionStatus } from '@prisma/client';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class WebhookEfiNotificationService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly efiTokenService: EFIGetTokenService,
  ) {}

  async processNotification(notificationToken: string) {
    const efiBearerToken = await this.efiTokenService.getToken();

    try {
      const response = await axios.get<EFIWebhookSubscriptionOutputDto>(
        `${process.env.EFI_BASE_URL}/v1/notification/${notificationToken}`,
        {
          headers: {
            Authorization: efiBearerToken,
          },
        },
      );
      // const response = await axios.post<EFIWebhookSubscriptionOutputDto>( // TESTE
      //   `http://localhost:3333/notification-teste`,
      // )
      const notificationData = response.data?.data;

      if (!notificationData || notificationData.length === 0) {
        throw new NotFoundException(
          'Nenhuma notificação encontrada para essa assinatura.',
        );
      }

      const validTransactionStatuses: TransactionStatus[] = [
        'new',
        'waiting',
        'identified',
        'approved',
        'paid',
        'unpaid',
        'refunded',
        'contested',
        'canceled',
        'settled',
        'link',
        'expired',
      ];

      const validSubscriptionStatuses: SubscriptionStatus[] = [
        'new',
        'active',
        'new_charge',
        'canceled',
        'expired',
      ];

      // Filtrar atualizações de subscription
      const subscriptionUpdates = notificationData.filter(
        (item) => item.type === 'subscription',
      );

      for (const subscription of subscriptionUpdates) {
        const { custom_id: subscription_id, status } = subscription;
        const currentStatus = status?.current;

        if (
          !subscription_id ||
          !currentStatus ||
          !validSubscriptionStatuses.includes(
            currentStatus as SubscriptionStatus,
          )
        ) {
          throw new InternalServerErrorException(
            'Status inválido ou dados incompletos na notificação de subscription.',
          );
        }

        const existingSubscription =
          await this.prismaService.subscriptions.findUnique({
            where: { secureId: subscription_id },
            include: {
              plan: {
                select: {
                  iaMessagesLimit: true,
                },
              },
            },
          });

        if (!existingSubscription) {
          throw new NotFoundException('Assinatura não encontrada');
        }

        const updateData: any = { status: currentStatus as SubscriptionStatus };

        if (currentStatus === 'new_charge') {
          updateData.cycle = existingSubscription.cycle + 1;
          updateData.startsAt = new Date();
          updateData.remainingSessions =
            existingSubscription.plan.iaMessagesLimit;
          updateData.endsAt = new Date(
            new Date().setMonth(new Date().getMonth() + 1),
          );
        }

        if (['canceled', 'expired'].includes(currentStatus)) {
          updateData.canceledAt = new Date();
          updateData.isActive = false;
        }

        await this.prismaService.subscriptions.update({
          where: { secureId: existingSubscription.secureId },
          data: updateData,
        });
      }

      // Filtrar atualizações de subscription_charge (transactions)
      const chargeUpdates = notificationData.filter(
        (item) => item.type === 'subscription_charge',
      );
      for (const charge of chargeUpdates) {
        const { identifiers, status, created_at } = charge;
        const currentStatus = status?.current as TransactionStatus;
        const chargeId = identifiers?.charge_id;
        const subscriptionId = identifiers?.subscription_id;

        if (
          !chargeId ||
          !currentStatus ||
          !validTransactionStatuses.includes(currentStatus)
        ) {
          throw new InternalServerErrorException(
            'Status inválido ou dados incompletos na notificação de charge.',
          );
        }

        const existingTransaction =
          await this.prismaService.transactions.findUnique({
            where: { gatewayId: chargeId },
          });

        if (existingTransaction) {
          await this.prismaService.transactions.update({
            where: { id: existingTransaction.id },
            data: {
              status: currentStatus,
              ...(currentStatus === 'paid' && { payedAt: new Date() }),
            },
          });
        } else {
          const relatedSubscription =
            await this.prismaService.subscriptions.findUnique({
              where: { gatewaySubscriptionId: subscriptionId },
              include: { plan: true },
            });

          if (!relatedSubscription) {
            throw new NotFoundException(
              'Assinatura relacionada não encontrada para charge',
            );
          }

          await this.prismaService.transactions.create({
            data: {
              secureId: uuidV4(),
              accountId: relatedSubscription.accountId,
              subscriptionId: relatedSubscription.id,
              gatewayId: chargeId,
              amount: relatedSubscription.plan.price,
              status: currentStatus,
              createdAt: new Date(created_at),
              ...(currentStatus === 'paid' && { payedAt: new Date() }),
            },
          });
        }
      }

      return { message: 'Atualizações processadas com sucesso.' };
    } catch (error) {
      console.error('Erro ao processar o webhook:', error);
      throw new InternalServerErrorException(
        error.response?.data?.message ||
          error.message ||
          'Erro ao processar o webhook.',
      );
    }
  }
}
