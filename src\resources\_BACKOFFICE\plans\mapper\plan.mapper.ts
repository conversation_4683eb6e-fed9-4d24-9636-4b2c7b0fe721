import { Plans as PlanModel } from '@prisma/client';

import { Plan } from '../entities/plan.entity';
import { UpdatePlanDto } from '../dto/update-plan.dto';

import { FormatMoney } from 'src/@shared/helpers/money/format-money';

export class PlanMapper {
  static fromEntityToModel(entity: Plan): PlanModel {
    const planModel = {
      id: entity.id && entity.id,
      secureId: entity.secureId,

      name: entity.name,
      slug: entity.slug,
      description: entity.description,
      details: entity.details,
      price: FormatMoney.fromEntityToModel(entity.price),
      interval: entity.interval,
      trialDays: entity.trialDays,

      attendantsLimit: entity.attendantsLimit,
      chatbotsLimit: entity.chatbotsLimit,
      iaMessagesLimit: entity.iaMessagesLimit,
      knowledgeBaseLimit: entity.knowledgeBaseLimit,
      whatsappNumberLimit: entity.whatsappNumberLimit,

      efiPlanId: entity.efiPlanId,
      efiIsDeleted: entity.efiIsDeleted,

      isActive: entity.isActive === false ? false : true,

      createdAt: entity?.createdAt && entity.createdAt,
      updatedAt: entity?.updatedAt && entity.updatedAt,
    };

    return planModel as PlanModel;
  }

  static fromModelToOutput(model: PlanModel): Plan {
    return new Plan({
      ...model,
      price: FormatMoney.fromModelToOutput(model.price),
    });
  }

  static fromUpdateDtoToModel(dto: UpdatePlanDto): Partial<PlanModel> {
    const planModel: Partial<PlanModel> = {};
  
    if (dto.name !== undefined) planModel.name = dto.name;
    if (dto.slug !== undefined) planModel.slug = dto.slug;
    if (dto.details !== undefined) planModel.details = dto.details;
    if (dto.description !== undefined) planModel.description = dto.description;
    if (dto.interval !== undefined) planModel.interval = dto.interval;
    if (dto.trialDays !== undefined) planModel.trialDays = dto.trialDays;
    if (dto.price !== undefined) planModel.price = FormatMoney.fromOutputDtoToModel(dto.price);
    if (dto.attendantsLimit !== undefined) planModel.attendantsLimit = dto.attendantsLimit;
    if (dto.chatbotsLimit !== undefined) planModel.chatbotsLimit = dto.chatbotsLimit;
    if (dto.iaMessagesLimit !== undefined) planModel.iaMessagesLimit = dto.iaMessagesLimit;
    if (dto.knowledgeBaseLimit !== undefined) planModel.knowledgeBaseLimit = dto.knowledgeBaseLimit;
    if (dto.whatsappNumberLimit !== undefined) planModel.whatsappNumberLimit = dto.whatsappNumberLimit;
    if (dto.isActive !== undefined) planModel.isActive = dto.isActive;
  
    return planModel;
  }
  

  static fromModelToEntity(model: PlanModel): Plan {
    return new Plan({
      ...model,
      price: FormatMoney.fromModelToOutput(model.price),
    });
  }
}
