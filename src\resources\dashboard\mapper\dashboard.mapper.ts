import { FindAllBackofficeDashboardOutputDto } from '../dto/find-all-dashboard.output.dto';
import { AppDashboardVO } from '../value-object/dashboard.vo';

export class AppDashboardMapper {
  static fromDashboardVOToDashboardOutputDto(
    vo: AppDashboardVO,
  ): FindAllBackofficeDashboardOutputDto {
    return {
      chats: {
        quantity: Number(vo.chatsQuantity),
      },
      leads: {
        quantity: Number(vo.leadsQuantity),
      },
      aiMessages: {
        quantity: Number(vo.aiMessagesQuantity),
      },
      aiSessions: {
        quantity: Number(vo.aiSessionsQuantity),
      },
      attendantSessions: {
        quantity: Number(vo.attendantSessionsQuantity),
      },
      waitingSessions: {
        quantity: Number(vo.waitingSessionsQuantity),
      },
      ongoingConversations: {
        quantity: Number(vo.ongoingConversationsQuantity),
      },
      finalizedConversations: {
        quantity: Number(vo.finalizedConversationsQuantity),
      },
      metrics: {
        averageFirstResponseTime: Number(vo.averageFirstResponseTime), // seconds
      },
    };
  }
}
