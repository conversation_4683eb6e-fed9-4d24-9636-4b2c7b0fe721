import { Exclude } from 'class-transformer';
import { v4 as uuidV4 } from 'uuid';

import { RoleName, RoleSlug } from 'src/@shared/types/role';
import { Entity } from 'src/@shared/contracts/entity/interface';

type RoleConstructorProps = {
  id?: number;
  secureId?: string;

  name: RoleName;
  slug: RoleSlug;
  description?: string;

  createAt?: Date;
  updatedAt?: Date;
};

type RoleJson = {
  secureId: string;

  name: RoleName;
  slug: RoleSlug;
  description?: string;

  createdAt?: Date;
  updatedAt?: Date;
};

export class Role implements Entity {
  @Exclude()
  id?: number;

  secureId?: string;

  name: RoleName;
  slug: RoleSlug;
  description: string | null;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: RoleConstructorProps) {
    this.id = props?.id && props.id;
    this.secureId = props?.secureId ? props?.secureId : uuidV4();

    this.name = props.name;
    this.slug = props.slug;
    this.description = props?.description ? props.description : undefined;

    this.createdAt = props?.createAt && props.createAt;
    this.updatedAt = props?.updatedAt && props.updatedAt;
  }

  toJSON(): RoleJson {
    return {
      secureId: this.secureId,

      name: this.name,
      slug: this.slug,
      description: this.description,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
