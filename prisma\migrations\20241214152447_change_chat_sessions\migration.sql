-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE `chat_sessions` DROP FOREIGN KEY `chat_sessions_chat_id_fkey`;

-- DropIndex
DROP INDEX `chat_sessions_id_secure_id_chat_id_is_active_customer_id_acc_idx` ON `chat_sessions`;

-- AlterTable
ALTER TABLE `chat_sessions` ADD COLUMN `source` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `whatsapp_id` INTEGER NULL,
    MODIFY `chat_id` INTEGER NULL;

-- CreateIndex
CREATE INDEX `chat_sessions_id_secure_id_chat_id_is_active_customer_id_acc_idx` ON `chat_sessions`(`id`, `secure_id`, `chat_id`, `is_active`, `customer_id`, `account_id`, `whatsapp_id`, `source`);

-- AddForeignKey
ALTER TABLE `chat_sessions` ADD CONSTRAINT `chat_sessions_chat_id_fkey` FOREIGN KEY (`chat_id`) REFERENCES `chats`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON><PERSON>Key
ALTER TABLE `chat_sessions` ADD CONSTRAINT `chat_sessions_whatsapp_id_fkey` FOREIGN KEY (`whatsapp_id`) REFERENCES `whatsapp_integration`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
