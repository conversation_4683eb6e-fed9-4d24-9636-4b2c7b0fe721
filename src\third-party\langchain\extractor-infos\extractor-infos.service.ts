import { ChatOpenAI } from '@langchain/openai';
import { Inject } from '@nestjs/common';
import storageEnvs from '../env.config';
import { ConfigType } from '@nestjs/config';
import { z } from 'zod';
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from '@langchain/core/prompts';
import { Example } from './types';
import {
  AIMessage,
  type BaseMessage,
  HumanMessage,
  ToolMessage,
} from '@langchain/core/messages';
import { v4 as uuid } from 'uuid';
import { extractorExamples } from './extractor-examples';
import { ToolCall } from '@langchain/core/dist/messages/tool';

export class ExtractorInfosService {
  private readonly llm: ChatOpenAI;

  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
  ) {
    this.llm = new ChatOpenAI({
      model: 'gpt-4o-mini',
      temperature: 0,
      apiKey: this.storageConfig.openApiKey,
    });
  }

  async extractorInfos(content: string, alternativeApiKey?: string) {
    this.llm.apiKey = alternativeApiKey || this.storageConfig.openApiKey;

    const SYSTEM_PROMPT_TEMPLATE = `You are an expert extraction algorithm.
			Extract the customer's name, email, and phone number from the message.
			Only extract relevant information from the text.
			If you do not know the value of an attribute asked to extract, you may omit the attribute's value.`;

    const prompt = ChatPromptTemplate.fromMessages([
      ['system', SYSTEM_PROMPT_TEMPLATE],
      new MessagesPlaceholder('examples'),
      ['human', '{text}'],
    ]);

    const infoSchema = z.object({
      name: z.string().optional().describe('The name of the person'),
      email: z.string().email().optional().describe('The email of the person'),
      phone: z
        .string()
        .min(10)
        .optional()
        .describe('The phone number of the person'),
      document: z.string().optional().describe('The document of the person'),
    });

    const extractionRunnable = prompt.pipe(
      this.llm.withStructuredOutput(infoSchema, {
        name: 'extractor',
      }),
    );

    const exampleMessages = [];
    for (const example of extractorExamples) {
      exampleMessages.push(...this.toolExampleToMessages(example));
    }

    const result = await extractionRunnable.invoke({
      text: content,
      examples: exampleMessages,
    });

    return result;
  }

  toolExampleToMessages(example: Example): BaseMessage[] {
    const openAIToolCalls: ToolCall[] = example.toolCallOutputs.map(
      (output) => {
        return {
          id: uuid(),
          type: 'tool_call',
          name: 'extract',
          args: {
            output: JSON.stringify(output),
          },
        };
      },
    );
    const messages: BaseMessage[] = [
      new HumanMessage(example.input),
      new AIMessage({
        content: '',
        tool_calls: openAIToolCalls,
      }),
    ];
    const toolMessages = openAIToolCalls.map((toolCall, _i) => {
      return new ToolMessage({
        content: 'You have correctly called this tool.',
        tool_call_id: toolCall.id,
      });
    });
    return messages.concat(toolMessages);
  }
}
