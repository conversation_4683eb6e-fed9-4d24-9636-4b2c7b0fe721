import { ChatBots as ChatBotModel } from '@prisma/client';
import { Chatbot } from '../entities/chatbot.entity';
import {
  LeadCaptureJsonOutput,
  OutputChatbotDto,
} from '../dto/output-chatbot.dto';

export class ChatBotFactory {
  static convertBatchFromModelToChatBot(
    models: ChatBotModel[],
  ): OutputChatbotDto[] {
    return models.map((model) => {
      return {
        secureId: model.secureId,
        name: model.name,
        isAI: model.isAI,
        isLeadCaptureActive: model.isLeadCaptureActive,
        leadTriggerMessageLimit: model.leadTriggerMessageLimit,
        leadCaptureMessage: model.leadCaptureMessage,
        leadCaptureThankYouMessage: model.leadCaptureThankYouMessage,
        emotionalTone: model.emotionalTone,
        mood: model.mood,
        responseSize: model.responseSize,
        responseStyle: model.responseStyle,
        temperature: model.temperature,
        greetingMessage: model.greetingMessage,
        isActive: model.isActive,
        isDeleted: model.isDeleted,
        createdAt: model.createdAt,
        updatedAt: model.updatedAt,
      };
    });
  }

  static createOneBatchChatBotOutputDtoFromBatchModel(
    model: ChatBotModel,
  ): OutputChatbotDto {
    return {
      secureId: model.secureId,
      name: model.name,
      isAI: model.isAI,
      isLeadCaptureActive: model.isLeadCaptureActive,
      leadTriggerMessageLimit: model.leadTriggerMessageLimit,
      leadCaptureMessage: model.leadCaptureMessage,
      leadCaptureThankYouMessage: model.leadCaptureThankYouMessage,
      isActive: model.isActive,
      emotionalTone: model.emotionalTone,
      mood: model.mood,
      responseSize: model.responseSize,
      responseStyle: model.responseStyle,
      temperature: model.temperature,
      leadCaptureJson: model.leadCaptureJson as LeadCaptureJsonOutput,
      greetingMessage: model.greetingMessage,

      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
