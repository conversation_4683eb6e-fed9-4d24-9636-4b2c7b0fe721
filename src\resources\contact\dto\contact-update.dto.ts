import { HttpException, HttpStatus } from '@nestjs/common'; // Added HttpStatus import
import { Transform } from 'class-transformer';
import { IsString, IsEmail, IsOptional, IsBoolean } from 'class-validator';

export class ContactUpdateDto {
  @IsOptional()
  @IsString({ message: 'O nome deve ser uma string.' })
  name?: string;

  @IsOptional()
  @IsEmail({}, { message: 'O email fornecido não é válido.' })
  email?: string;

  @IsOptional()
  @IsString({ message: 'O telefone deve ser uma string.' })
  // @Transform(({ value }) => removeCharactersAndValidatePhone(value))
  phone?: string;

  @IsOptional()
  @IsString({ message: 'O documento deve ser uma string.' })
  // @Transform(({ value }) => removeCharactersAndValidateCpf(value))
  document?: string;

  @IsOptional()
  @IsBoolean({ message: 'O campo isActive deve ser um booleano.' })
  isActive?: boolean;
}

function removeCharactersAndValidatePhone(
  value: string | null | undefined,
): string | undefined {
  if (value === null || value === undefined) {
    return undefined;
  }
  const stringValue = String(value);
  const formattedValue = stringValue.replace(/[^0-9]/g, '');

  if (
    formattedValue.length > 0 &&
    (formattedValue.length < 12 || formattedValue.length > 13)
  ) {
    throw new HttpException(
      {
        message: [
          {
            property: 'phone',
            message:
              'phone deve conter o código do país e o DDD, sendo entre 12 e 13 caracteres.',
          },
        ],
        error: 'Bad Request',
        statusCode: HttpStatus.BAD_REQUEST,
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  return formattedValue.length > 0 ? formattedValue : undefined;
}

function removeCharactersAndValidateCpf(
  value: string | null | undefined,
): string | undefined {
  if (value === null || value === undefined) {
    return undefined; // Return undefined if not provided
  }
  const stringValue = String(value);
  const formattedValue = stringValue.replace(/[^0-9]/g, '');

  if (formattedValue.length > 0 && formattedValue.length !== 11) {
    throw new HttpException(
      {
        message: [
          {
            property: 'document',
            message: 'CPF deve conter exatamente 11 dígitos.',
          },
        ],
        error: 'Bad Request',
        statusCode: HttpStatus.BAD_REQUEST,
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  // Return formatted value or undefined if original was empty after formatting
  return formattedValue.length > 0 ? formattedValue : undefined;
}
