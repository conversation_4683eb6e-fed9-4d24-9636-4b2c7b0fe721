import { Injectable } from '@nestjs/common';

@Injectable()
export class ResetPasswordTemplate {
  build(userName: string, resetLink: string): string {
    const year = new Date().getFullYear();

    return `
      <!DOCTYPE html>
      <html>
      <head>
          <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title></title>

          <!--[if !mso]><!-->
          <style type="text/css">
              @import url('https://fonts.mailersend.com/css?family=Inter:400,600');
          </style>
            <!--<![endif]-->

          <style type="text/css" rel="stylesheet" media="all">
              @media only screen and (max-width: 640px) {

                  .ms-header {
                      display: none !important;
                  }
                  .ms-content {
                      width: 100% !important;
                      border-radius: 0;
                  }
                  .ms-content-body {
                      padding: 30px !important;
                  }
                  .ms-footer {
                      width: 100% !important;
                  }
                  .mobile-wide {
                      width: 100% !important;
                  }
                  .info-lg {
                      padding: 30px;
                  }
              }
          </style>
          <!--[if mso]>
          <style type="text/css">
          body { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td * { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td p { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td a { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td span { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td div { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td ul li { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td ol li { font-family: Arial, Helvetica, sans-serif!important  !important; }
          td blockquote { font-family: Arial, Helvetica, sans-serif!important  !important; }
          th * { font-family: Arial, Helvetica, sans-serif!important  !important; }
          </style>
          <![endif]-->
      </head>
      <body style="font-family:'Inter', Helvetica, Arial, sans-serif; width: 100% !important; height: 100%; margin: 0; padding: 0; -webkit-text-size-adjust: none; background-color: #f4f7fa; color: #4a5566;" >

      <div class="preheader" style="display:none !important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;max-width:0;opacity:0;overflow:hidden;" ></div>

      <table class="ms-body" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:collapse;background-color:#f4f7fa;width:100%;margin-top:0;margin-bottom:0;margin-right:0;margin-left:0;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0;" >
          <tr>
              <td align="center" style="word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;" >

                  <table class="ms-container" width="100%" cellpadding="0" cellspacing="0" style="border-collapse:collapse;width:100%;margin-top:0;margin-bottom:0;margin-right:0;margin-left:0;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0;" >
                      <tr>
                          <td align="center" style="word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;" >

                              <table class="ms-header" width="100%" cellpadding="0" cellspacing="0" style="border-collapse:collapse;" >
                                  <tr>
                                      <td height="40" style="font-size:0px;line-height:0px;word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;" >
                                          &nbsp;
                                      </td>
                                  </tr>
                              </table>

                          </td>
                      </tr>
                      <tr>
                          <td align="center" style="word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;" >

                              <table class="ms-content" width="640" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:collapse;width:640px;margin-top:0;margin-bottom:0;margin-right:auto;margin-left:auto;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0;background-color:#FFFFFF;border-radius:6px;box-shadow:0 3px 6px 0 rgba(0,0,0,.05);" >
                                  <tr>
                                      <td class="ms-content-body" style="word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;padding-top:40px;padding-bottom:40px;padding-right:50px;padding-left:50px;" >

                                          <p class="logo" style="margin-right:0;margin-left:0;line-height:28px;font-weight:600;font-size:21px;color:#111111;text-align:center;margin-top:0;margin-bottom:40px;" ><img src="https://cdn.plyrtech.com.br/uploads/plyrchat-logo.png"/></p>

                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                            Olá ${userName},
                                          </p>
                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                            Recebemos uma solicitação para redefinir sua senha de acesso ao PlyrChat.
                                          </p>
                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                            Para criar uma nova senha, basta clicar no botão abaixo:
                                          </p>
                                          <a
                                            href="${resetLink}"
                                            class="button"
                                            style=
                                                "background-color: #FD2264;
                                                color: #ffffff !important;
                                                text-decoration: none !important;
                                                border-radius: 6px;
                                                padding: 8px 20px;
                                                display: inline-block;
                                                font-weight: bold;
                                                font-size: 14px;"
                                          >
                                                Redefinir senha
                                            </a>
                                            </br>
                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                              Se você não solicitou essa alteração, pode ignorar este e-mail. Sua senha atual permanecerá segura.
                                          </p>

                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                              Este link para redefinição de senha é válido por 1 hora.
                                          </p>

                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                              Qualquer dúvida, nossa equipe está pronta para ajudar!
                                          </p>
                                          <p style="color:#4a5566;margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;font-size:14px;line-height:28px;" >
                                              Atenciosamente,</br>
                                              Equipe PlyrChat</br>
                                              <a style="color:#4a5566; font-size:14px;" href="https://plyrchat.com.br">https://plyrchat.com.br</a>
                                          </p>
                                      </td>
                                  </tr>
                              </table>

                          </td>
                      </tr>
                      <tr>
                          <td align="center" style="word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;" >

                              <table class="ms-footer" width="640" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:collapse;width:640px;margin-top:0;margin-bottom:0;margin-right:auto;margin-left:auto;" >
                                  <tr>
                                      <td class="ms-content-body" align="center" style="word-break:break-word;font-family:'Inter', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;padding-top:40px;padding-bottom:40px;padding-right:50px;padding-left:50px;" >
                                          <p class="small" style="margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;color:#96a2b3;font-size:14px;line-height:21px;" >&copy; ${year} PlyrChat. All rights reserved.</p>
                                          <p class="small" style="margin-top:20px;margin-bottom:20px;margin-right:0;margin-left:0;color:#96a2b3;font-size:14px;line-height:21px;" >
                                              Conectamos inteligência artificial e atendimento humano para impulsionar suas vendas, otimizar o suporte e criar experiências de relacionamento verdadeiramente escaláveis.
                                          </p>
                                      </td>
                                  </tr>
                              </table>

                          </td>
                      </tr>
                  </table>

              </td>
          </tr>
      </table>

      </body>
      </html>
    `;
  }
}
