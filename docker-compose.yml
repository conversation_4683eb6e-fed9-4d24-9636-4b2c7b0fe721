services:
  database:
    container_name: plyrchat-database
    image: mysql:8
    user: mysql
    volumes:
      - .docker/mysql:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: plyrchat
      MYSQL_USER: chat
      MYSQL_PASSWORD: chat
    ports:
      - '3319:3306'
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - '5672:5672'
      - '15672:15672'
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
  shadow-database:
    container_name: plyrchat-shadow-database
    image: mysql:8
    user: mysql
    volumes:
      - .docker/shadow:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: plyrchat
      MYSQL_USER: chat
      MYSQL_PASSWORD: chat
    ports:
      - '3320:3306'
