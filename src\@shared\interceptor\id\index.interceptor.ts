import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class ExcludeIdInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        if (Array.isArray(data)) {
          return data.map((item) => {
            delete item.id; // Exclui o campo `id`
            return item;
          });
        } else {
          // Se for um único objeto, remove o 'id'
          delete data.id;
          return data;
        }
      }),
    );
  }
}

// import {
//   Injectable,
//   NestInterceptor,
//   ExecutionContext,
//   CallHandler,
// } from '@nestjs/common';
// import { Observable } from 'rxjs';
// import { map } from 'rxjs/operators';

// @Injectable()
// export class ExcludeIdInterceptor implements NestInterceptor {
//   intercept(context: ExecutionContext, next: <PERSON><PERSON>and<PERSON>): Observable<any> {
//     return next.handle().pipe(map((data) => this.removeIdRecursively(data)));
//   }

//   private removeIdRecursively(obj: any): any {
//     if (Array.isArray(obj)) {
//       // Itera sobre cada item no array e aplica a recursão
//       return obj.map((item) => this.removeIdRecursively(item));
//     } else if (obj && typeof obj === 'object') {
//       // Cria uma cópia superficial do objeto para evitar mutação
//       const newObj = { ...obj };

//       // Remove apenas a propriedade `id`
//       delete newObj.id;

//       // Aplica recursão em propriedades aninhadas
//       for (const key in newObj) {
//         if (newObj.hasOwnProperty(key) && typeof newObj[key] === 'object') {
//           newObj[key] = this.removeIdRecursively(newObj[key]);
//         }
//       }
//       return newObj;
//     }
//     // Retorna o valor original se não for um objeto ou array
//     return obj;
//   }
// }
