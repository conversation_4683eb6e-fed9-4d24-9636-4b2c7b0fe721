import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { UpdateUserDto } from './dto/update-user.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { GetAllUsersOutputDto } from './dto/get-all-users-output.dto';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { IBackofficeUsersNameAndEmailSearchDecorator } from './decorators/find-all.decorator';
import { BackofficeUsersGetAllUsersFactory } from './factory/get-all-users.factory';
import { BackofficeUserGetUserMapper } from './mappers/user.mapper';
import { HashService } from 'src/resources/auth/jwt/hash/hash.service';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class BackofficeUsersService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly hashService: HashService,
  ) { }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    searchQuery: IBackofficeUsersNameAndEmailSearchDecorator,
    userSecureId: string,
  ): Promise<IWithPagination<GetAllUsersOutputDto>> {
    const roleBackofficeModel = await this.prismaService.roles.findFirst({
      where: {
        slug: 'BACKOFFICE',
      },
    });

    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      name: searchQuery?.name && { contains: searchQuery.name },
      email: searchQuery?.email && { contains: searchQuery.email },
      secureId: { not: userSecureId },
      usersAccounts: {
        some: {
          roleId: roleBackofficeModel.id,
        },
      },
    };

    const paginationHelper = new PaginationHelper({
      modelDelegate: this.prismaService.users,
      paginationQuery,
      whereClause: whereClause as never,
    });

    const usersModel = await this.prismaService.users.findMany({
      where: whereClause,
      take: paginationHelper.take,
      skip: paginationHelper.skip,
      include: {
        usersAccounts: {
          select: {
            isActive: true,
            role: {
              select: {
                slug: true,
              },
            },
          },
        },
      },
    });

    const meta = await paginationHelper.getPaginationMeta();
    const data = !usersModel
      ? ([] as GetAllUsersOutputDto[])
      : BackofficeUsersGetAllUsersFactory.createFromUserModelBatchToOutputDtoBatch(
        usersModel,
      );

    return {
      meta,
      data: data,
    };
  }

  async findOne(secureId: string) {
    const userModel = await this.prismaService.users.findFirst({
      where: {
        secureId: { equals: secureId },
      },

      select: {
        secureId: true,
        name: true,
        email: true,
        cpf: true,
        isActive: true,

        createdAt: true,
        updatedAt: true,

        usersAccounts: {
          select: {
            isActive: true,
            role: {
              select: {
                slug: true,
              }
            },
            accountsPermissions: {
              select: {
                permission: {
                  select: {
                    secureId: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    if (!userModel) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return BackofficeUserGetUserMapper.fromUserWithAccountAndAccountPermissionsModelToOutputDto(
      userModel,
    );
  }

  async update(
    secureId: string,
    updateUserDto: UpdateUserDto,
    accountId: number,
    userIdToRequest: number,
  ) {
    const userModel = await this.prismaService.users.findFirst({
      where: {
        secureId: { equals: secureId },
      },

      select: {
        id: true,
        name: true,
        cpf: true,
        email: true,
        password: true,
        isActive: true,
      },
    });

    if (!userModel) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const backofficeAccountModel =
      await this.prismaService.usersAccounts.findFirst({
        where: {
          userId: { equals: userModel.id },
          accountId: { equals: accountId },
          isDeleted: false,
        },

        select: {
          id: true,
          secureId: true,
          isOwner: true,
          accountsPermissions: {
            select: {
              secureId: true,
            },
          },
        },
      });

    if (!backofficeAccountModel) {
      throw new NotFoundException(
        'Nenhuma conta foi encontrada para esse atendente. Talvez essa conta tenha sido excluída.',
      );
    }

    if (backofficeAccountModel.isOwner && userIdToRequest !== userModel.id) {
      throw new NotFoundException('Você não pode editar o dono da conta');
    }

    const { isActive } = updateUserDto;

    if (isActive !== undefined) {
      await this.prismaService.usersAccounts.update({
        where: {
          id: backofficeAccountModel.id,
        },
        data: {
          isActive,
        },
      });
    }

    const { name, email, cpf, password } = updateUserDto;

    const formattedCpf = cpf ? cpf.replace(/\D/g, '') : undefined;

    if (!!email && email !== userModel.email) {
      const emailAlreadyExists = await this.checkIfRecordExistsByEmail(email);
      if (emailAlreadyExists) {
        throw new ConflictException(
          'Já existe um usuário com esse e-mail cadastrado na sua conta',
        );
      }
    }

    if (!!formattedCpf && formattedCpf !== userModel.cpf) {
      const cpfAlreadyExists =
        await this.checkIfRecordExistsByCPF(formattedCpf);

      if (cpfAlreadyExists) {
        throw new ConflictException(
          'Já existe um usuário com esse CPF cadastrado na sua conta',
        );
      }
    }

    await this.prismaService.users.update({
      where: {
        id: userModel.id,
      },
      data: {
        name,
        email,
        cpf: formattedCpf,
        password: password
          ? await this.hashService.generate(password)
          : undefined,
      },
    });

    if (updateUserDto.hasAllPermissions) {
      const permissionsModel = await this.prismaService.permissions.findMany({
        select: {
          id: true,
        },
        where: {
          slug: {
            in: [
              'backoffice_view',
              'backoffice_create',
              'backoffice_edit',
              'backoffice_delete',
            ],
          },
        },
      });

      if (!permissionsModel) {
        throw new NotFoundException(
          'Permissões não encontradas, contate o suporte!',
        );
      }

      await this.prismaService.accountsPermissions.deleteMany({
        where: {
          userAccountId: { equals: backofficeAccountModel.id },
        },
      });

      const permissions = permissionsModel.map((permission) => {
        return {
          secureId: uuidV4(),
          userAccountId: backofficeAccountModel.id,
          permissionId: permission.id,
        };
      });

      await this.prismaService.accountsPermissions.createMany({
        data: permissions,
      });

      return;
    }

    if (updateUserDto.permissions && updateUserDto.permissions.length > 0) {
      const permissionsModel = await this.prismaService.permissions.findMany({
        select: {
          id: true,
        },
        where: {
          secureId: {
            in: updateUserDto.permissions,
          },
        },
      });

      if (!permissionsModel) {
        throw new NotFoundException(
          'Permissões não encontradas, contate o suporte!',
        );
      }

      await this.prismaService.accountsPermissions.deleteMany({
        where: {
          userAccountId: { equals: backofficeAccountModel.id },
        },
      });

      const permissions = permissionsModel.map((permission) => {
        return {
          secureId: uuidV4(),
          userAccountId: backofficeAccountModel.id,
          permissionId: permission.id,
        };
      });

      await this.prismaService.accountsPermissions.createMany({
        data: permissions,
      });
    }

    return;
  }

  async remove(id: string) {
    return await `This action removes a #${id} user`;
  }

  private async checkIfRecordExistsByEmail(email: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM users WHERE email = ?) AS has;`,
      email,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  private async checkIfRecordExistsByCPF(cpf: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM users WHERE cpf = ?) AS has;`,
      cpf,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }
}
