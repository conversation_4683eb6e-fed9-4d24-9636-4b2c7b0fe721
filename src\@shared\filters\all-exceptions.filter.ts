import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { NotificationService } from 'src/resources/notification/notification.service';
import { ErrorNotificationTemplate } from 'src/resources/notification/templates/error-notification.template';
import { ConfigService } from '@nestjs/config';
import { AxiosError } from 'axios';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);
  private readonly adminEmail: string;

  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    private readonly notificationService: NotificationService,
    private readonly errorTemplate: ErrorNotificationTemplate,
    private readonly configService: ConfigService,
  ) {
    this.adminEmail =
      this.configService.get<string>('ADMIN_EMAIL') ||
      '<EMAIL>';
  }

  async catch(exception: unknown, host: ArgumentsHost): Promise<void> {
    // Get the HTTP adapter from the host
    const { httpAdapter } = this.httpAdapterHost;

    // Determine the context type (HTTP, WebSocket, etc.)
    const contextType = host.getType();
    let request: any = {};
    let response: any = {};

    // Get the appropriate context based on type
    if (contextType === 'http') {
      const ctx = host.switchToHttp();
      request = ctx.getRequest();
      response = ctx.getResponse();
    } else if (contextType === 'ws') {
      // For WebSocket context, we still want to log and notify about errors
      const ctx = host.switchToWs();
      const client = ctx.getClient();
      const data = ctx.getData();
      request = {
        method: 'WS',
        url: 'WebSocket Connection',
        client,
        data,
        type: 'websocket',
      };
    }

    // Determine the HTTP status code
    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    // Create a timestamp
    const timestamp = new Date();

    // Prepare the error object
    const error =
      exception instanceof Error ? exception : new Error(String(exception));

    // Log the error
    this.logger.error(
      `Exception: ${error.message}`,
      error.stack,
      `${request.method} ${request.url}`,
    );

    const shouldSendEmail =
      !(exception instanceof HttpException) ||
      httpStatus >= 500 ||
      exception instanceof AxiosError;

    if (shouldSendEmail) {
      try {
        const emailBody = this.errorTemplate.build(error, request, timestamp);

        await this.notificationService.sendEmail({
          to: this.adminEmail,
          subject: `[ERRO] PlyrChat - ${error.name || 'Erro'}: ${error.message}`,
          body: emailBody,
        });

        this.logger.log(`Error notification email sent to ${this.adminEmail}`);
      } catch (emailError) {
        this.logger.error(
          `Failed to send error notification email: ${emailError.message}`,
          emailError.stack,
        );
      }
    }

    // Prepare response body
    const responseBody =
      exception instanceof HttpException
        ? exception.getResponse()
        : 'Ocorreu um erro interno no servidor. Por favor, tente novamente mais tarde.';

    // Send the response only for HTTP context
    if (contextType === 'http') {
      httpAdapter.reply(response, responseBody, httpStatus);
    } else if (contextType === 'ws') {
      // For WebSocket errors, we've already logged them and sent email notifications
      // The client will handle the error based on the WebSocket implementation
      console.log(`WebSocket error handled: ${error.message}`);
    }
  }
}
