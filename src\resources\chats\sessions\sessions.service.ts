import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { SessionFactory } from './factory/session.factory';
import { UpdateSessionDto } from './dto/update-session.dto';
import { StartSessionDTO } from './dto/start-session.dto';
import { v4 as uuidv4 } from 'uuid';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';

@Injectable()
export class SessionsService {
  constructor(private readonly prismaService: PrismaService) {}

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    isAIResponder: boolean,
    accountId: number,
    userId: number,
    showAll: boolean,
    showOnlyArchived: boolean,
    showOnlyFinalized: boolean,
    sessionTypeFilter?: string,
    sortOrder?: string,
  ) {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    var baseWhereClause: any = {
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      isFinalized: false,
      customerName: {
        contains: paginationQuery?.search || undefined,
      },
      chatMessages: {
        some: {},
      },
      accountId: accountId,
    };

    if (!showAll) {
      baseWhereClause = {
        ...baseWhereClause,
        OR: [
          { attendant: null, isAIResponder: isAIResponder ?? undefined },
          {
            attendant: {
              userId: userId,
            },
            isAIResponder: isAIResponder ?? undefined,
          },
        ],
      };
    } else {
      baseWhereClause = {
        ...baseWhereClause,
        isAIResponder: isAIResponder ?? undefined,
      };
    }

    if (showOnlyFinalized) {
      baseWhereClause = {
        ...baseWhereClause,
        isFinalized: true,
      };
    }

    let finalWhereClause = { ...baseWhereClause };

    if (!showOnlyArchived) {
      const archivedLatestSessions = (await this.prismaService.$queryRaw`
        SELECT DISTINCT s1.customer_id, s1.whatsapp_id
        FROM chat_sessions s1
        WHERE s1.account_id = ${accountId}
          AND s1.is_archived = true
          AND s1.is_active = true
          AND s1.is_deleted = false
          AND s1.last_message_at = (
            SELECT MAX(s2.last_message_at)
            FROM chat_sessions s2
            WHERE s2.customer_id = s1.customer_id
              AND s2.whatsapp_id = s1.whatsapp_id
              AND s2.account_id = ${accountId}
              AND s2.is_active = true
              AND s2.is_deleted = false
          )
      `) as Array<{ customer_id: string; whatsapp_id: number }>;

      // Criar condições para excluir apenas as combinações onde a sessão MAIS RECENTE está arquivada
      const excludeConditions = archivedLatestSessions
        .filter((session) => session.customer_id && session.whatsapp_id)
        .map((session) => ({
          customerId: session.customer_id,
          whatsappId: session.whatsapp_id,
        }));

      finalWhereClause = {
        ...finalWhereClause,
        isArchived: false,
        // Excluir apenas as combinações onde a sessão mais recente está arquivada
        NOT:
          excludeConditions.length > 0
            ? {
                OR: excludeConditions,
              }
            : undefined,
      };
    } else {
      finalWhereClause = {
        ...finalWhereClause,
        isArchived: true,
      };
    }

    const sessions = await this.prismaService.chatSessions.findMany({
      take: paginationQuery.limit,
      skip,
      where: finalWhereClause,
      include: {
        chatMessages: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
          select: {
            sendMessage: true,
            receiveMessage: true,
            messageType: true,
            createdAt: true,
          },
        },
        whatsapp: {
          select: {
            secureId: true,
            phoneNumber: true,
          },
        },
        attendant: true,
        account: true,
      },
      orderBy: [
        {
          lastMessageAt: sortOrder === 'asc' ? 'asc' : 'desc',
        },
      ],
    });

    const archivedCount = await this.prismaService.chatSessions.count({
      where: {
        ...baseWhereClause,
        isArchived: true,
      },
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prismaService.chatSessions,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: finalWhereClause,
      },
    );

    const latestSessionsMap = new Map<string, any>();
    if (sessions) {
      sessions.forEach((session) => {
        if (!session.customerId) {
          return;
        }

        // Verificar se a sessão tem relacionamento com whatsapp
        if (session.whatsapp) {
          // Usar customerId + whatsappId como chave para agrupar sessões com o mesmo customerId e whatsapp.id
          const mapKey = `${session.customerId}-${session.whatsapp.secureId}`;

          const existingSession = latestSessionsMap.get(mapKey);
          const currentSessionDate = session.lastMessageAt
            ? new Date(session.lastMessageAt)
            : new Date(0);
          const existingSessionDate = existingSession?.lastMessageAt
            ? new Date(existingSession.lastMessageAt)
            : new Date(0);

          // Condições para substituir a sessão existente:
          // 1. Não existe sessão prévia
          // 2. A sessão atual tem uma mensagem mais recente e ambas têm nomes genéricos ou não-genéricos
          if (!existingSession || currentSessionDate > existingSessionDate) {
            latestSessionsMap.set(mapKey, session);
          }
        } else {
          // Para sessões sem whatsapp, manter o comportamento original usando customerId + accountId
          const mapKey = `${session.customerId}-${session.accountId}`;

          const existingSession = latestSessionsMap.get(mapKey);
          const currentSessionDate = session.lastMessageAt
            ? new Date(session.lastMessageAt)
            : new Date(0);
          const existingSessionDate = existingSession?.lastMessageAt
            ? new Date(existingSession.lastMessageAt)
            : new Date(0);

          // Verifica se o nome da sessão atual começa com "Usuário"
          const isCurrentNameGeneric =
            session.customerName?.startsWith('Usuário');
          // Verifica se o nome da sessão existente começa com "Usuário"
          const isExistingNameGeneric =
            existingSession?.customerName?.startsWith('Usuário');

          // Condições para substituir a sessão existente:
          // 1. Não existe sessão prévia
          // 2. A sessão atual tem uma mensagem mais recente e ambas têm nomes genéricos ou não-genéricos
          // 3. A sessão existente tem nome genérico e a atual não
          if (
            !existingSession ||
            (currentSessionDate > existingSessionDate &&
              isCurrentNameGeneric === isExistingNameGeneric) ||
            (isExistingNameGeneric && !isCurrentNameGeneric)
          ) {
            latestSessionsMap.set(mapKey, session);
          }
        }
      });
    }
    const uniqueLatestSessions = Array.from(latestSessionsMap.values());

    const formattedSessions =
      SessionFactory.convertBatchFromModelToSession(uniqueLatestSessions);

    return {
      meta: meta,
      data: !formattedSessions ? [] : formattedSessions,
      archivedCount: archivedCount,
    };
  }

  async findOne(secureId: string, accountId: number) {
    const session = await this.prismaService.chatSessions.findUnique({
      where: {
        secureId: secureId,
        accountId: { equals: accountId },
      },
      include: {
        account: {
          select: {
            secureId: true,
          },
        },
        chat: {
          select: {
            secureId: true,
          },
        },
        attendant: {
          select: {
            secureId: true,
            user: {
              select: {
                name: true,
                secureId: true,
              },
            },
          },
        },
        whatsapp: {
          select: {
            phoneNumber: true,
          },
        },
      },
    });

    if (!session) {
      return {};
    }

    await this.prismaService.chatSessions.update({
      where: { secureId: secureId },
      data: {
        isRead: true,
      },
    });

    return SessionFactory.convertFromModelToSession(session);
  }

  async updateSession(
    sessionId: string,
    updateSession: UpdateSessionDto,
  ): Promise<void> {
    const { newAttendantSecureId, ...newObjectUpdateSession } = updateSession;

    const sessionModel = await this.prismaService.chatSessions.findFirst({
      where: { secureId: sessionId },
      select: {
        customerName: true,
        customerEmail: true,
        customerPhone: true,
        customerDocument: true,
        sessionDescription: true,
        accountId: true,
      },
    });

    if (!sessionModel) {
      throw new NotFoundException('Sessão não encontrada');
    }

    let userAccountId: number | undefined;

    if (newAttendantSecureId) {
      const userAccount = await this.prismaService.usersAccounts.findFirst({
        where: {
          accountId: sessionModel.accountId,
          user: {
            secureId: newAttendantSecureId,
          },
        },
      });

      if (!userAccount) {
        throw new NotFoundException('Atendente não encontrado na conta');
      }
      userAccountId = userAccount.id;
    }

    await this.prismaService.chatSessions.update({
      where: { secureId: sessionId },
      data: {
        attendantId: userAccountId,
        ...newObjectUpdateSession,
      },
    });

    if (newObjectUpdateSession.isArchived !== undefined) {
      await this.prismaService.chatSessions.updateMany({
        where: {
          customerId: sessionModel.customerName,
          accountId: sessionModel.accountId,
        },
        data: {
          isArchived: newObjectUpdateSession.isArchived,
        },
      });
    }

    return;
  }

  async startSession(
    startSessiondDTO: StartSessionDTO,
    accountId: number,
    userId: number,
  ): Promise<{ sessionSecureId: string }> {
    const contact = await this.prismaService.contacts.findFirst({
      where: {
        secureId: startSessiondDTO.contactSecureId,
      },
    });

    if (!contact) {
      throw new NotFoundException('Contato não encontrado');
    }

    const attendant = await this.prismaService.usersAccounts.findFirst({
      where: {
        accountId: accountId,
        userId: userId,
      },
    });

    if (!attendant) {
      throw new NotFoundException('Atendente não encontrado na conta');
    }
    const customerId = startSessiondDTO.isBusiness
      ? contact.phone
      : `${contact.phone}@s.whatsapp.net`; //É necessario fazer isso, pois todo local no banco onde salva o customerId do usuario via evolution, ele esta salvando dessa forma, então seguimos assim para manter o padrão e evitar problemas
    const session = await this.prismaService.chatSessions.findFirst({
      where: {
        customerId: customerId,
        attendantId: attendant.id,
        whatsapp: {
          secureId: startSessiondDTO.whatsappIntegrationSecureId,
        },
        accountId: accountId,
        isAIResponder: false,
        isActive: true,
      },
    });

    //Caso seja isBussiness, fazer a validação p ver se a sessão esta dentro da janela de 24hrs, se não estiver, criar outra sessão!

    if (session) {
      return {
        sessionSecureId: session.secureId,
      };
    }

    const newSession = await this.prismaService.chatSessions.create({
      data: {
        secureId: uuidv4(),
        customerId: customerId,
        account: {
          connect: { id: accountId },
        },
        attendant: {
          connect: { id: attendant.id },
        },
        whatsapp: {
          connect: {
            secureId: startSessiondDTO.whatsappIntegrationSecureId,
          },
        },
        customerPhone: contact?.phone,
        customerDocument: contact?.document,
        customerEmail: contact?.email,
        customerName: contact?.name,
        isAIResponder: false,
        isLeadCaptured: true,
        isLeadCaptureMessageSent: true,
        source: startSessiondDTO.isBusiness
          ? ChatSourceEnum.whatsappBusiness
          : ChatSourceEnum.whatsapp,
      },
      select: {
        secureId: true,
      },
    });

    return {
      sessionSecureId: newSession.secureId,
    };
  }

  async verifySessionTypeFilter(sessionTypeFilter: string) {
    if (sessionTypeFilter === 'webchat') {
      return {
        source: ChatSourceEnum.webchat,
      };
    }

    if (
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        sessionTypeFilter,
      )
    ) {
      const wppIntegration =
        await this.prismaService.whatsAppIntegration.findFirst({
          where: {
            secureId: sessionTypeFilter,
          },
        });

      if (!wppIntegration) {
        throw new NotFoundException('Integração não encontrada');
      }

      return {
        whatsappId: wppIntegration.id,
      };
    }
  }
}
