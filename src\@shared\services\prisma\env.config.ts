import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import type { IPrismaEnvs } from './prisma.contract';

export default registerAs('database-environment', () => {
  const values: IPrismaEnvs = {
    databaseUrl: process.env.DATABASE_URL,
  };

  const schema = Joi.object({
    databaseUrl: Joi.string().required().messages({
      'any.required': 'ENV: DATABASE_URL is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
