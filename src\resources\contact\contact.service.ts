import { Injectable, NotFoundException } from '@nestjs/common';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { ContactOutputDto } from './dto/contact-output.dto';
import { GetAllContactFactory } from './factory/get-all-leads.factory';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { ContactCreateDto } from './dto/contact-create.dto';
import { ContactUpdateDto } from './dto/contact-update.dto';
import { GetOneContactFactory } from './factory/get-one-contact.factory';
import { Prisma } from '@prisma/client';
import { CreateContactFactory } from './factory/create-contact.factory';

@Injectable()
export class ContactService {
  constructor(private readonly prismaService: PrismaService) {}

  async create(
    createContactDto: ContactCreateDto,
    accountId: number,
  ): Promise<ContactOutputDto> {
    const contactData = CreateContactFactory.createFromCreateDtoToModel(
      createContactDto,
      accountId,
    );
    const createdContact = await this.prismaService.contacts.create({
      data: contactData,
    });
    return CreateContactFactory.createFromModelToOutputDto(createdContact);
  }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountId: number,
    searchTerm?: string,
  ): Promise<IWithPagination<ContactOutputDto>> {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    const whereClause: Prisma.ContactsWhereInput = {
      accountId: { equals: accountId },
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      ...(searchTerm && {
        OR: [
          { name: { contains: searchTerm } },
          { email: { contains: searchTerm } },
          { phone: { contains: searchTerm } },
          { document: { contains: searchTerm } },
        ],
      }),
    };

    const allContacts = await this.prismaService.contacts.findMany({
      where: whereClause,
      take: paginationQuery.limit,
      skip,
      orderBy: [{ createdAt: 'desc' }],
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prismaService.contacts,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    const data = !allContacts
      ? ([] as ContactOutputDto[])
      : GetAllContactFactory.createFromContactModelBatchToOutputDtoBatch(
          allContacts,
        );

    return {
      meta,
      data,
    };
  }

  async findOne(
    secureId: string,
    accountId: number,
  ): Promise<ContactOutputDto> {
    const contact = await this.prismaService.contacts.findFirst({
      where: { secureId, accountId, isDeleted: false },
    });

    if (!contact) {
      throw new NotFoundException(`Contato com ID ${secureId} não encontrado`);
    }
    return GetOneContactFactory.createFromModelToOutputDto(contact);
  }

  async update(
    secureId: string,
    updateContactDto: ContactUpdateDto,
    accountId: number,
  ) {
    const existingContact = await this.prismaService.contacts.findFirst({
      where: { secureId, accountId, isDeleted: false },
    });

    if (!existingContact) {
      throw new NotFoundException(
        `Contato com ID ${secureId} não encontrado ou não pertence a esta conta`,
      );
    }

    await this.prismaService.contacts.update({
      where: { secureId },
      data: updateContactDto,
    });
    return;
  }

  async remove(secureId: string, accountId: number): Promise<void> {
    const existingContact = await this.prismaService.contacts.findFirst({
      where: { secureId, accountId, isDeleted: false },
    });

    if (!existingContact) {
      throw new NotFoundException(
        `Contact with ID ${secureId} not found or does not belong to this account`,
      );
    }

    await this.prismaService.contacts.update({
      where: { secureId },
      data: { isDeleted: true, isActive: false },
    });
  }
}
