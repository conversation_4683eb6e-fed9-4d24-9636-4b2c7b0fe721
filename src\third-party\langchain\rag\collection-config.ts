export const collectionConfig = {
  timeout: 10000, // Tempo limite em milissegundos

  vectors: {
    size: 1536, // Dimensão dos vetores para 'text-embedding-3-small'
    distance: 'Cosine', // Métrica de distância
  } as any,

  hnsw_config: {
    m: 16, // Número de conexões por nó
    ef_construct: 200, // Tamanho da fila para construção
    full_scan_threshold: 10000, // Limite para realizar full scan
  },

  // quantization_config: {
  //   type: 'int8', // Tipo de quantização
  //   quantile: 0.99, // Percentil para quantização
  // },

  wal_config: {
    wal_enabled: true, // Habilitar Write-Ahead Log
    wal_segment_size: 32, // Tamanho do segmento WAL em MB
  },
};
