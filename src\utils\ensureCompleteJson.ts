function extractKeys(obj: any, prefix = ''): string[] {
  if (typeof obj !== 'object' || obj === null) return [];
  let keys: string[] = [];
  for (const key in obj) {
    const path = prefix ? `${prefix}.${key}` : key;
    keys.push(path);
    keys = keys.concat(extractKeys(obj[key], path));
  }
  return keys;
}

function compareJsonStructure(json1: any, json2: any): boolean {
  const keys1 = extractKeys(json1).sort();
  const keys2 = extractKeys(json2).sort();
  return JSON.stringify(keys1) === JSON.stringify(keys2);
}

function deepMerge(defaultObj: any, candidateObj: any): any {
	// Se não for objeto, retorna o candidato se existir, senão o default.
	if (typeof defaultObj !== 'object' || defaultObj === null) {
		return candidateObj !== undefined ? candidateObj : defaultObj;
	}

	const merged: any = { ...defaultObj };
	for (const key in candidateObj) {
		if (candidateObj.hasOwnProperty(key)) {
			if (
				defaultObj.hasOwnProperty(key) &&
				typeof defaultObj[key] === 'object' &&
				typeof candidateObj[key] === 'object' &&
				candidateObj[key] !== null
			) {
				merged[key] = deepMerge(defaultObj[key], candidateObj[key]);
			} else {
				merged[key] = candidateObj[key];
			}
		}
	}
	return merged;
}

function mergeJson(model: any, candidate: any): any {
	const modelKeys = extractKeys(model);
	const candidateKeys = extractKeys(candidate);

	if (candidateKeys.length < modelKeys.length) {
		// Se faltar chaves, mescla preservando os valores do candidato onde existirem.
		return deepMerge(model, candidate);
	} else {
		// Se tiver a mais ou for diferente, retorna o candidato por completo.
		return candidate;
	}
}

export function ensureCompleteJson<T>(jsonToValidate: any, jsonModel: T): T {
  return compareJsonStructure(jsonToValidate, jsonModel)
    ? jsonToValidate
    : mergeJson(jsonModel, jsonToValidate) as T;
}
