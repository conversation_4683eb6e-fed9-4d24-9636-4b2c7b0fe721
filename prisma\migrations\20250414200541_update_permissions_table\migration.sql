-- AlterTable
ALTER TABLE `permissions` MODIFY `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete', 'Attendant Chat View', 'Attendant Chat Create', 'Attendant Chat Edit', 'Attendant Contact View', 'Attendant Contact Create', 'Attendant Contact Edit', 'Attendant Contact Delete', 'Attendant Dashboard View', 'Attendant Report View', 'Attendant Configuration View', 'Attendant WhatsApp View', 'Attendant WhatsApp Create', 'Attendant WhatsApp Edit', 'Attendant WhatsApp Delete', 'Attendant WebChat View', 'Attendant WebChat Create', 'Attendant WebChat Edit', 'Attendant WebChat Delete', 'Attendant ChatBot View', 'Attendant ChatBot Create', 'Attendant ChatBot Edit', 'Attendant ChatBot Delete', 'Attendant Knowledge Base Create', 'Attendant Knowledge Base Delete', 'Attendant Attendant View', 'Attendant Attendant Create', 'Attendant Attendant Edit', 'Attendant Attendant Delete', 'Backoffice Dashboard View', 'Backoffice Account View', 'Backoffice Account Edit', 'Backoffice Plans View', 'Backoffice Plans Create', 'Backoffice Plans Delete', 'Backoffice Plans Edit', 'Backoffice User View', 'Backoffice User Create', 'Backoffice User Edit', 'Backoffice User Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete', 'attendant_chat_view', 'attendant_chat_create', 'attendant_chat_edit', 'attendant_contact_view', 'attendant_contact_create', 'attendant_contact_edit', 'attendant_contact_delete', 'attendant_dashboard_view', 'attendant_report_view', 'attendant_configuration_view', 'attendant_whatsapp_view', 'attendant_whatsapp_create', 'attendant_whatsapp_edit', 'attendant_whatsapp_delete', 'attendant_webchat_view', 'attendant_webchat_create', 'attendant_webchat_edit', 'attendant_webchat_delete', 'attendant_chatbot_view', 'attendant_chatbot_create', 'attendant_chatbot_edit', 'attendant_chatbot_delete', 'attendant_knowledge_base_create', 'attendant_knowledge_base_delete', 'attendant_attendant_view', 'attendant_attendant_create', 'attendant_attendant_edit', 'attendant_attendant_delete', 'backoffice_dashboard_view', 'backoffice_account_view', 'backoffice_account_edit', 'backoffice_plans_view', 'backoffice_plans_create', 'backoffice_plans_edit', 'backoffice_plans_delete', 'backoffice_user_view', 'backoffice_user_create', 'backoffice_user_edit', 'backoffice_user_delete') NOT NULL;
