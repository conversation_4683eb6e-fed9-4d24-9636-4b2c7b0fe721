import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PaymentRequiredException } from 'src/@shared/exceptions/payment-required.exception';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

// TODO: Implementar evento para desativar a subscription caso ela estiver vencida.

/**
 * @description Esse guard é responsável por verificar se o usuário possui uma inscrição ativa.
 */
@Injectable()
export class SubscriptionGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user; // NOTE: Pequeno Gafanhoto isso aqui é para pegar o token no header.

    // NOTE: Gafanhotenho essa parada aqui é um BYPASS para master ;)
    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    if (!user.activeAccount.accountSecureId) {
      throw new BadRequestException(
        'Nenhuma Conta encontrada para esse usuário. Entre em contato com o suporte',
      );
    }

    const account = await this.prismaService.accounts.findFirst({
      where: {
        secureId: { equals: user.activeAccount.accountSecureId },
      },

      select: {
        companyName: true,
        subscriptions: {
          select: {
            isActive: true,
            endsAt: true,
            canceledAt: true,
          },
        },
      },
    });

    const subscriptions = account.subscriptions;

    if (account.companyName === 'PlyrChat') {
      return true; // Bypass para PlyrChat, não precisa de assinatura
    }

    if (!subscriptions || subscriptions.length < 1) {
      throw new PaymentRequiredException('Inscrição não encontrada');
    }

    const hasActiveSubscription = subscriptions.some(
      (subscription) => subscription.isActive,
    );
    if (!hasActiveSubscription) {
      throw new PaymentRequiredException('Nenhuma inscrição ativa encontrada');
    }

    for (const subscription of subscriptions) {
      const hasEnded = new Date(subscription.endsAt) < new Date();

      if (subscription.canceledAt) {
        if (hasEnded) {
          throw new PaymentRequiredException(
            'Sua inscrição chegou ao fim e foi cancelada. Assine um novo plano para continuar',
          );
        }
      }

      if (hasEnded) {
        throw new PaymentRequiredException(
          'Sua inscrição chegou ao fim. Assine um novo plano para continuar',
        );
      }
    }

    return true;
  }
}
