import { v4 as uuidV4 } from 'uuid';

type CreateEvoInstanceConstructorProps = {
  phoneNumber: string;
  token?: string;
  numberId?: string;
  businessId?: string;
};

export class CreateEvoInstance {
  name: string;
  number?: string;
  token?: string;
  numberId?: string;
  businessId?: string;

  constructor(props: CreateEvoInstanceConstructorProps) {
    this.name = uuidV4();
    this.number = props?.phoneNumber ? props.phoneNumber : undefined;
    this.token = props?.token ? props.token : undefined;
    this.numberId = props?.numberId ? props.numberId : undefined;
    this.businessId = props?.businessId ? props.businessId : undefined;
  }
}
