import { EFIAuthenticationInputDto } from '../../get-token/dto/authentication-input.dto';

export class EFIAuthenticatedToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expireAt: Date;
  tokenType: string;

  constructor(props: EFIAuthenticationInputDto) {
    this.accessToken = props.access_token;
    this.refreshToken = props.refresh_token;
    this.expiresIn = props.expires_in;
    this.expireAt = props.expire_at;
    this.tokenType = props.token_type;
  }
}
