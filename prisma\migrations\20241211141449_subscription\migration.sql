-- CreateTable
CREATE TABLE `subscriptions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `cycle` INTEGER NOT NULL,
    `status` ENUM('new', 'waiting', 'link', 'paid', 'unpaid', 'canceled', 'identified', 'settled') NOT NULL,
    `type` ENUM('paid', 'trial') NOT NULL,
    `plan_id` INTEGER NOT NULL,
    `gateway_subscription_id` INTEGER NOT NULL,
    `starts_at` DATETIME(3) NOT NULL,
    `ends_at` DATETIME(3) NOT NULL,
    `canceled_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `subscriptions_id_key`(`id`),
    UNIQUE INDEX `subscriptions_secure_id_key`(`secure_id`),
    INDEX `subscriptions_id_secure_id_idx`(`id`, `secure_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `subscriptions` ADD CONSTRAINT `subscriptions_plan_id_fkey` FOREIGN KEY (`plan_id`) REFERENCES `plans`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
