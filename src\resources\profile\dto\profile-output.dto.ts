import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { profileSwaggerDto } from '../docs/dto';
import { SubscriptionStatus, SubscriptionType } from 'src/@shared/types/subscriptions';
import { TransactionStatus } from 'src/@shared/types/transaction';

class Account {
  @ApiProperty(profileSwaggerDto.profileOutputDto.account.secureId)
  secureId: string;

  @ApiProperty(profileSwaggerDto.profileOutputDto.account.companyName)
  companyName: string;

  @ApiProperty(profileSwaggerDto.profileOutputDto.account.isActive)
  isActive: boolean;

  @ApiProperty(profileSwaggerDto.profileOutputDto.account.isDeleted)
  isDeleted: boolean;

  @ApiProperty(profileSwaggerDto.profileOutputDto.account.createdAt)
  createdAt: Date;

  @ApiProperty(profileSwaggerDto.profileOutputDto.account.updatedAt)
  updatedAt: Date;
}

class UserAccount {
  @ApiProperty(profileSwaggerDto.profileOutputDto.userAccounts.secureId)
  secureId: string;

  @ApiProperty({
    description: 'Dad<PERSON> da conta',
    type: Account,
  })
  account: Account;

  @ApiProperty(profileSwaggerDto.profileOutputDto.userAccounts.isOwner)
  isOwner: boolean;

  @ApiProperty(profileSwaggerDto.profileOutputDto.userAccounts.isActive)
  isActive: boolean;

  @ApiProperty(profileSwaggerDto.profileOutputDto.userAccounts.isDeleted)
  isDeleted: boolean;

  @ApiProperty(profileSwaggerDto.profileOutputDto.userAccounts.createdAt)
  createdAt: Date;

  @ApiProperty(profileSwaggerDto.profileOutputDto.userAccounts.updatedAt)
  updatedAt: Date;
}

export class ProfileOutputDto {
  @ApiProperty(profileSwaggerDto.profileOutputDto.user.secureId)
  secureId: string;

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.name)
  name: string;

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.email)
  email: string;

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.cpf)
  cpf: string;

  @ApiPropertyOptional(profileSwaggerDto.profileOutputDto.user.cellPhone)
  cellPhone?: string;

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.isActive)
  isActive: boolean;

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.isDeleted)
  isDeleted: boolean;

  @ApiProperty({
    description: 'Contas do usuário',
    type: [UserAccount],
  })
  userAccounts: UserAccount[];

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.createdAt)
  createdAt: Date;

  @ApiProperty(profileSwaggerDto.profileOutputDto.user.updatedAt)
  updatedAt: Date;
}

type User = {
  secureId: string;
  name: string;
  email: string;
  cpf: string;
  cellPhone: string | null;
};

type Plan = {
  secureId: string;
  name: string;
  slug: string;
  knowledgeBaseLimit: number;
  attendantsLimit: number;
  chatbotsLimit: number;
  description: string;
  iaMessagesLimit: number;
  price: string;
  whatsappNumberLimit: number;
};

type Subscription = {
  secureId: string;
  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;
  plan: Plan;
};

type Transaction = {
  payedAt: Date | null;
  secureId: string;
  amount: string;
  status: TransactionStatus;
};

export type TypeProfileOutputDto = {
  user: User;
  account: {
    secureId: string;
  }
  subscription: Subscription;
  transactions: Transaction[];
};
