import { Chats as PrismaAccount } from '@prisma/client';
import { Chats } from '../entities/chat.entity';

export class ChatMapper {
  static toModel(entity: Chats) {
    const chatModel = {
      ...entity,
      isActive: entity.isActive === false ? false : true,
      isDeleted: entity.isDeleted === false ? false : true,
    };

    return chatModel as PrismaAccount;
  }

  static toEntity(model: PrismaAccount): Chats {
    const accountEntity = new Chats(model);

    return accountEntity;
  }
}
