import {
  Inject,
  Injectable,
  ServiceUnavailableException,
} from '@nestjs/common';

import envModule from './env.config';
import { ConfigType } from '@nestjs/config';
import { EFIGetTokenService } from '../get-token/efi-get-token.service';
import axios from 'axios';
import { EFISubscriptionOutputDto } from './dto/efi-subscription-output.dto';
import { CreateEFISubscriptionVO } from './value-object/create-efi-subscription.vo';
import { EFISubscriptionMapper } from './mapper/efi-subscription.mapper';

@Injectable()
export class EfiSubscriptionsService {
  constructor(
    @Inject(envModule.KEY)
    private readonly env: ConfigType<typeof envModule>,

    private readonly efiTokenService: EFIGetTokenService,
  ) { }

  async getSubscriptionById(
    id: number,
    token: string,
  ): Promise<EFISubscriptionOutputDto> {
    const url = this.env.baseUrl + '/v1' + this.env.subscriptionEP + `/${id}`;

    const response = await axios.get<EFISubscriptionOutputDto>(url, {
      headers: {
        Authorization: token,
      },
    });

    return response.data;
  }

  async createSubscription(
    createEFISubscriptionVO: CreateEFISubscriptionVO,
    holderName: string,
  ): Promise<number> {
    const efiBearerToken = await this.efiTokenService.getToken();

    const notificationUrl = this.env.serverBaseUrl + this.env.webhookEP;
    const planId = createEFISubscriptionVO.plan.efiPlanId;

    const url =
      this.env.baseUrl +
      this.env.planEP +
      `/${planId}` +
      this.env.subscriptionEP +
      '/one-step'; // 'https://cobrancas-h.api.efipay.com.br/v1/plan/12987/subscription/one-step';

    const createEFISubscriptionDto =
      EFISubscriptionMapper.fromCreateEfiSubscriptionVOToEfiSubscriptionDto(
        createEFISubscriptionVO,
        holderName,
        notificationUrl,
      );

    try {
      const response = await axios.post<EFISubscriptionOutputDto>(
        url,
        createEFISubscriptionDto,
        {
          headers: {
            Authorization: efiBearerToken,
          },
        },
      );

      return response.data.data.subscription_id;
    } catch (error) {
      console.error(error);
      throw new ServiceUnavailableException(
        'Não foi possível completar a assinatura, entre em contato com suporte',
      );
    }
  }

  async cancelSubscription(
    id: number,
  ): Promise<number> {
    const efiBearerToken = await this.efiTokenService.getToken();
    const url = this.env.baseUrl + '/v1' + this.env.subscriptionEP + `/${id}/cancel`;

    try {
      const response = await axios.put<any>(
        url,
        {},
        {
          headers: {
            Authorization: efiBearerToken,
          },
        },
      )

      return response.data
    } catch (error) {
      console.error(error);
      throw new ServiceUnavailableException(
        'Não foi possível cancelar a assinatura, entre em contato com suporte',
      );
    }
  }
}
