import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { PublicPlansMapper } from './mappers/plans.mappers';
import { Plan } from 'src/resources/_BACKOFFICE/plans/entities/plan.entity';

@Injectable()
export class PublicPlansService {
  constructor(private readonly prismaService: PrismaService) { }

  async findAllActive() {
    const activePlansModel = await this.prismaService.plans.findMany({
      where: { isActive: true, efiIsDeleted: false },
    });

    // Filtro para obter apenas os planos não gratuitos
    const activePlansFiltered = activePlansModel.filter((plan) => {
      return Number(plan.price) > 0;
    });

    const activePlans =
      PublicPlansMapper.fromBatchModelToBatchEntity(activePlansFiltered);

    return activePlans;
  }

  async findBySlug(slug: string): Promise<Plan> {
    const planModel = await this.prismaService.plans.findFirst({
      where: { slug: { equals: slug }, isActive: true, efiIsDeleted: false },
    });

    const plan = PublicPlansMapper.fromModelToEntity(planModel);

    return plan;
  }
}
