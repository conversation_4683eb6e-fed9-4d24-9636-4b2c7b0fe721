import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { ILoginEnvs } from './login.contract';

export default registerAs('sign-in-envs', () => {
  const values: ILoginEnvs = {
    jwtSecret: process.env.JWT_SECRET,
  };

  const schema = Joi.object<ILoginEnvs>({
    jwtSecret: Joi.string().required().messages({
      'any.required': 'ENV: JWT_SECRET is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
