import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

import { Plan } from '../entities/plan.entity';

const secureIdParam = {
  name: 'secureId',
  description: 'Identificador único do plano',
  example: '2996eab6-5898-4036-966a-e70b298f1f27',
};

const notFound: ApiResponseNoStatusOptions = {
  examples: {
    notFound: {
      summary: 'Plano não encontrado',
      value: {
        message: 'Plano não encontrado',
        error: 'Not Found',
        statusCode: 404,
      },
    },
  },
};

const okResponse: ApiResponseNoStatusOptions = {
  example: {
    secureId: '2996eab6-5898-4036-966a-e70b298f1f27',
    attendantsLimit: 10,
    whatsappNumberLimit: 10,
    chatbotsLimit: 10,
    knowledgeBaseLimit: 10,
    iaMessagesLimit: 10,
    name: 'Plano básico',
    slug: 'plano-basico',
    description: 'Plano básico para pequenas empresas',
    price: 'R$ 100,00',
    isActive: true,
    createdAt: '2024-11-21T16:33:50.734Z',
    updatedAt: '2024-11-21T16:33:50.734Z',
  },
  type: Plan,
  description: 'Retorna um plano',
};

export const findOnePlan = {
  params: {
    secureId: secureIdParam,
  },

  status: {
    notFound,
    okResponse,
  },
};
