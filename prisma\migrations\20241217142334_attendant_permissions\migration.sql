-- AlterTable
ALTER TABLE `permissions` MODIFY `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete', 'Account View', 'Account Create', 'Account Edit', 'Account Delete', 'Evo Integration View', 'Evo Integration Create', 'Evo Integration Edit', 'Evo Integration Delete', 'Attendant View', 'Attendant Create', 'Attendant Edit', 'Attendant Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete', 'account_view', 'account_create', 'account_edit', 'account_delete', 'evo_integration_view', 'evo_integration_create', 'evo_integration_edit', 'evo_integration_delete', 'attendant_view', 'attendant_create', 'attendant_edit', 'attendant_delete') NOT NULL,
    MODIFY `group` ENUM('backoffice', 'app', 'account', 'evo_integration', 'attendant') NOT NULL;

-- AlterTable
ALTER TABLE `roles` MODIFY `name` ENUM('Master', 'Backoffice', 'App', 'Attendant') NOT NULL,
    MODIFY `slug` ENUM('MASTER', 'BACKOFFICE', 'APP', 'ATTENDANT') NOT NULL;
