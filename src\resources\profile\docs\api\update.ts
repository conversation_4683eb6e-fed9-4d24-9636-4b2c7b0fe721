import {
  ApiOperationOptions,
  ApiQueryOptions,
  ApiResponseNoStatusOptions,
} from '@nestjs/swagger';

type ProfileUpdate = {
  apiOperation: ApiOperationOptions;
  apiQuerySecureId: ApiQueryOptions;
  apiForbiddenResponse: ApiResponseNoStatusOptions;
};

export const profileUpdate: ProfileUpdate = {
  apiOperation: {
    summary: 'Atualiza perfil do próprio usuário',
    description: 'Atualiza perfil do próprio usuário',
  },

  apiQuerySecureId: {
    name: 'secureId',
    required: true,
    type: String,
    description: 'Identificador único do perfil. Sendo o secureId do usuário.',
    example: 'd2b6b0b2-2b4a-4c5d-8c6e-6b7a8b9c0d1e',
  },

  apiForbiddenResponse: {
    examples: {
      anotherAccount: {
        summary:
          'Usuário não tem permissão para atualizar um perfil que não seja o dele',
        value: {
          message: 'Você não pode acessar o perfil de outro usuário',
          error: 'Forbidden',
          statusCode: 403,
        },
      },

      unauthorized: {
        summary: 'Usuário não tem permissão para acessar o recurso',
        value: {
          message: 'Forbidden resource',
          error: 'Forbidden',
          statusCode: 403,
        },
      },
    },
  },
};
