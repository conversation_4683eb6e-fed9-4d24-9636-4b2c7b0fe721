import { applyDecorators } from '@nestjs/common';
import {
  ApiUnauthorizedResponse,
  ApiOperation,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiForbiddenResponse,
  ApiOkResponse,
  ApiQuery,
  ApiNotFoundResponse,
  ApiParam,
  ApiNoContentResponse,
} from '@nestjs/swagger';

import { swaggerAccounts } from './docs';
import { swaggerShared } from 'src/@shared/docs/swagger';

function Create() {
  return applyDecorators(
    ApiOperation({
      summary: 'Criação de uma nova Account',
      description:
        'Criação de uma nova account. Esta account será utilizada pelo Backoffice para gerenciar as informações da empresa. O usuário deve ter role de **Backoffice** e permission de **account_create**.',
    }),
    ApiCreatedResponse(swaggerAccounts.api.status.create.created),
    ApiConflictResponse(swaggerAccounts.api.status.create.conflict),
    ApiBadRequestResponse(swaggerAccounts.api.status.create.badRequest),
    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

function FindAll() {
  return applyDecorators(
    ApiOperation({
      summary: 'Puxa as accounts trazendo informações de paginação',
      description:
        'Puxa as accounts trazendo informações de paginação. O usuário deve ter role de **Backoffice** e permission de **account_view**.',
    }),
    ApiOkResponse(swaggerAccounts.api.status.findAll.okResponse),
    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
    ApiQuery(swaggerShared.query.isActive),
    ApiQuery(swaggerShared.query.isDeleted),
    ApiQuery(swaggerShared.query.limit),
    ApiQuery(swaggerShared.query.page),
    ApiQuery(swaggerShared.query.search),
  );
}

function FindOne() {
  return applyDecorators(
    ApiOperation({
      summary: 'Puxa os dados da Account pelo secureId',
      description:
        'Puxa os dados da Account pelo secureId. O usuário deve ter role de **Backoffice** e permission de **account_view**.',
    }),
    ApiOkResponse(swaggerAccounts.api.status.findOne.okResponse),
    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
    ApiNotFoundResponse(swaggerAccounts.api.status.findOne.notFound),
    ApiParam(swaggerShared.params.secureId),
  );
}

function Update() {
  return applyDecorators(
    ApiOperation({
      summary: 'Atualiza os dados da Account pelo secureId',
      description:
        'Puxa os dados da Account pelo secureId. O usuário deve ter role de **Backoffice** e permission de **account_edit**.',
    }),
    ApiNoContentResponse(),
    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
    ApiNotFoundResponse(swaggerAccounts.api.status.update.notFound),
    ApiConflictResponse(swaggerAccounts.api.status.update.conflict),
    ApiParam(swaggerShared.params.secureId),
  );
}

function Remove() {
  return applyDecorators(
    ApiOperation({
      summary: 'Apaga uma Account pelo secureId',
      description:
        'Apaga uma Account pelo secureId. O usuário deve ter role de **Backoffice** e permission de **account_delete**.',
    }),
    ApiNoContentResponse(),
    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
    ApiNotFoundResponse(swaggerAccounts.api.status.update.notFound),
    ApiConflictResponse(swaggerAccounts.api.status.update.conflict),
    ApiParam(swaggerShared.params.secureId),
  );
}

export const SwaggerAccount = {
  Create,
  FindAll,
  FindOne,
  Update,
  Remove,
};
