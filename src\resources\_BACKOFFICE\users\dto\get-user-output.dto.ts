import { ApiProperty } from '@nestjs/swagger';
import {
  PermissionGroup,
  PermissionName,
  PermissionSlug,
} from 'src/@shared/types/permissions';
import { backofficeUsersSwaggerDto } from '../docs/dto';
import { IsOptional } from 'class-validator';

// class GetUserAccountPermissions {
//   @ApiProperty(
//     backofficeUsersSwaggerDto.getUser.account.permissions
//       .accountPermissionSecureId,
//   )
//   accountPermissionSecureId: string;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.permissions.name)
//   name: PermissionName;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.permissions.slug)
//   slug: PermissionSlug;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.permissions.group)
//   group: PermissionGroup;

//   @ApiProperty(
//     backofficeUsersSwaggerDto.getUser.account.permissions.description,
//   )
//   description?: string;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.permissions.createdAt)
//   createdAt: Date;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.permissions.updatedAt)
//   updatedAt: Date;
// }

// class GetUserAccount {
//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.secureId)
//   secureId: string;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.companyName)
//   companyName: string;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.createdAt)
//   createdAt: Date;

//   @ApiProperty(backofficeUsersSwaggerDto.getUser.account.updatedAt)
//   updatedAt: Date;

//   @ApiProperty({
//     description: 'Permissões da conta',
//     type: [GetUserAccountPermissions],
//     required: false,
//   })
//   permissions?: GetUserAccountPermissions[];
// }

export class GetUserOutputDto {
  @ApiProperty(backofficeUsersSwaggerDto.getUser.secureId)
  secureId: string;

  @ApiProperty(backofficeUsersSwaggerDto.getUser.name)
  name: string;

  @ApiProperty(backofficeUsersSwaggerDto.getUser.email)
  email: string;

  @ApiProperty(backofficeUsersSwaggerDto.getUser.cpf)
  cpf: string;

  @ApiProperty(backofficeUsersSwaggerDto.getUser.isActive)
  isActive: boolean;

  // @ApiProperty({
  //   description: 'Contas do usuário',
  //   type: [GetUserAccount],
  //   required: false,
  // })
  // @ApiProperty(backofficeUsersSwaggerDto.getUser.account.permissions.accountPermissionSecureId)
  @IsOptional()
  permissions?: string[];

  @IsOptional()
  hasAllPermissions?: boolean;

  @ApiProperty(backofficeUsersSwaggerDto.getUser.createdAt)
  createdAt: Date;

  @ApiProperty(backofficeUsersSwaggerDto.getUser.updatedAt)
  updatedAt: Date;
}
