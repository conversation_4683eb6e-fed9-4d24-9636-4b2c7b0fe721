import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const okResponse: ApiResponseNoStatusOptions = {
  examples: {
    noItems: {
      summary: 'Resposta, válida, quando não existem planos no banco de dados',
      value: {
        meta: {
          totalItems: 0,
          totalPages: 0,
          currentPage: 1,
          itemsPerPage: 15,
        },
        data: [],
      },
    },

    withItems: {
      summary: 'Resposta, válida, com planos no banco de dados',
      value: {
        meta: {
          totalItems: 2,
          totalPages: 1,
          currentPage: 1,
          itemsPerPage: 15,
        },
        data: [
          {
            secureId: '0cfc9d70-9191-4a05-970b-3d8bda66d4ea',
            name: 'Plano básico',
            slug: 'basic-plan',
            description: 'Plano básico',
            price: '45025',
            attendantsLimit: 10,
            whatsappNumberLimit: 10,
            chatbotsLimit: 10,
            knowledgeBaseLimit: 10,
            iaMessagesLimit: 10,
            isActive: true,
            createdAt: '2024-11-28T11:57:12.046Z',
            updatedAt: '2024-11-28T11:57:12.046Z',
          },
          {
            secureId: '2996eab6-5898-4036-966a-e70b298f1f27',
            name: 'Plano básico 2',
            slug: 'basic-plan-2',
            description: 'Plano básico 2',
            price: '10000',
            attendantsLimit: 10,
            whatsappNumberLimit: 10,
            chatbotsLimit: 10,
            knowledgeBaseLimit: 10,
            iaMessagesLimit: 10,
            isActive: true,
            createdAt: '2024-11-28T12:48:44.791Z',
            updatedAt: '2024-11-28T12:48:44.791Z',
          },
        ],
      },
    },
  },
};

const limitParam = {
  name: 'limit',
  required: false,
  description: 'Quantidade de itens por página',
  allowEmptyValue: true,
  example: 15,
};

const pageParam = {
  name: 'page',
  required: false,
  description: 'Página atual',
  allowEmptyValue: true,
  example: 1,
};

const searchParam = {
  name: 'search',
  description: 'Filtrar por nome do plano',
  required: false,
  type: 'string',
};

const isActiveParam = {
  name: 'isActive',
  description: 'Filtrar por planos ativos ou inativos',
  required: false,
  type: 'boolean',
};

export const findAllPlans = {
  status: {
    okResponse,
  },

  params: {
    limit: limitParam,
    page: pageParam,
    isActive: isActiveParam,
    search: searchParam,
  },
};
