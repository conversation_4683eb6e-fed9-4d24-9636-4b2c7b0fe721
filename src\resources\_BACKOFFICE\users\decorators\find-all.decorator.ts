import { createParamDecorator, ExecutionContext } from '@nestjs/common';

interface NameAndEmailSearchProps extends Headers {
  name?: string;
  email?: string;
}

export interface IBackofficeUsersNameAndEmailSearchDecorator {
  name: string | undefined;
  email: string | undefined;
}

export const BackofficeUsersNameAndEmailSearchDecorator = createParamDecorator(
  (
    data: any,
    ctx: ExecutionContext,
  ): IBackofficeUsersNameAndEmailSearchDecorator => {
    const request = ctx.switchToHttp().getRequest();
    const query: NameAndEmailSearchProps = request.query;

    let name: string | undefined;
    let email: string | undefined;

    if (!query?.['name']) {
      name = undefined;
    } else {
      name = query['name'];
    }

    if (!query?.['email']) {
      email = undefined;
    } else {
      email = query['email'];
    }

    return {
      name,
      email,
    };
  },
);
