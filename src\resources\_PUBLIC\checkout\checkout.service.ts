import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateCheckoutDto } from './dto/create-checkout.dto';
import { UpdateCheckoutDto } from './dto/update-checkout.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { v4 as uuidV4 } from 'uuid';
import { EfiSubscriptionsService } from 'src/third-party/efi/subscriptions/efi-subscriptions.service';
import { CreateEFISubscriptionVO } from 'src/third-party/efi/subscriptions/value-object/create-efi-subscription.vo';
import { SubscriptionStatus, SubscriptionType } from '@prisma/client';

@Injectable()
export class CheckoutService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly efiSubscriptionService: EfiSubscriptionsService,
  ) {}
  async create(
    createCheckoutDto: CreateCheckoutDto,
    userId: number,
    accountId: number,
  ) {
    const subscriptionSecureId = uuidV4(); // custom_id p/ EFI
    const holderName = createCheckoutDto.holderName;

    const planModel = await this.prismaService.plans.findFirst({
      where: {
        secureId: createCheckoutDto.planSecureId,
        isActive: true,
        efiIsDeleted: false,
      },
    });
    if (!planModel) {
      throw new NotFoundException('Plano não encontrado');
    }

    const userModel = await this.prismaService.users.findFirst({
      where: { id: userId, isActive: true, isDeleted: false },
    });
    if (!userModel) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const accountModel = await this.prismaService.accounts.findFirst({
      where: { id: accountId, isActive: true, isDeleted: false },
    });
    if (!accountModel) {
      throw new NotFoundException('Conta não encontrada');
    }

    // Check for active subscription
    const activeSubscription = await this.prismaService.subscriptions.findFirst(
      {
        where: { accountId: accountModel.id, isActive: true },
      },
    );

    // If there's an active subscription, cancel it first
    if (activeSubscription) {
      // Check if user is account owner
      const isOwner = await this.prismaService.usersAccounts.findFirst({
        where: {
          accountId: accountId,
          userId: userId,
          isOwner: true,
        },
      });

      if (!isOwner) {
        throw new BadRequestException(
          'Apenas o proprietário da conta pode alterar a assinatura',
        );
      }

      // Cancel the subscription in the payment gateway if it has a gateway ID
      if (activeSubscription.gatewaySubscriptionId) {
        await this.efiSubscriptionService.cancelSubscription(
          activeSubscription.gatewaySubscriptionId,
        );
      }

      // Update the subscription status in the database
      await this.prismaService.subscriptions.update({
        where: {
          secureId: activeSubscription.secureId,
        },
        data: {
          isActive: false,
          status: 'canceled',
          canceledAt: new Date(),
        },
      });
    }

    const efiSubscriptionVO = new CreateEFISubscriptionVO({
      planModel: planModel,
      userModel: userModel,
      paymentToken: createCheckoutDto.paymentToken,
      subscriptionSecureId: subscriptionSecureId,
    });

    const gatewayId = await this.efiSubscriptionService.createSubscription(
      efiSubscriptionVO,
      holderName,
    );

    const modelSubscription = {
      secureId: subscriptionSecureId,
      cycle: 0,
      status: SubscriptionStatus.new,
      type:
        planModel.trialDays > 0
          ? SubscriptionType.trial
          : SubscriptionType.paid,
      plan: { connect: { id: planModel.id } },
      account: { connect: { id: accountModel.id } },
      remainingSessions: planModel.iaMessagesLimit,
      gatewaySubscriptionId: gatewayId,
      startsAt: new Date(),
      endsAt: new Date(new Date().setMonth(new Date().getMonth() + 1)),
      trialEndsAt:
        planModel.trialDays > 0
          ? new Date(
              new Date().setDate(new Date().getDate() + planModel.trialDays),
            )
          : null,
    };

    await this.prismaService.subscriptions.create({
      data: modelSubscription,
    });
  }
}
