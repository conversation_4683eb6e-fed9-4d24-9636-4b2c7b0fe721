import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

export const AccountSecureIdExtractor = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as ReceivedUserTokenPayload; // NOTE: Aqui Gafanhotenho é onde o token é decodificado e o payload é retornado

    return data ? user?.[data] : user?.activeAccount.accountSecureId;
  },
);
