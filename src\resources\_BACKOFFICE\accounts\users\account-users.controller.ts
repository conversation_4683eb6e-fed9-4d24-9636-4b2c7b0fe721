import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AccountUsersService } from './account-users.service';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { AccountUsersDecorator } from './account-users.decorator';

@Controller('accounts-users')
export class AccountUsersController {
  constructor(
    private readonly accountUsersService: AccountUsersService,
  ) { }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE', 'MASTER'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountUsersDecorator()
    accountUsersQuery: any,
  ) {
    return await this.accountUsersService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountUsersQuery
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.accountUsersService.findOne(+id);
  }
}
