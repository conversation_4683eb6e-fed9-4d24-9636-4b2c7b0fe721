import { IsNotEmpty } from "class-validator";
import { IsLegalAge } from "src/@shared/decorators/validation/is-legal-age/is-legal-age.decorator";

export class CreateSubscriptionDto {
  @IsLegalAge({ message: "A idade deve ser maior ou igual a 18 anos" })
  @IsNotEmpty({ message: "Data de Nascimento é obrigatório" })
  birthDate: Date;

  @IsNotEmpty({ message: "PaymentToken é obrigatório" })
  paymentToken: string;

  @IsNotEmpty({ message: "UserSecureId é obrigatório" })
  userSecureId: string;

  @IsNotEmpty({ message: "AccountSecureId é obrigatório" })
  accountSecureId: string;

  planSecureId: string;
}
