import { ApiResponseOptions } from '@nestjs/swagger';

const created: ApiResponseOptions = {};

const badRequest: ApiResponseOptions = {
  examples: {
    accountSecureIdRequired: {
      summary: "O campo 'accountSecureId' não foi fornecido",
      value: {
        message: [
          {
            property: 'accountSecureId',
            message: 'O id da conta é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },
  },
};

const notFound: ApiResponseOptions = {
  examples: {
    accountNotFound: {
      summary: 'Conta não encontrada',
      value: {
        message: 'Conta não encontrada',
        error: 'Not Found',
        statusCode: 404,
      },
    },

    userNotFound: {
      summary: 'Usuário não encontrado',
      value: {
        message: 'Problemas para encontrar o usuário',
        error: 'Not Found',
        statusCode: 404,
      },
    },
  },
};

const forbidden: ApiResponseOptions = {
  examples: {
    userInactive: {
      summary: 'Usuário inativo',
      value: {
        message:
          'Sua conta de usuário está inativada. Entre em contato com o suporte',
        error: 'Forbidden',
        statusCode: 403,
      },
    },

    userDeleted: {
      summary: 'Usuário excluído',
      value: {
        message:
          'Sua conta de usuário está excluída. Entre em contato com o suporte',
        error: 'Forbidden',
        statusCode: 403,
      },
    },

    accountInactive: {
      summary: 'Conta inativa',
      value: {
        message: 'A conta que deseja acessar está inativa',
        error: 'Forbidden',
        statusCode: 403,
      },
    },

    accountDeleted: {
      summary: 'Conta excluída',
      value: {
        message: 'A conta que deseja acessar está excluída',
        error: 'Forbidden',
        statusCode: 403,
      },
    },
  },
};

export const changeAccount = {
  created,
  badRequest,
  notFound,
  forbidden,
};
