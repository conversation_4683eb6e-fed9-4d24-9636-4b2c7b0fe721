import { Injectable } from "@nestjs/common";
import { PrismaClient } from "@prisma/client";
import { Command, CommandRunner } from "nest-commander";
import { SubscribeFreemiumService } from "./subscribe-freemium.service";

@Command({
  name: 'subscribe-freemium',
  description: 'Assina plano freemium para contas sem assinatura ativa',
})
@Injectable()
export class SubscribeFreemiumCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(
    private readonly subscribeFreemiumService: SubscribeFreemiumService,
  ) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    await this.subscribeFreemiumService
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
