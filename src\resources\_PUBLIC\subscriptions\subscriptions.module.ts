import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { SubscriptionsController } from './subscriptions.controller';
import { IsPlanActive } from 'src/@shared/helpers/validation/plans/is-plan-active';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { EfiSubscriptionsModule } from 'src/third-party/efi/subscriptions/efi-subscriptions.module';
import { FreemiumSubscriptionModule } from 'src/@shared/services/freemium-subscription/freemium-subscription.module';

@Module({
  imports: [PrismaModule, EfiSubscriptionsModule, FreemiumSubscriptionModule],
  controllers: [SubscriptionsController],
  providers: [SubscriptionsService, IsPlanActive],
})
export class SubscriptionsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('subscriptions');
  }
}
