import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { BadRequestException, ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { HttpAdapterHost } from '@nestjs/core';
import { AllExceptionsFilter } from './@shared/filters/all-exceptions.filter';
import { NotificationService } from './resources/notification/notification.service';
import { ErrorNotificationTemplate } from './resources/notification/templates/error-notification.template';
import { ConfigService } from '@nestjs/config';

const port = process.env.SERVER_PORT ?? 3333;
const isDevelopMode = process.env.NODE_ENV === 'development';
const rabbitmqUrl = process.env.RABBITMQ_URL;

console.log('\x1b[32m', `Running on port: ${port}\n`);

if (isDevelopMode) {
  console.log('\x1b[32m', 'Running under Development mode\n');
} else {
  console.log('\x1b[31m', 'Running under Production mode\n');
}

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bodyParser: true,
  });

  // Enable CORS
  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: (errors) => {
        console.log(errors);
        const result = errors.map((error) => ({
          property: error.property,
          message: error.constraints
            ? error.constraints[Object.keys(error.constraints)[0]]
            : 'Validation failed',
        }));
        return new BadRequestException(result);
      },
      stopAtFirstError: false,
    }),
  );
  app.useGlobalInterceptors();
  app.useGlobalGuards();
  app.useBodyParser('json', { limit: '10mb' });

  if (!isDevelopMode) {
    const httpAdapterHost = app.get(HttpAdapterHost);
    const notificationService = app.get(NotificationService);
    const errorTemplate = app.get(ErrorNotificationTemplate);
    const configService = app.get(ConfigService);

    // Then register the AllExceptions filter (catches everything else)
    app.useGlobalFilters(
      new AllExceptionsFilter(
        httpAdapterHost,
        notificationService,
        errorTemplate,
        configService,
      ),
    );
  }

  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle('Plyrchat')
    .setDescription('Plyrchat - Sistema avançado de criação de Agentes')
    .setVersion('1.0')
    .build();

  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, documentFactory);
  app.enableCors({
    origin: '*',
  });

  await app.listen(port);
}
bootstrap();
