import { HttpException } from '@nestjs/common';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  isNotEmpty,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateWhatsappIntegrationDTO {
  @Transform(({ value }) => removeCharactersAndValidatePhone(value))
  @IsString({ message: 'instanceName deve ser uma string!' })
  @IsNotEmpty({ message: 'phoneNumber é obrigatório!' })
  phoneNumber: string;

  @IsString({ message: 'number deve ser uma string!' })
  numberId: string;

  @IsString({ message: 'businessId deve ser uma string!' })
  businessId: string;

  @IsString({ message: 'Token deve ser uma string!' })
  token: string;
}

function removeCharactersAndValidatePhone(value: string): string {
  const formattedValue = value.replace(/[^0-9]/g, '');

  if (formattedValue.length < 12 || formattedValue.length > 13) {
    throw new HttpException(
      {
        message: [
          {
            property: 'phoneNumber',
            message:
              'phoneNumber deve conter o código do país e o DDD, sendo entre 12 e 13 caracteres.',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
      400,
    );
  }

  return formattedValue;
}
