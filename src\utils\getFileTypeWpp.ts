/**
 * Recebe um tipo MIME e retorna uma categoria simplificada.
 * @param mimeType Tipo MIME, ex: 'application/pdf', 'image/png', etc.
 * @returns 'document', 'image', 'audio', 'video' ou 'document' (default)
 */
export function getFileTypeWpp(
  mimeType: string,
): 'document' | 'image' | 'audio' | 'video' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('video/')) return 'video';
  return 'document';
}
