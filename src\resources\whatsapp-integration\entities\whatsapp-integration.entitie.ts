import { v4 as uuidV4 } from 'uuid';

import { Account } from 'src/resources/_BACKOFFICE/accounts/entities/account.entity';

import { Entity } from 'src/@shared/contracts/entity/interface';

type WhatsAppIntegrationConstructorProps = {
  id?: number;
  secureId?: string;

  phoneNumber: string;
  instanceId?: string;
  instanceName?: string;
  token?: string;
  numberId?: string;
  webhookToken?: string;
  businessId?: string;

  account?: Account;
  accountId?: number;

  isBusiness: boolean;
  isActive?: boolean;
  isDeleted?: boolean;

  createdAt?: Date;
  updatedAt?: Date;
};

type WhatsAppIntegrationJson = {
  secureId: string;

  phoneNumber: string;
  instanceId?: string;
  instanceName?: string;
  token?: string;
  numberId?: string;
  webhookToken?: string;
  businessId?: string;

  account: Account;
  accountId?: number;

  isBusiness: boolean;
  isActive: boolean;
  isDeleted: boolean;

  createdAt: Date;
  updatedAt: Date;
};

export class WhatsAppIntegration implements Entity {
  id?: number;
  secureId: string;

  phoneNumber: string;
  instanceId?: string;
  instanceName?: string;
  token?: string;
  numberId?: string;
  webhookToken?: string;
  businessId?: string;

  account?: Account;
  accountId?: number;

  isBusiness: boolean;
  isActive?: boolean;
  isDeleted?: boolean;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: WhatsAppIntegrationConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.phoneNumber = props.phoneNumber;
    this.instanceId = props?.instanceId ? props.instanceId : undefined;
    this.instanceName = props?.instanceName ? props.instanceName : undefined;
    this.token = props?.token ? props.token : undefined;
    this.numberId = props?.numberId ? props.numberId : undefined;
    this.businessId = props?.businessId ? props.businessId : undefined;

    this.account = props?.account ? props.account : undefined;
    this.accountId = props?.accountId ? props.accountId : undefined;

    this.isBusiness = props.isBusiness;
    this.isActive = props.isActive === false ? false : true;
    this.isDeleted = props.isDeleted === true ? true : false;
  }

  deactivate(): void {
    this.isActive = false;
  }

  activate(): void {
    this.isActive = true;
  }

  delete(): void {
    this.isDeleted = true;
  }

  restore(): void {
    this.isDeleted = false;
  }

  toJSON(): WhatsAppIntegrationJson {
    return {
      secureId: this.secureId,

      phoneNumber: this.phoneNumber,
      instanceId: this.instanceId,
      instanceName: this.instanceName,
      token: this.token,
      numberId: this.numberId,
      businessId: this.businessId,

      account: this.account,
      accountId: this.accountId,

      isBusiness: this.isBusiness,
      isActive: this.isActive,
      isDeleted: this.isDeleted,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
