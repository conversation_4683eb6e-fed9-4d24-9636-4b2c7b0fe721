import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { CreateChatInputDto } from './dto/create-chat.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { S3Service } from 'src/third-party/s3/s3.service';
import { ChatsService } from './chats.service';
import { Response } from 'express';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UpdateChatInputDto } from './dto/update-chat.dto';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';

@Controller('chats')
export class ChatsController {
  constructor(
    private readonly chatService: ChatsService,
    private readonly s3Service: S3Service,
  ) {}

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['MASTER', 'BACKOFFICE', 'APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_webchat_view'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountIdExtractor() accountId: number,
  ) {
    return this.chatService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['MASTER', 'BACKOFFICE', 'APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_webchat_view'])
  @Get(':secureId')
  async findOne(@Param('secureId') secureId: string) {
    return await this.chatService.findOne(secureId);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['MASTER', 'BACKOFFICE', 'APP', 'ATTENDANT'])
  @Permissions(['app_create', 'attendant_webchat_create'])
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @Body() createChatDto: CreateChatInputDto,
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response,
  ) {
    try {
      const uploadSecureId = await this.s3Service.uploadFile(file);

      if (!uploadSecureId) {
        return res.status(500).json({ message: 'File upload failed' });
      }
      const chatSecureId = await this.chatService.create(
        createChatDto,
        uploadSecureId,
      );

      return res.status(201).json({ chatSecureId });
    } catch (error) {
      return res.status(500).json({
        message: 'Error creating chat',
        error: error.message,
      });
    }
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['MASTER', 'BACKOFFICE', 'APP', 'ATTENDANT'])
  @Permissions(['app_edit', 'attendant_webchat_edit'])
  @Put(':secureId')
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Param('secureId') secureId: string,
    @Body() updateChatInputDto: UpdateChatInputDto,
    @Res() res: Response,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    try {
      let uploadSecureId: string | undefined;
      if (file) {
        uploadSecureId = await this.s3Service.uploadFile(file);

        if (!uploadSecureId) {
          return res.status(500).json({ message: 'File upload failed' });
        }
      }
      this.chatService.update(secureId, updateChatInputDto, uploadSecureId);

      return res.status(200).json({ message: 'Chat updated' });
    } catch (error) {
      return res.status(500).json({
        message: 'Error creating chat',
        error: error.message,
      });
    }
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['MASTER', 'BACKOFFICE', 'APP', 'ATTENDANT'])
  @Permissions(['app_delete', 'attendant_webchat_delete'])
  @Delete(':secureId')
  async delete(@Param('secureId') secureId: string, @Res() res: Response) {
    try {
      this.chatService.delete(secureId);

      return res.status(200).json({ message: 'Chat deleted' });
    } catch (error) {
      return res.status(500).json({
        message: 'Error deleting chat',
        error: error.message,
      });
    }
  }
}
