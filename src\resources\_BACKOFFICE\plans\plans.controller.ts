import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Put,
  Res,
  Delete,
} from '@nestjs/common';
import { Response } from 'express';

import { ApiBearerAuth } from '@nestjs/swagger';

import { PlansService } from './plans.service';

import { Plan } from './entities/plan.entity';

import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';

import { SwaggerPlan } from './docs/plans.swagger.decorator';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';

import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';

@ApiBearerAuth()
@Controller('plans')
export class PlansController {
  constructor(private readonly plansService: PlansService) {}

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_plans_create', 'backoffice_create'])
  @Post()
  @SwaggerPlan.Create()
  async create(@Body() createPlanDto: CreatePlanDto) {
    return await this.plansService.create(createPlanDto);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_plans_view', 'backoffice_view'])
  @Get()
  @SwaggerPlan.FindAll()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
  ): Promise<IWithPagination<Plan>> {
    return await this.plansService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_plans_view', 'backoffice_view'])
  @Get(':slug')
  @SwaggerPlan.FindOne()
  async findOne(@Param('slug') slug: string): Promise<Plan> {
    return await this.plansService.findOne(slug);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_plans_edit', 'backoffice_edit'])
  @Put(':secureId')
  @SwaggerPlan.Update()
  async update(
    @Param('secureId') secureId: string,
    @Body() updatePlanDto: UpdatePlanDto,
    @Res() res: Response,
  ): Promise<void> {
    await this.plansService.update(secureId, updatePlanDto);

    res.status(204).send();
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_plans_delete', 'backoffice_delete'])
  @Delete(':secureId')
  @SwaggerPlan.Remove()
  async remove(@Param('secureId') secureId: string, @Res() res: Response) {
    await this.plansService.remove(secureId);

    res.status(204).send();
  }
}
