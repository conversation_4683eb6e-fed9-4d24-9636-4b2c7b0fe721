import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { EvoInstancesService } from './instances.service';

import { EvoApiModule } from '../api/evo-api.module';

import evoInstanceEnvModule from './env.config';

@Module({
  imports: [ConfigModule.forFeature(evoInstanceEnvModule), EvoApiModule],
  providers: [EvoInstancesService],
  exports: [EvoInstancesService],
})
export class EvoInstancesModule {}
