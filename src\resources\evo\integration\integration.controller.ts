import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Res,
  Put,
} from '@nestjs/common';
import { Response } from 'express';

import { ApiBearerAuth } from '@nestjs/swagger';

import { CreateEvoIntegrationDto } from './dto/create-evo-integration.dto';
import {
  CreateEvoIntegrationBusinessOutputDto,
  CreateEvoIntegrationOutputDto,
} from './dto/create-evo-integration-output.dto';

import { EvoIntegrationService } from './integration.service';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { SubscriptionGuard } from 'src/@shared/guards/subscription/subscription.guard';
import { CanAddNewWhatsAppNumberGuard } from 'src/@shared/guards/plan/whatsapp-numbers.guard';

import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';

@ApiBearerAuth()
@Controller('whatsapp/integration')
export class EvoIntegrationController {
  constructor(private readonly integrationService: EvoIntegrationService) {}

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
    SubscriptionGuard,
    CanAddNewWhatsAppNumberGuard,
  )
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_create', 'app_create'])
  @Post()
  async create(
    @Body() createEvoIntegrationDto: CreateEvoIntegrationDto,
    @AccountIdExtractor() accountSecureId: number,
  ): Promise<
    CreateEvoIntegrationOutputDto | CreateEvoIntegrationBusinessOutputDto
  > {
    return await this.integrationService.create(
      createEvoIntegrationDto,
      accountSecureId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_create', 'app_view'])
  @Get('login/:secureId')
  async login(
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.integrationService.login(secureId, accountId);
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_delete', 'app_delete'])
  @Delete(':secureId')
  async remove(
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
    @Res() response: Response,
  ) {
    await this.integrationService.remove(secureId, accountId);

    response.status(204).send();
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_whatsapp_delete', 'app_delete'])
  @Delete('logout/:secureId')
  async logout(
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
    @Res() response: Response,
  ) {
    await this.integrationService.logout(secureId, accountId);

    response.status(204).send();
  }
}
