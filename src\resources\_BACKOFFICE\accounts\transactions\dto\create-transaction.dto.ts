import {
  SubscriptionStatus,
  SubscriptionType,
} from 'src/@shared/types/subscriptions';
import { TransactionStatus } from 'src/@shared/types/transaction';

export class AccountTransactionOutputDto {
  secureId: string;

  amount: string;
  status: TransactionStatus;

  account: {
    secureId: string;

    companyName: string;

    isActive: boolean;
    isDeleted: boolean;

    createdAt: Date;
    updatedAt: Date;
  };

  subscription: {
    secureId: string;

    cycle: number;
    status: SubscriptionStatus;
    type: SubscriptionType;

    gatewaySubscriptionId: number;

    startsAt: Date;
    endsAt: Date;
    canceledAt: Date | null;

    createdAt: Date;
    updatedAt: Date;
  };

  payedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}
