import {
  CanActivate,
  ConflictException,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o usuário pode alterar a companyName da Account onde ele é owner, verificando se a accountName já não existe.
 */
@Injectable()
export class DoesUserCanChangeCompanyNameGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;
    const body = context.switchToHttp().getRequest().body;
    if (!body.companyName) {
      return true;
    }

    const userWithSameCPF = await this.prismaService.accounts.findFirst({
      where: {
        companyName: { equals: body.companyName },
        usersAccounts: {
          some: {
            user: {
              secureId: { not: user.subject },
            },
          },
        },
      },
    });
    if (userWithSameCPF) {
      throw new ConflictException(
        'Esse nome de conta já está sendo utilizado por outro usuário',
      );
    }

    return true;
  }
}
