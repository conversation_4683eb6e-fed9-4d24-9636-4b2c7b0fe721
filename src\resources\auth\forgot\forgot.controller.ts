import { <PERSON>, <PERSON>, Body } from '@nestjs/common';
import { ForgotService } from './forgot.service';
import { SendForgotPasswordEmailDto } from './dto/send-email-forgot.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';

@Controller('forgot-password')
export class ForgotController {
  constructor(private readonly forgotService: ForgotService) {}

  @Post()
  async sendForgotPasswordEmail(@Body() sendForgotPasswordEmailDto: SendForgotPasswordEmailDto) {
    return this.forgotService.sendForgotPasswordEmail(sendForgotPasswordEmailDto);
  
  }
  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.forgotService.resetPassword(resetPasswordDto);
  }
}
