import { EVOIntegrationValues } from 'src/@shared/types/evo/integration';
import { InstanceCount } from './instance-count.entity';

export class EvoInstance {
  id: string;
  name: string;

  connectionStatus: string;

  ownerJid: string | null;
  profileName: string | null;
  profilePicUrl: string | null;

  integration: EVOIntegrationValues;
  number: string;

  // businessId: string | null;
  // token: string | null;
  // clientName: 'evolution_api';

  // disconnectionReasonCode: string | null;
  // disconnectionObject: string | null;
  // disconnectionAt: Date | null;

  // 'Chatwoot': null;
  // 'Proxy': null;
  // 'Rabbitmq': null;
  // 'Sqs': null;
  // 'Websocket': null;

  // Setting: {
  //   id: string;
  //   rejectCall: boolean;
  //   msgCall: string;
  //   groupsIgnore: boolean;
  //   alwaysOnline: boolean;
  //   readMessages: boolean;
  //   readStatus: boolean;
  //   syncFullHistory: boolean;
  //   instanceId: string;

  //   createdAt: Date;
  //   updatedAt: Date;
  // };

  _count: InstanceCount;

  createdAt: Date;
  updatedAt: Date;
}
