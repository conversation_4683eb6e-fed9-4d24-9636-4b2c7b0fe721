import { KnowledgeBase } from '../entities/knowledge-base.entity';
import { Prisma, KnowledgeBase as PrismaModule } from '@prisma/client';

export class KnowledgeBaseMapper {
  static fromEntityToModel(entity: KnowledgeBase): PrismaModule {
    const knowledgeBaseModel = {
      chatbotId: entity.chatbotId,
      secureId: entity.secureId,
      accountId: entity.accountId,
      collectionName: entity.collectionName,
      chunkSize: entity.chunkSize,
      chunkOverlap: entity.chunkOverlap,
      isActive: entity.isActive,
      isDeleted: entity.isDeleted,
    };

    return knowledgeBaseModel as PrismaModule;
  }

  static fromModelToOutput(model: PrismaModule): KnowledgeBase {
    return new KnowledgeBase({
      secureId: model.secureId,
      collectionName: model.collectionName,
      chunkSize: model.chunkSize,
      chunkOverlap: model.chunkOverlap,
      isActive: model.isActive,
      isDeleted: model.isDeleted,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      content: model.content,
    });
  }
}
