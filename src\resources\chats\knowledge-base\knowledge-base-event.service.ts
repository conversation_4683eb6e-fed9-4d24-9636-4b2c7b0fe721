import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { RAGService } from 'src/third-party/langchain/rag/rag.service';

@Injectable()
export class KnowledgeBaseListener {
  constructor(private readonly ragService: RAGService) {}

  @OnEvent('knowledgeBase.ingest')
  async handleIngestEvent(payload: {
    collectionName: string;
    content: string;
    chunkSize: number;
    chunkOverlap: number;
  }) {
    await this.ragService.ingestContent(
      payload.collectionName,
      payload.content,
      payload.chunkSize,
      payload.chunkOverlap,
    );
  }
}
