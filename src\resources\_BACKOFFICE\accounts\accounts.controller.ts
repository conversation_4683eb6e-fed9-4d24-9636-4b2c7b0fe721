import {
  Controller,
  Get,
  Post,
  Body,
  Delete,
  Param,
  Put,
  UseGuards,
  Res,
} from '@nestjs/common';
import { Response } from 'express';

import { ApiBearerAuth } from '@nestjs/swagger';

import { Account } from './entities/account.entity';

import { UpdateAccountDto } from './dto/update-account.dto';
import { CreateAccountInputDto } from './dto/create-account.dto';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';

import { AccountsService } from './accounts.service';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';

import { SwaggerAccount } from './accounts.decorator';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';

import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';

@ApiBearerAuth()
@Controller('accounts')
export class AccountsController {
  constructor(private readonly accountsService: AccountsService) {}

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_create'])
  @Post()
  @SwaggerAccount.Create()
  async create(@Body() createAccountDto: CreateAccountInputDto) {
    return await this.accountsService.create(createAccountDto);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_view', 'backoffice_account_view'])
  @Get()
  @SwaggerAccount.FindAll()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
  ): Promise<IWithPagination<Account>> {
    return await this.accountsService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_view', 'backoffice_account_view'])
  @Get(':secureId')
  @SwaggerAccount.FindOne()
  async findOne(@Param('secureId') secureId: string): Promise<Account> {
    return await this.accountsService.findOne(secureId);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_edit', 'backoffice_account_edit'])
  @Put(':secureId')
  @SwaggerAccount.Update()
  async update(
    @Param('secureId') secureId: string,
    @Body() updateAccountDto: UpdateAccountDto,
    @Res() response: Response,
  ) {
    await this.accountsService.update(secureId, updateAccountDto);

    response.status(204).send();
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_delete'])
  @Delete(':secureId')
  @SwaggerAccount.Remove()
  async remove(@Param('secureId') secureId: string, @Res() response: Response) {
    await this.accountsService.remove(secureId);

    response.status(204).send();
  }
}
