import {
  Controller,
  Post,
  Body,
  Put,
  Param,
  UseGuards,
  Get,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiOperation,
} from '@nestjs/swagger';

import { RegisterService } from './register.service';

import { CreateAccountInputDto } from './dto/create-account-input.dto';

import { swaggerBadRequest, swaggerConflict } from './register.swagger';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { SubscriptionGuard } from 'src/@shared/guards/subscription/subscription.guard';
import { CanAddNewAttendantGuard } from 'src/@shared/guards/plan/attendant.guard';
import { CreateAttendantInputDto } from './dto/create-attendant-input.dto';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { CreateBackofficeInputDto } from './dto/create-backoffice-input.dto';
import { BackofficeRegisterGuard } from './guards/backoffice.guard';
import { CreateAccountWithPlanInputDto } from './dto/create-account-with-plan-input.dto';
import { CreateAccountUserInputDto } from './dto/create-account-user-input.dto';

@Controller('register')
export class RegisterController {
  constructor(private readonly registerService: RegisterService) { }

  @Post()
  @ApiOperation({
    summary:
      'Cria um user, account e permissions. Role e permissions em cima de PARTNER',
  })
  @ApiCreatedResponse() // TODO: Adicionar SecureId de resposta
  @ApiBadRequestResponse(swaggerBadRequest)
  @ApiConflictResponse(swaggerConflict)
  async create(@Body() createAccountInputDto: CreateAccountInputDto) {
    return await this.registerService.create(createAccountInputDto);
  }

  @Put(':secureId') // TODO: Adicionar swagger
  async update(
    @Param('secureId') secureId: string,
    @Body() updateAccountInputDto: CreateAccountInputDto,
  ) {
    return await this.registerService.update(secureId, updateAccountInputDto);
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
    SubscriptionGuard,
    CanAddNewAttendantGuard,
  )
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_create', 'attendant_attendant_create'])
  @Post('/attendant')
  async createAttendant(
    @Body() createAttendantDto: CreateAttendantInputDto,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.registerService.createAttendant(
      createAttendantDto,
      accountId,
    );
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
  )
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_create', 'backoffice_user_create'])
  @Post('/backoffice-account/user')
  async createAttendantBackoffice(
    @Body('user') createAccountUserInputDto: CreateAccountUserInputDto,
    @Body('accountSecureId') accountSecureId: string,
  ) {
    const accountId = await this.registerService.getAccountIdBySecureId(accountSecureId);
    
    return await this.registerService.createAttendant(
      createAccountUserInputDto,
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, BackofficeRegisterGuard)
  @Post('/backoffice')
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_create', 'backoffice_user_create'])
  async createBackoffice(
    @Body() createBackofficeDto: CreateBackofficeInputDto,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.registerService.createBackoffice(
      createBackofficeDto,
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, BackofficeRegisterGuard)
  @Post('/backoffice-account')
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_create', 'backoffice_user_create'])
  async createAccountWithPlan(
    @Body() createAccountWithPlanInputDto: CreateAccountWithPlanInputDto,
  ) {
    return await this.registerService.createAccountWithPlan(
      createAccountWithPlanInputDto,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Get('/backoffice-dependencies')
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_create', 'backoffice_user_create'])
  async getBackofficeDependencies() {
    return await this.registerService.getBackofficeDependencies();
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Get('/attendant-dependencies')
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_create', 'attendant_attendant_create'])
  async getAttendantDependencies() {
    return await this.registerService.getAttendantDependencies();
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
  )
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_edit', 'backoffice_user_edit'])
  @Put('/backoffice-account/user/:userSecureId')
  async updateAttendantBackoffice(
    @Param('userSecureId') userSecureId: string,
    @Body('user') updateAccountUserInputDto: CreateAccountUserInputDto,
    @Body('accountSecureId') accountSecureId: string,
  ) {
    const accountId = await this.registerService.getAccountIdBySecureId(accountSecureId);
    return await this.registerService.updateAttendant(
      userSecureId,
      updateAccountUserInputDto,
      accountId
    );
  }
}
