import { v4 as uuidV4 } from 'uuid';
import {
  EnumChatBotEmotionalTone,
  EnumChatBotMood,
  EnumChatBotResponseSize,
  EnumChatBotResponseStyle,
} from './enums';
import { LeadCaptureJson } from '../constants/leadCaptureJson';

type ChatbotConstructorProps = {
  id?: number;
  secureId?: string;

  accountId: number;

  name: string;
  isAI: boolean;
  isLeadCaptureActive?: boolean;

  leadTriggerMessageLimit?: number;

  temperature?: number;
  inputToken?: number;
  outputToken?: number;

  leadCaptureMessage?: string;
  AIPrompt?: string;
  leadCaptureThankYouMessage?: string;
  emotionalTone?: EnumChatBotEmotionalTone;
  mood?: EnumChatBotMood;
  responseSize?: EnumChatBotResponseSize;
  responseStyle?: EnumChatBotResponseStyle;
  leadCaptureJson?: LeadCaptureJson;
  greetingMessage?: string;

  isActive?: boolean;
  isDeleted?: boolean;

  createdAt?: Date;
  updatedAt?: Date;
};

type ChatbotJson = {
  secureId: string;

  name: string;
  isAI: boolean;
  isLeadCaptureActive?: boolean;

  inputToken?: number;
  outputToken?: number;
  leadTriggerMessageLimit?: number;
  temperature?: number;

  emotionalTone?: EnumChatBotEmotionalTone;
  mood?: EnumChatBotMood;
  responseSize?: EnumChatBotResponseSize;
  responseStyle?: EnumChatBotResponseStyle;
  leadCaptureJson?: LeadCaptureJson;
  leadCaptureThankYouMessage?: string;
  leadCaptureMessage?: string;
  greetingMessage?: string;
  isActive: boolean;
  isDeleted: boolean;

  createdAt: Date;
  updatedAt: Date;
};

export class Chatbot {
  id?: number;
  secureId?: string;

  accountId?: number;

  name: string;
  isAI: boolean;
  isLeadCaptureActive?: boolean;
  leadTriggerMessageLimit?: number;
  leadCaptureThankYouMessage?: string;
  leadCaptureMessage?: string;
  temperature?: number;
  emotionalTone?: EnumChatBotEmotionalTone;
  mood?: EnumChatBotMood;
  responseSize?: EnumChatBotResponseSize;
  responseStyle?: EnumChatBotResponseStyle;
  leadCaptureJson?: LeadCaptureJson;
  AIPrompt?: string;
  inputToken?: number;
  greetingMessage?: string;
  outputToken?: number;

  isActive: boolean;
  isDeleted: boolean;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: ChatbotConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.accountId = props.accountId;
    this.isAI = props.isAI;
    this.isLeadCaptureActive = props?.isLeadCaptureActive
      ? props.isLeadCaptureActive
      : false;
    this.leadTriggerMessageLimit = props?.leadTriggerMessageLimit
      ? props.leadTriggerMessageLimit
      : undefined;
    this.leadCaptureMessage = props?.leadCaptureMessage
      ? props.leadCaptureMessage
      : undefined;
    this.leadCaptureThankYouMessage = props?.leadCaptureThankYouMessage
      ? props.leadCaptureThankYouMessage
      : undefined;
    this.name = props.name;
    this.temperature = props?.temperature ? props.temperature : undefined;
    this.emotionalTone = props?.emotionalTone ? props.emotionalTone : undefined;
    this.mood = props?.mood ? props.mood : undefined;
    this.responseSize = props?.responseSize ? props.responseSize : undefined;
    this.responseStyle = props?.responseStyle ? props.responseStyle : undefined;
    this.leadCaptureJson = props?.leadCaptureJson
      ? props.leadCaptureJson
      : undefined;
    this.AIPrompt = props?.AIPrompt ? props.AIPrompt : undefined;
    this.greetingMessage = props?.greetingMessage
      ? props.greetingMessage
      : undefined;

    this.inputToken = props?.inputToken ? props.inputToken : 0;
    this.outputToken = props?.outputToken ? props.outputToken : 0;

    this.isActive = props?.isActive ? props.isActive : true;
    this.isDeleted = props?.isDeleted ? props.isDeleted : false;

    this.createdAt = props?.createdAt ? props.createdAt : new Date();
    this.updatedAt = props?.updatedAt ? props.updatedAt : new Date();
  }

  toJson(): ChatbotJson {
    return {
      secureId: this.secureId,

      name: this.name,
      isAI: this.isAI,
      isLeadCaptureActive: this.isLeadCaptureActive,
      leadCaptureMessage: this.leadCaptureMessage,
      leadCaptureThankYouMessage: this.leadCaptureThankYouMessage,
      leadTriggerMessageLimit: this.leadTriggerMessageLimit,
      temperature: this.temperature,
      emotionalTone: this.emotionalTone,
      mood: this.mood,
      responseSize: this.responseSize,
      responseStyle: this.responseStyle,
      leadCaptureJson: this.leadCaptureJson,
      inputToken: this.inputToken,
      greetingMessage: this.greetingMessage,
      outputToken: this.outputToken,

      isActive: this.isActive,
      isDeleted: this.isDeleted,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
