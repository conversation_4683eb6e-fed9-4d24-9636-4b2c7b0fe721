export class OutputChatbotDto {
  secureId: string;
  name: string;
  isAI: boolean;
  isLeadCaptureActive: boolean;

  leadTriggerMessageLimit: number;
  leadCaptureMessage: string;
  leadCaptureThankYouMessage: string;
  leadCaptureJson?: LeadCaptureJsonOutput;
  responseStyle: string | null;
  responseSize: string | null;
  mood: string | null;
  emotionalTone: string | null;
  greetingMessage: string | null;
  temperature: number;

  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type LeadCaptureJsonOutput = {
  collectEmail: boolean;
  collectPhone: boolean;
  collectName: boolean;
  collectCPF: boolean;
};
