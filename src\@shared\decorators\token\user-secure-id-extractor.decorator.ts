import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

export const UserSecureIdExtractorDecorator = createParamDecorator(
  (data: any, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;

    return user.subject;
  },
);
