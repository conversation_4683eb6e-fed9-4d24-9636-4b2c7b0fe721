import {
  BadRequestException,
  createParamDecorator,
  ExecutionContext,
  NotFoundException,
} from '@nestjs/common';
import { TransactionStatus as TransactionStatusEnum } from '@prisma/client';

import { AccountTransactionDecoratorOutput } from './account-transaction.contract';

import { TransactionStatus } from 'src/@shared/types/transaction';

interface AccountTransactionDecoratorProps extends Headers {
  transactionStatus?: TransactionStatus;
  subscriptionSecureId?: string;
  accountSecureId?: string;
}

export const AccountTransactionDecorator = createParamDecorator(
  async (
    data: any,
    ctx: ExecutionContext,
  ): Promise<AccountTransactionDecoratorOutput> => {
    const request = ctx.switchToHttp().getRequest();
    const query: AccountTransactionDecoratorProps = request.query;

    const prisma = request.prismaService;

    let transactionStatus: TransactionStatus | undefined;
    let subscriptionId: number | undefined;
    let accountId: number | undefined;

    if (!query?.['transactionStatus']) {
      transactionStatus = undefined;
    } else {
      if (
        !Object.values(TransactionStatusEnum).includes(
          query?.['transactionStatus'],
        )
      ) {
        throw new BadRequestException('Valor do status incorreto!');
      }
      transactionStatus = query['transactionStatus'] as TransactionStatus;
    }

    if (!query?.['subscriptionSecureId']) {
      subscriptionId = undefined;
    } else {
      const subscription = await prisma.subscriptions.findFirst({
        where: {
          secureId: { equals: query['subscriptionSecureId'] },
        },

        select: {
          id: true,
        },
      });
      if (!subscription) {
        throw new NotFoundException('Inscrição não encontrada!');
      }

      subscriptionId = subscription.id;
    }

    if (!query?.['accountSecureId']) {
      accountId = undefined;
    } else {
      const account = await prisma.accounts.findFirst({
        where: {
          secureId: { equals: query['accountSecureId'] },
        },

        select: {
          id: true,
        },
      });
      if (!account) {
        throw new NotFoundException('Conta não encontrada!');
      }

      accountId = account.id;
    }

    return {
      transactionStatus,
      subscriptionId,
      accountId,
    };
  },
);
