import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';

import { users } from 'src/constants/users';

import { GenerateUserData } from 'src/@shared/contracts/users/interface';
import { HashService } from 'src/resources/auth/jwt/hash/hash.service';

// XXX: ATENÇÃO: Esse comando cria o users_account com base no id do usuário e no id da companyName. Portanto apenas a role pode ser trocada!
// XXX: Ou seja se por acaso for trocado o email do usuário, o mesmo será duplicado no banco de dados. O mesmo acontece com a companyName.

@Injectable()
export class SyncUsersRecordService extends ISyncRecordCommand {
  entity: string = 'Users';

  constructor(private readonly hashService: HashService) {
    super();
  }

  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Sincronizando ${this.entity}:`);

    for (const user of users) {
      this.logRecord(user.name);
      await this.createRecord(user, prisma);
    }

    console.log('\x1b[32m', `\n${this.entity} sincronizado!\n`);
  }

  logRecord(itemName: string) {
    console.log('\x1b[32m', `\t${itemName}`);
  }

  async createRecord(record: GenerateUserData, prisma: PrismaClient) {
    const user = await prisma.users.upsert({
      where: { email: record.email },
      create: {
        secureId: record.secureId,
        name: record.name,
        email: record.email,
        cpf: record.cpf,
        password: await this.hashService.generate(record.password),
      },
      update: {
        // secureId: record.secureId,
        name: record.name,
        email: record.email,
        cpf: record.cpf,
        // password: await this.hashService.generate(record.password), // Remove o password para não alterar a segunda vez
      },
    });

    const account = await prisma.accounts.findFirst({
      where: {
        companyName: record.companyName,
      },
    });

    const role = await prisma.roles.findFirst({
      where: {
        slug: record.roleSlug,
      },
    });

    const userAccount = await prisma.usersAccounts.findFirst({
      where: {
        userId: user.id,
        accountId: account.id,
      },
    });

    if (!userAccount) {
      await prisma.usersAccounts.create({
        data: {
          secureId: record.secureId,
          userId: user.id,
          accountId: account.id,
          roleId: role.id,
          isOwner: record.isOwner,
        },
      });
    } else {
      await prisma.usersAccounts.update({
        where: {
          id: userAccount.id,
        },
        data: {
          secureId: record.secureId,
          userId: user.id,
          accountId: account.id,
          roleId: role.id,
          isOwner: record.isOwner,
        },
      });
    }
  }
}
