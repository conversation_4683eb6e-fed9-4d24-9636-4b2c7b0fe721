import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const secureIdParam = {
  name: 'secureId',
  description: 'Identificador único do plano',
  example: '2996eab6-5898-4036-966a-e70b298f1f27',
};

const notFound: ApiResponseNoStatusOptions = {
  examples: {
    notFound: {
      summary: 'Plano não encontrado',
      value: {
        message: 'Plano não encontrado',
        error: 'Not Found',
        statusCode: 404,
      },
    },
  },
};

const badRequest: ApiResponseNoStatusOptions = {
  examples: {
    badRequest: {
      summary: 'Quando enviado um objeto vazio',
      value: {
        message:
          'Dados inválidos. É necessário ter ao menos 1 item dentro do objeto',
        error: 'Bad Request',
        statusCode: 400,
      },
    },
  },
};

const conflict: ApiResponseNoStatusOptions = {
  examples: {
    duplicatedName: {
      summary: 'Quando já existe um plano com o nome enviado',
      value: {
        message: 'Já existe um plano com esse nome',
        error: 'Conflict',
        statusCode: 409,
      },
    },

    duplicatedSlug: {
      summary: 'Quando já existe um plano com o slug enviado',
      value: {
        message: 'Já existe um plano com esse slug',
        error: 'Conflict',
        statusCode: 409,
      },
    },
  },
};

export const updatePlan = {
  params: {
    secureId: secureIdParam,
  },

  status: {
    notFound,
    badRequest,
    conflict,
  },
};
