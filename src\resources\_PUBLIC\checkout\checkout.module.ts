import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { CheckoutService } from './checkout.service';
import { CheckoutController } from './checkout.controller';
import { IsPlanActive } from 'src/@shared/helpers/validation/plans/is-plan-active';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { EfiSubscriptionsModule } from 'src/third-party/efi/subscriptions/efi-subscriptions.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule, EfiSubscriptionsModule],
  controllers: [CheckoutController],
  providers: [CheckoutService, IsPlanActive],
})
export class CheckoutModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
      consumer.apply(PrismaMiddleware).forRoutes('checkout');
    }
}
