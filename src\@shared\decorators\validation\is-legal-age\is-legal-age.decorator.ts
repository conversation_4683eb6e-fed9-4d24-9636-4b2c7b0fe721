import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function IsLegalAge(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'birthDate',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (typeof value !== 'string') return false;
          const birth = new Date(value);

          const today = new Date();

          let age = today.getFullYear() - birth.getFullYear();
          const monthDifference = today.getMonth() - birth.getMonth();

          if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birth.getDate())) {
            age--;
          }

          return age >= 18;
        },

        defaultMessage(args: ValidationArguments) {
          return `${args.property} deve ser maior ou igual a 18 anos`;
        },
      },
    });
  };
}
