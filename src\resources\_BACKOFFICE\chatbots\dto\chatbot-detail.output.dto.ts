export class ChatbotConfigurationDto {
  emotionalTone: string | null;
  mood: string | null;
  responseSize: string | null;
  responseStyle: string | null;
  temperature: number;
  isLeadCaptureActive: boolean;
  leadTriggerMessageLimit: number | null;
  leadCaptureMessage: string | null;
  leadCaptureThankYouMessage: string | null;
  greetingMessage: string | null;
  AIPrompt: string | null;
  leadCaptureJson: any;
}

export class ChatbotMessageDto {
  secureId: string;
  sendMessage: string | null;
  receiveMessage: string | null;
  messageDirection: string | null;
  sessionSecureId: string;
  customerName: string | null;
  customerId: string;
  inputToken: number | null;
  outputToken: number | null;
  createdAt: Date;
}

import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';

export class ChatbotDetailOutputDto {
  // Basic chatbot information
  secureId: string;
  name: string;
  isAI: boolean;
  inputTokenCount: number;
  outputTokenCount: number;
  accountOwnerName: string;
  totalMessagesSent: number;
  totalSessionsResponded: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Detailed configuration
  configuration: ChatbotConfigurationDto;

  // Message history for analysis
  sentMessages: IWithPagination<ChatbotMessageDto>;
  receivedMessages: IWithPagination<ChatbotMessageDto>;
}
