import { Prisma } from '@prisma/client';
import { Notifications } from '@prisma/client';
import { NotificationOutputDTO } from '../dto/notification-output.dto';

type Notification = Prisma.NotificationsGetPayload<{
  select: {
    secureId: true;
    title: true;
    message: true;
    createdAt: true;
    session: {
      select: {
        secureId: true;
      };
    };
  };
}>;

export class NotificationMapper {
  static toNotificationResponse(
    notification: Notification[],
  ): NotificationOutputDTO[] {
    return notification.map((notification) => ({
      secureId: notification.secureId,
      title: notification.title,
      message: notification.message,
      createdAt: notification.createdAt,
      sessionSecureId: notification.session?.secureId || null,
    }));
  }
}
