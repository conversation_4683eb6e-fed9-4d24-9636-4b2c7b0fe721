import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { MigrateFreemiumSubscriptionsService } from './migrate-freemium-subscriptions.service';

@Command({
  name: 'migrate-freemium-subscriptions',
  description:
    'Migra assinaturas freemium existentes para o novo sistema de trial de 7 dias',
})
@Injectable()
export class MigrateFreemiumSubscriptionsCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(
    private readonly migrateFreemiumSubscriptionsService: MigrateFreemiumSubscriptionsService,
  ) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    console.log('\x1b[34m', 'Iniciando migração de assinaturas freemium...\n');

    try {
      await this.migrateFreemiumSubscriptionsService.execute(this.prisma);
      console.log('\x1b[32m', '\nMigração concluída com sucesso!\n');
    } catch (error) {
      console.error('\x1b[31m', '<PERSON>rro durante a migração:', error);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}
