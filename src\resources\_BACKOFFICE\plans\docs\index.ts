import { createPlan } from './create-plan';
import { deletePlan } from './delete';

import { dtos } from './dto';
import { findAllPlans } from './find-all';
import { findOnePlan } from './find-one';
import { updatePlan } from './update';

export const swaggerPlans = {
  api: {
    status: {
      create: createPlan.status,
      findAll: findAllPlans.status,
      findOne: findOnePlan.status,
      update: updatePlan.status,
      delete: deletePlan.status,
    },

    params: {
      findAll: findAllPlans.params,
      findOne: findOnePlan.params,
      update: updatePlan.params,
    },
  },

  dto: dtos,
};
