import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { KnowledgeBaseMapper } from './mapper/knowledge-base.mapper';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class KnowledgeBaseService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async find(secureId: string) {
    const knowledgeBaseModel = await this.prismaService.knowledgeBase.findFirst(
      {
        where: {
          chatbot: {
            secureId: { equals: secureId },
          },
        },
      },
    );

    if (!knowledgeBaseModel) {
      throw new NotFoundException('Base de conhecimento não encontrada');
    }

    const knowledgeBaseOutput =
      KnowledgeBaseMapper.fromModelToOutput(knowledgeBaseModel);

    return knowledgeBaseOutput;
  }

  async create(knowledgeBaseSecureId: string, content: string) {
    const knowledgeBase = await this.prismaService.knowledgeBase.update({
      data: {
        content,
      },
      where: {
        secureId: knowledgeBaseSecureId,
      },
    });

    if (!knowledgeBase) {
      throw new NotFoundException('Base de conhecimento não encontrada');
    }

    //talvez usar o rabbitmq
    this.eventEmitter.emit('knowledgeBase.ingest', {
      collectionName: knowledgeBase.collectionName,
      content,
      chunkSize: knowledgeBase.chunkSize,
      chunkOverlap: knowledgeBase.chunkOverlap,
    });

    return;
  }
}
