@apiBaseUrl = http://localhost:3333
@chatsEndPoint = /chats
@chatsAPI = {{apiBaseUrl}}{{chatsEndPoint}}
@loginEndPoint = /login
@loginApi = {{apiBaseUrl}}{{loginEndPoint}}

### Login
# @name login
POST {{loginApi}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "3z2io23m"
}


### Get All Chats
# @name getChats
# GET {{chatsAPI}}
GET {{chatsAPI}}?search=&page=1&limit=10
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}


### Get Chat By Id
# @name getChatById
GET {{chatsAPI}}/60920f14-b0a6-4551-be7d-a7114c9333ad
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}


### Create Chat
# @name createChat
POST {{chatsAPI}}
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

{
  "name": "Chat 1",
  "accountSecureId": "37e123a6-b9fb-486d-9f5b-359b207d4f7e",
  "welcomeMessage": "Bem-vindo ao nosso chat!",
  "description": "Chat 1 description",
  "inputChatBgColor": "#FFFFFF",
  "inputChatTextColor": "#000000",
  "customerMessageBubbleColor": "#FF5733",
  "attendantMessageBubbleColor": "#33FF57"
}


### Update Chat
# @name putChat
PUT  {{chatsAPI}}/60920f14-b0a6-4551-be7d-a7114c9333ad
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

{
  "chatbotSecureId": "fb448d6f-c5fc-4391-904d-f97a384083f6"
}
