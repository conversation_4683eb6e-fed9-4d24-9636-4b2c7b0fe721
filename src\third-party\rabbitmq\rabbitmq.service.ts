import { Injectable, OnModuleInit, Logger, Inject } from '@nestjs/common';
import { Channel, Connection, connect, ConsumeMessage } from 'amqplib';
import { OneSignalService } from '../one-signal/one-signal.service';

@Injectable()
export class RabbitMQService implements OnModuleInit {
  private connection: Connection;
  private channel: Channel;
  private readonly logger = new Logger(RabbitMQService.name);
  private consumers: { [queue: string]: (msg: any) => Promise<void> } = {};

  // Defina a URI do RabbitMQ diretamente aqui ou use outro mecanismo de configuração se necessário
  private readonly uri = process.env.RABBITMQ_URI;

  constructor(private readonly oneSignalService: OneSignalService) {}

  async onModuleInit() {
    await this.connect();
    await this.startAllConsumers();
  }

  async connect() {
    this.connection = await connect(this.uri);
    this.channel = await this.connection.createChannel();
    this.logger.log('Connected to RabbitMQ');
  }

  async publish(queue: string, message: any) {
    await this.channel.assertQueue(queue, { durable: true });
    this.channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)), {
      persistent: true,
    });
  }

  async addConsumer(queue: string, handler: (msg: any) => Promise<void>) {
    this.consumers[queue] = handler;
    if (this.channel) {
      await this.consumeQueue(queue, handler);
    }
  }

  private async startAllConsumers() {
    for (const queue in this.consumers) {
      await this.consumeQueue(queue, this.consumers[queue]);
    }
  }

  private async consumeQueue(
    queue: string,
    handler: (msg: any) => Promise<void>,
  ) {
    await this.channel.assertQueue(queue, { durable: true });
    this.channel.consume(
      queue,
      async (msg: ConsumeMessage) => {
        if (msg) {
          try {
            const content = JSON.parse(msg.content.toString());
            await handler(content);
            this.channel.ack(msg);
          } catch (err) {
            this.logger.error(`Error processing message from ${queue}:`, err);
            await this.oneSignalService.sendEmail({
              to: '<EMAIL>',
              subject: `[ERRO] RabbitMQ - Erro na fila ${queue}`,
              body: `<pre>${JSON.stringify(
                {
                  queue,
                  error: err?.message,
                  stack: err?.stack,
                  msg: msg.content.toString(),
                  timestamp: new Date().toISOString(),
                },
                null,
                2,
              )}</pre>`,
            });
            this.channel.nack(msg, false, false);
          }
        }
      },
      { noAck: false },
    );
    this.logger.log(`Consuming queue: ${queue}`);
  }
}
