import {
  ConflictException,
  Inject,
  Injectable,
  ServiceUnavailableException,
} from '@nestjs/common';
import axios from 'axios';
import { ConfigType } from '@nestjs/config';

import { EFIGetTokenService } from '../get-token/efi-get-token.service';

import { CreateEFIPlanDto } from './dto/create-efi-plan.dto';
import { DeleteEFIPlanDto } from './dto/delete-efi-plan.dto';
import { EFIPlanOutputDto, EFIPlansOutputDto } from './dto/efi-plan-output.dto';
import { EFIPlanChangeNameInputDto } from './dto/efi-plan-change-name-input.dto';

import efiPlansEnvs from './env.config';

@Injectable()
export class EFIPlansService {
  constructor(
    private readonly efiTokenService: EFIGetTokenService,

    @Inject(efiPlansEnvs.KEY)
    private readonly efiPlansModule: ConfigType<typeof efiPlansEnvs>,
  ) { }

  async getAllPlans(): Promise<EFIPlanOutputDto> {
    const efiBearerToken = await this.efiTokenService.getToken();

    const url = this.efiPlansModule.baseUrl + this.efiPlansModule.plansEP;

    try {
      const response = await axios.get<EFIPlanOutputDto>(url, {
        headers: {
          Authorization: efiBearerToken,
        },
      });

      return response.data;
    } catch (error) {
      console.log(error);
      throw new ServiceUnavailableException(
        'Erro ao tentar criar um plano no EFI',
      );
    }
  }

  async getPlanByName(name: string, token: string): Promise<EFIPlansOutputDto> {
    const url = this.efiPlansModule.baseUrl + this.efiPlansModule.plansEP;

    const response = await axios.get<EFIPlansOutputDto>(`${url}?name=${name}`, {
      headers: {
        Authorization: token,
      },
    });

    return response.data;
  }

  async createPlan(createEFIPlanDto: CreateEFIPlanDto): Promise<number> {
    const efiBearerToken = await this.efiTokenService.getToken();

    const planAlreadyExists = await this.getPlanByName(
      createEFIPlanDto.name,
      efiBearerToken,
    );
    if (planAlreadyExists.data.length > 0 && planAlreadyExists.data[0].name === createEFIPlanDto.name) {
      throw new ConflictException(
        'Já existe um plano com esse nome nos servidores da EFI',
      );
    }

    const url = this.efiPlansModule.baseUrl + this.efiPlansModule.planEP;

    try {
      const createPlanResponse = await axios.post<EFIPlanOutputDto>(
        url,
        createEFIPlanDto,
        {
          headers: {
            Authorization: efiBearerToken,
          },
        },
      );

      return createPlanResponse.data.data.plan_id;
    } catch (error) {
      if (error?.response?.status)
        throw new ServiceUnavailableException(
          'Erro ao tentar criar um plano no EFI',
        );
    }
  }

  async deletePlan(deleteEFIPlanDto: DeleteEFIPlanDto): Promise<void> {
    const efiBearerToken = await this.efiTokenService.getToken();

    const url = this.efiPlansModule.baseUrl + this.efiPlansModule.planEP;

    try {
      await axios.delete(`${url}/${deleteEFIPlanDto.planId}`, {
        headers: {
          Authorization: efiBearerToken,
        },
      });
    } catch (error) {
      console.log(error);
      throw new ServiceUnavailableException(
        'Erro ao tentar criar um plano no EFI',
      );
    }
    try {
    } catch (error) {
      console.log(error);
      throw new ServiceUnavailableException(
        'Erro ao tentar criar um plano no EFI',
      );
    }
  }

  async changePlanName(
    efiChangeNameDto: EFIPlanChangeNameInputDto,
  ): Promise<void> {
    const efiBearerToken = await this.efiTokenService.getToken();

    const url = this.efiPlansModule.baseUrl + this.efiPlansModule.planEP;

    try {
      await axios.put(
        `${url}/${efiChangeNameDto.planId}`,
        {
          name: efiChangeNameDto.name,
        },
        {
          headers: {
            Authorization: efiBearerToken,
          },
        },
      );
    } catch (error) {
      console.log(error);
      throw new ServiceUnavailableException(
        'Erro ao tentar criar um plano no EFI',
      );
    }
  }
}
