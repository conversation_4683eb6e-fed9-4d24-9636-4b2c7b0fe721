import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { FREEMIUM_TRIAL_DAYS, FREEMIUM_AI_SESSIONS_LIMIT } from 'src/@shared/types/subscriptions';

@Injectable()
export class MigrateFreemiumSubscriptionsService {
  async execute(prisma: PrismaClient) {
    console.log('\x1b[33m', 'Buscando assinaturas freemium existentes...');

    // Find all existing freemium subscriptions
    const existingFreemiumSubscriptions = await prisma.subscriptions.findMany({
      where: {
        isActive: true,
        plan: {
          slug: 'freemium',
        },
        type: 'free', // Old freemium type
      },
      include: {
        account: {
          select: {
            id: true,
            companyName: true,
          },
        },
      },
    });

    console.log(
      '\x1b[33m',
      `Encontradas ${existingFreemiumSubscriptions.length} assinaturas freemium para migrar...`,
    );

    if (existingFreemiumSubscriptions.length === 0) {
      console.log('\x1b[32m', 'Nenhuma assinatura freemium precisa ser migrada.');
      return;
    }

    let migratedCount = 0;
    let expiredCount = 0;

    for (const subscription of existingFreemiumSubscriptions) {
      try {
        const accountCreatedAt = subscription.createdAt;
        const now = new Date();
        const daysSinceCreation = Math.floor(
          (now.getTime() - accountCreatedAt.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSinceCreation >= FREEMIUM_TRIAL_DAYS) {
          // Account is older than 7 days, convert to expired freemium
          await prisma.subscriptions.update({
            where: { id: subscription.id },
            data: {
              type: 'freemium_expired',
              remainingSessions: 0, // No AI sessions
              trialEndsAt: new Date(accountCreatedAt.getTime() + FREEMIUM_TRIAL_DAYS * 24 * 60 * 60 * 1000),
            },
          });
          expiredCount++;
          console.log(
            '\x1b[33m',
            `Conta ${subscription.account.companyName} migrada para freemium expirado (${daysSinceCreation} dias)`,
          );
        } else {
          // Account is within 7 days, convert to active trial
          const trialEndsAt = new Date(accountCreatedAt.getTime() + FREEMIUM_TRIAL_DAYS * 24 * 60 * 60 * 1000);
          
          await prisma.subscriptions.update({
            where: { id: subscription.id },
            data: {
              type: 'freemium_trial',
              remainingSessions: FREEMIUM_AI_SESSIONS_LIMIT,
              trialEndsAt,
            },
          });
          migratedCount++;
          console.log(
            '\x1b[32m',
            `Conta ${subscription.account.companyName} migrada para trial ativo (${daysSinceCreation} dias, expira em ${trialEndsAt.toLocaleDateString()})`,
          );
        }
      } catch (error) {
        console.error(
          '\x1b[31m',
          `Erro ao migrar assinatura ${subscription.id}:`,
          error.message,
        );
      }
    }

    console.log('\x1b[32m', `\nResumo da migração:`);
    console.log('\x1b[32m', `- Assinaturas migradas para trial ativo: ${migratedCount}`);
    console.log('\x1b[33m', `- Assinaturas migradas para expirado: ${expiredCount}`);
    console.log('\x1b[32m', `- Total processado: ${migratedCount + expiredCount}`);
  }
}
