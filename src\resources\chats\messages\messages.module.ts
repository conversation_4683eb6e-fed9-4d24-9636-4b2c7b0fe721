import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { MessagesController } from './messages.controller';
import { MessagesServices } from './messages.service';

@Module({
  imports: [PrismaModule],
  controllers: [MessagesController],
  providers: [MessagesServices],
})
export class MessagesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('sessions/messages');
  }
}
