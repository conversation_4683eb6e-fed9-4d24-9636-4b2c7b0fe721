import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { swaggerPlans } from '../docs';

export class CreatePlanDto {
  @ApiProperty(swaggerPlans.dto.createPlans.name)
  @MaxLength(255, {
    message: 'O campo name deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O campo name deve ter no mínimo 3 caracteres',
  })
  @IsString({ message: 'O campo name deve ser do tipo string' })
  @IsNotEmpty({ message: 'O campo name é obrigatório' })
  name: string;

  @ApiProperty(swaggerPlans.dto.createPlans.slug)
  @MaxLength(255, {
    message: 'O campo slug deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O campo slug deve ter no mínimo 3 caracteres',
  })
  @IsString({ message: 'O campo name deve ser do tipo string' })
  @IsNotEmpty({ message: 'O campo name é obrigatório' })
  slug: string;

  @ApiProperty(swaggerPlans.dto.createPlans.description)
  @MaxLength(255, {
    message: 'O campo description deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O campo description deve ter no mínimo 3 caracteres',
  })
  @IsString({ message: 'O campo description deve ser do tipo string' })
  @IsNotEmpty({ message: 'O campo name é obrigatório' })
  @IsOptional()
  description: string;

  @ApiProperty(swaggerPlans.dto.createPlans.details)
  @MaxLength(65535, {
    message: 'O campo details deve ter no máximo 65535 caracteres',
  })
  @MinLength(3, {
    message: 'O campo details deve ter no mínimo 3 caracteres',
  })
  @IsString({ message: 'O campo details deve ser do tipo string' })
  @IsNotEmpty({ message: 'O campo name é obrigatório' })
  @IsOptional()
  details: string;

  @ApiProperty(swaggerPlans.dto.createPlans.price)
  @IsString({ message: 'O campo price deve ser do tipo string' })
  @IsOptional()
  price: string;

  @ApiProperty(swaggerPlans.dto.createPlans.attendantsLimit)
  @Max(99999, { message: 'O campo attendantsLimit deve ser menor que 100000' })
  @Min(0, { message: 'O campo attendantsLimit deve ser maior ou igual a 0' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo attendantsLimit deve ser do tipo number' },
  )
  @IsNotEmpty({ message: 'O campo attendantsLimit é obrigatório' })
  attendantsLimit: number;

  @ApiProperty(swaggerPlans.dto.createPlans.whatsappNumberLimit)
  @Max(99999, { message: 'O campo attendantsLimit deve ser menor que 100000' })
  @Min(0, { message: 'O campo attendantsLimit deve ser maior ou igual a 0' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo attendantsLimit deve ser do tipo number' },
  )
  @IsNotEmpty({ message: 'O campo whatsappNumberLimit é obrigatório' })
  whatsappNumberLimit: number;

  @ApiProperty(swaggerPlans.dto.createPlans.chatbotsLimit)
  @Max(99999, { message: 'O campo chatbotsLimit deve ser menor que 100000' })
  @Min(0, { message: 'O campo chatbotsLimit deve ser maior ou igual a 0' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo chatbotsLimit deve ser do tipo number' },
  )
  @IsNotEmpty({ message: 'O campo chatbotsLimit é obrigatório' })
  chatbotsLimit: number;

  @ApiProperty(swaggerPlans.dto.createPlans.knowledgeBaseLimit)
  @Max(99999, { message: 'O campo attendantsLimit deve ser menor que 100000' })
  @Min(0, { message: 'O campo attendantsLimit deve ser maior ou igual a 0' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo attendantsLimit deve ser do tipo number' },
  )
  @IsNotEmpty({ message: 'O campo attendantsLimit é obrigatório' })
  knowledgeBaseLimit: number;

  @ApiProperty(swaggerPlans.dto.createPlans.iaMessagesLimit)
  @Max(99999, { message: 'O campo attendantsLimit deve ser menor que 100000' })
  @Min(0, { message: 'O campo attendantsLimit deve ser maior ou igual a 0' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo attendantsLimit deve ser do tipo number' },
  )
  @IsNotEmpty({ message: 'O campo attendantsLimit é obrigatório' })
  iaMessagesLimit: number;

  @ApiProperty(swaggerPlans.dto.createPlans.interval)
  @Max(12, { message: 'O campo interval deve ser menor que 13' })
  @Min(1, { message: 'O campo interval deve ser maior ou igual que 0' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo interval deve ser do tipo number' },
  )
  @IsNotEmpty({
    message:
      'O campo interval não é obrigatório, mas se for enviado deve conter um valor',
  })
  @IsOptional()
  interval: number;

  @ApiProperty(swaggerPlans.dto.createPlans.trialDays)
  @Max(365, { message: 'O campo trialDays deve ser menor que 366' })
  @IsNumber(
    { allowNaN: false, allowInfinity: false },
    { message: 'O campo trialDays deve ser do tipo number' },
  )
  @IsNotEmpty({
    message:
      'O campo trialDays não é obrigatório, mas se for enviado deve conter um valor',
  })
  @IsOptional()
  trialDays: number;

  @ApiProperty(swaggerPlans.dto.createPlans.isActive)
  @IsBoolean({
    message: 'O campo isActive deve ser do tipo boolean',
  })
  @IsOptional()
  isActive: boolean;
}
