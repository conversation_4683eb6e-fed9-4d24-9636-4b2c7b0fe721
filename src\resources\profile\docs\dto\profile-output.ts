const userSecureId = {
  description: 'SecureId do usuário',
  type: String,
  unique: true,
  example: 'd2b6b0b2-2b4a-4c5d-8c6e-6b7a8b9c0d1e',
};

const userName = {
  description: 'Nome do usuário',
  type: String,
  unique: false,
  example: 'John Doe',
};

const userEmail = {
  description: 'Email do usuário',
  type: String,
  unique: true,
  example: '<EMAIL>',
};

const userCPF = {
  description: 'CPF do usuário',
  type: String,
  unique: true,
  example: '123.456.789-01',
};

const userCellPhone = {
  description: 'Telefone do usuário',
  type: String,
  unique: false,
  example: '+55 (11) 9 9999-9999',
};

const isUserActive = {
  description: 'Define se o usuário está ativo ou não',
  type: Boolean,
  example: true,
};

const isUserDeleted = {
  description: 'Define se o usuário está deletado ou não',
  type: Boolean,
  example: false,
};

const userCreatedAt = {
  description: 'Data de criação do usuário',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const userUpdatedAt = {
  description: 'Data da última atualização do usuário',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const userAccountSecureId = {
  description: 'SecureId da UserAccount',
  type: String,
  unique: true,
  example: 'b2b6b0b2-2b4a-4c5d-8c6e-6b7a8b9c0d1e',
};

const userAccountIsOwner = {
  description: 'Representa se o usuário é dono da conta para userAccount',
  type: Boolean,
  example: true,
};

const userAccountIsActive = {
  description: 'Representa se a UserAccount está ativa ou não',
  type: Boolean,
  example: true,
};

const userAccountDeleted = {
  description: 'Representa se a UserAccount está deletada ou não',
  type: Boolean,
  example: false,
};

const userAccountCreatedAt = {
  description: 'Data de criação da UserAccount',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const userAccountUpdatedAt = {
  description: 'Data da última atualização da UserAccount',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const accountSecureId = {
  description: 'SecureId da conta',
  type: String,
  unique: true,
  example: 'a2b6b0b2-2b4a-4c5d-8c6e-6b7a8b9c0d1e',
};

const accountCompanyName = {
  description: 'Nome da empresa',
  type: String,
  unique: true,
  example: 'BlackWater Bank',
};

const accountIsActive = {
  description: 'Define se a conta está ativa ou não',
  type: Boolean,
  example: true,
};

const accountIsDeleted = {
  description: 'Define se a conta está deletada ou não',
  type: Boolean,
  example: false,
};

const accountCreatedAt = {
  description: 'Data de criação da conta',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

const accountUpdatedAt = {
  description: 'Data da última atualização da conta',
  type: Date,
  example: '2021-07-01T00:00:00.000Z',
};

export const profileOutputDto = {
  user: {
    secureId: userSecureId,

    name: userName,
    email: userEmail,
    cpf: userCPF,
    cellPhone: userCellPhone,

    isActive: isUserActive,
    isDeleted: isUserDeleted,

    createdAt: userCreatedAt,
    updatedAt: userUpdatedAt,
  },

  userAccounts: {
    secureId: userAccountSecureId,

    isOwner: userAccountIsOwner,
    isActive: userAccountIsActive,
    isDeleted: userAccountDeleted,

    createdAt: userAccountCreatedAt,
    updatedAt: userAccountUpdatedAt,
  },

  account: {
    secureId: accountSecureId,

    companyName: accountCompanyName,

    isActive: accountIsActive,
    isDeleted: accountIsDeleted,

    createdAt: accountCreatedAt,
    updatedAt: accountUpdatedAt,
  },
};
