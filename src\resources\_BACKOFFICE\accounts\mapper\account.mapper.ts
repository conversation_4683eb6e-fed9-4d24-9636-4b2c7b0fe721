import { Accounts as PrismaAccount } from '@prisma/client';

import { Account } from '../entities/account.entity';

export class AccountMapper {
  static toModel(entity: Account) {
    const accountModel = {
      secureId: entity.secureId,
      companyName: entity.companyName,
      openaiApiKey: entity.openaiApiKey,
      prompt: entity.prompt,
      isActive: entity.isActive === false ? false : true,
      isDeleted: entity.isDeleted === false ? false : true,
    };

    return accountModel as PrismaAccount;
  }

  static toEntity(model: PrismaAccount): Account {
    const accountEntity = new Account(model);

    return accountEntity;
  }
}
