import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

export const UserIdExtractorDecorator = createParamDecorator(
  async (data: any, ctx: ExecutionContext): Promise<number> => {
    const request = ctx.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;
    const prisma = request.prismaService;

    const userModel = await prisma.users.findFirst({
      where: {
        email: user.email,
        secureId: user.subject,
      },

      select: { id: true },
    });

    return userModel.id;
  },
);
