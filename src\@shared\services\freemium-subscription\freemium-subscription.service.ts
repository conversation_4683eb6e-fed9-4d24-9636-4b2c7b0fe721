import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { 
  FREEMIUM_TRIAL_DAYS, 
  FREEMIUM_AI_SESSIONS_LIMIT 
} from '../../types/subscriptions';
import { v4 as uuidV4 } from 'uuid';

@Injectable()
export class FreemiumSubscriptionService {
  private readonly logger = new Logger(FreemiumSubscriptionService.name);

  constructor(private readonly prismaService: PrismaService) {}

  /**
   * Creates a new freemium trial subscription for an account
   */
  async createFreemiumTrialSubscription(accountId: number): Promise<void> {
    try {
      // First, get the freemium plan
      const freemiumPlan = await this.prismaService.plans.findFirst({
        where: {
          slug: 'freemium',
          isActive: true,
        },
      });

      if (!freemiumPlan) {
        throw new Error('Freemium plan not found');
      }

      // Cancel any existing active subscriptions
      await this.prismaService.subscriptions.updateMany({
        where: {
          accountId,
          isActive: true,
        },
        data: {
          isActive: false,
          status: 'canceled',
          canceledAt: new Date(),
        },
      });

      // Create new freemium trial subscription
      const trialEndsAt = new Date();
      trialEndsAt.setDate(trialEndsAt.getDate() + FREEMIUM_TRIAL_DAYS);

      await this.prismaService.subscriptions.create({
        data: {
          secureId: uuidV4(),
          cycle: 1,
          status: 'active',
          type: 'freemium_trial',
          planId: freemiumPlan.id,
          accountId,
          remainingSessions: FREEMIUM_AI_SESSIONS_LIMIT,
          gatewaySubscriptionId: null,
          startsAt: new Date(),
          endsAt: trialEndsAt, // Trial ends in 7 days
          trialEndsAt,
          isActive: true,
        },
      });

      this.logger.log(`Freemium trial subscription created for account ${accountId}`);
    } catch (error) {
      this.logger.error(`Error creating freemium trial subscription: ${error.message}`);
      throw error;
    }
  }

  /**
   * Checks if an account has an active freemium trial
   */
  async hasActiveFreemiumTrial(accountId: number): Promise<boolean> {
    const subscription = await this.prismaService.subscriptions.findFirst({
      where: {
        accountId,
        isActive: true,
        type: 'freemium_trial',
        trialEndsAt: {
          gte: new Date(),
        },
      },
    });

    return !!subscription;
  }

  /**
   * Checks if freemium trial has expired and converts to expired state
   */
  async checkAndExpireFreemiumTrials(): Promise<void> {
    const expiredTrials = await this.prismaService.subscriptions.findMany({
      where: {
        type: 'freemium_trial',
        isActive: true,
        trialEndsAt: {
          lt: new Date(),
        },
      },
    });

    if (expiredTrials.length > 0) {
      await this.prismaService.subscriptions.updateMany({
        where: {
          id: {
            in: expiredTrials.map(sub => sub.id),
          },
        },
        data: {
          type: 'freemium_expired',
          remainingSessions: 0, // Disable AI sessions
        },
      });

      this.logger.log(`Expired ${expiredTrials.length} freemium trial subscriptions`);
    }
  }

  /**
   * Gets freemium trial status for an account
   */
  async getFreemiumTrialStatus(accountId: number): Promise<{
    hasActiveTrial: boolean;
    daysRemaining: number;
    sessionsRemaining: number;
    trialEndsAt: Date | null;
  }> {
    const subscription = await this.prismaService.subscriptions.findFirst({
      where: {
        accountId,
        isActive: true,
        type: 'freemium_trial',
      },
    });

    if (!subscription || !subscription.trialEndsAt) {
      return {
        hasActiveTrial: false,
        daysRemaining: 0,
        sessionsRemaining: 0,
        trialEndsAt: null,
      };
    }

    const now = new Date();
    const trialEndsAt = subscription.trialEndsAt;
    const hasActiveTrial = trialEndsAt > now;
    const daysRemaining = hasActiveTrial 
      ? Math.ceil((trialEndsAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      : 0;

    return {
      hasActiveTrial,
      daysRemaining,
      sessionsRemaining: subscription.remainingSessions || 0,
      trialEndsAt,
    };
  }

  /**
   * Checks if AI features should be enabled for freemium users
   */
  async shouldEnableAIForFreemium(accountId: number): Promise<boolean> {
    const status = await this.getFreemiumTrialStatus(accountId);
    return status.hasActiveTrial && status.sessionsRemaining > 0;
  }
}
