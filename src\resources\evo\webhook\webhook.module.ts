import { Module, OnModuleInit } from '@nestjs/common';
import { WebhookService } from './webhook.service';
import { WebhookController } from './webhook.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { ChatsModule } from 'src/resources/chats/chats.module';
import { MessageChainModule } from 'src/third-party/langchain/message-chain/message-chain.module';
import { EvoMessageModule } from 'src/third-party/evo/message/message.module';
import { AIModule } from 'src/resources/ia/ai.module';
import { S3Module } from 'src/third-party/s3/s3.module';
import { OpenIAModule } from 'src/third-party/openIA/openIA.module';
import { RabbitMQModule } from 'src/third-party/rabbitmq/rabbitmq.module';
import { RabbitMQService } from 'src/third-party/rabbitmq/rabbitmq.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SubscriptionSessionModule } from 'src/@shared/services/subscription-session/subscription-session.module';
import { NotificationModule } from 'src/resources/notification/notification.module';

@Module({
  imports: [
    PrismaModule,
    OpenIAModule,
    ChatsModule,
    S3Module,
    MessageChainModule,
    EvoMessageModule,
    AIModule,
    RabbitMQModule,
    SubscriptionSessionModule,
    NotificationModule,
  ],
  controllers: [WebhookController],
  providers: [WebhookService],
})
export class WebhookModule implements OnModuleInit {
  constructor(
    private readonly rabbitMQService: RabbitMQService,
    private readonly webhookService: WebhookService,
  ) {}

  async onModuleInit() {
    const queue = 'incoming-evo-messages';
    const updateQueue = 'update-integration-status';

    await this.rabbitMQService.addConsumer(queue, async (msg) => {
      await this.webhookService.processIncomingEvoMessage(msg);
    });

    await this.rabbitMQService.addConsumer(updateQueue, async (msg) => {
      await this.webhookService.processUpdateIntegrationStatus(msg);
    });
  }
}
