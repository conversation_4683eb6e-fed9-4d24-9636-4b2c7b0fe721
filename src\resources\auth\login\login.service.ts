import {
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { HashService } from '../jwt/hash/hash.service';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { UserMapper } from 'src/resources/users/mapper/user.mapper';

import { LoginInputDto } from './dto/login-input.dto';
import { LoginOutputDto } from './dto/login-output.dto';
import { ChangeAccountInputDto } from './dto/select-account-input.dto';

import { JwtTokenService } from '../jwt/token/token.service';
import { PayloadService } from '../jwt/payload/payload.service';
import { UserWithUsersAccountModel } from 'src/resources/users/mapper/type';

@Injectable()
export class LoginService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly hashService: HashService,
    private readonly tokenService: JwtTokenService,
    private readonly payloadService: PayloadService,
  ) {}

  async changeAccount(
    ChangeAccountInputDto: ChangeAccountInputDto,
    userSecureId: string,
  ) {
    const modelUser = await this.prismaService.users.findFirst({
      where: { secureId: userSecureId },

      include: {
        usersAccounts: {
          where: {
            account: {
              is: { secureId: ChangeAccountInputDto.accountSecureId },
            },
          },

          select: {
            role: true,
            account: true,

            accountsPermissions: {
              select: {
                permission: true,
              },
            },

            isActive: true,
            isDeleted: true,
          },
        },
      },
    });

    this.changeAccountExceptionHandler(modelUser);

    const user = UserMapper.fromModelToEntity(modelUser);
    const userTokenPayload = this.payloadService.generateUserTokenPayload(
      user,
      ChangeAccountInputDto.accountSecureId,
    );

    const token = this.tokenService.generate(userTokenPayload);

    return {
      accessToken: token,
    };
  }

  async login(loginInputDto: LoginInputDto): Promise<LoginOutputDto> {
    const modelUser = await this.prismaService.users.findFirst({
      where: {
        email: { equals: loginInputDto.email },
        isActive: true,
        isDeleted: false,
        usersAccounts: {
          some: {
            isDeleted: false,
          },
        },
      },

      include: {
        usersAccounts: {
          where: {
            isActive: true,
            isDeleted: false,

            account: {
              isActive: true,
              isDeleted: false,
            },
          },

          select: {
            role: true,
            account: {
              include: {
                subscriptions: {
                  where: {
                    isActive: true,
                  },
                  include: {
                    plan: {
                      select: {
                        slug: true,
                      },
                    },
                  },
                },
              },
            },

            accountsPermissions: {
              select: {
                permission: true,
              },
            },

            isActive: true,
            isDeleted: true,

            isOwner: true,
          },
        },
      },
    });
    if (!modelUser) {
      throw new UnauthorizedException('Usuário ou senha inválidos');
    }

    if (!modelUser.usersAccounts) {
      throw new InternalServerErrorException(
        'User malformed, Contata o administrador',
      );
    }

    const didPasswordMatch = await this.hashService.compare(
      loginInputDto.password,
      modelUser.password,
    );
    if (!didPasswordMatch) {
      throw new UnauthorizedException('Usuário ou senha inválidos');
    }

    // NOTE: Como o user só pode ser Owner de 1 única Account é corrido o array de accounts para ver se ele é dono de alguma account, caso ele for ela é retornada para preencher o payload do token
    const isOwnerOnAnyAccount = modelUser.usersAccounts.find(
      (data) =>
        (data.isOwner === true) !==
        // && data.account.subscriptions.find((data) => data.isActive === true) // NOTE: Comentei para que o user possa logar mesmo sem ter uma inscrição ativa
        undefined,
    );

    // NOTE: Caso ele nao seja Owner de nenhuma account esse usuário é apenas um atendente sem conta própria, então é retornado a primeira account que ele tem acesso.

    let accountSecureId = '';
    modelUser.usersAccounts.find((data) =>
      data.role.name === 'Master' || data.role.name === 'Backoffice'
        ? (accountSecureId = data.account.secureId)
        : data.account.subscriptions.find((data) => data.isActive === true) !==
            undefined
          ? (accountSecureId = data.account.secureId)
          : null,
    );

    if (!isOwnerOnAnyAccount && accountSecureId === '') {
      throw new NotFoundException(
        'Conta sem inscrição ativa, ou sem inscrição vinculada',
      );
    }
    // const accountSecureId = modelUser.usersAccounts[0].account.secureId;

    const user = UserMapper.fromModelToEntity(modelUser);

    const userTokenPayload = this.payloadService.generateUserTokenPayload(
      user,
      isOwnerOnAnyAccount
        ? isOwnerOnAnyAccount.account.secureId
        : accountSecureId,
    );

    const token = this.tokenService.generate(userTokenPayload);

    return {
      accessToken: token,
    };
  }

  private changeAccountExceptionHandler(
    userModel: UserWithUsersAccountModel,
  ): void {
    if (!userModel) {
      throw new NotFoundException('Problemas para encontrar o usuário');
    }
    if (!userModel.usersAccounts || userModel.usersAccounts.length === 0) {
      throw new NotFoundException('Conta não encontrada');
    }

    if (userModel.isDeleted) {
      throw new ForbiddenException(
        'Sua conta de usuário está excluída. Entre em contato com o suporte',
      );
    }
    if (!userModel.isActive) {
      throw new ForbiddenException(
        'Sua conta de usuário está inativada. Entre em contato com o suporte',
      );
    }

    if (userModel.usersAccounts[0].isDeleted) {
      throw new ForbiddenException('A conta que deseja acessar está excluída');
    }
    if (!userModel.usersAccounts[0].isActive) {
      throw new ForbiddenException('A conta que deseja acessar está inativa');
    }

    if (userModel.usersAccounts[0].account.isDeleted) {
      throw new ForbiddenException('A conta que deseja acessar está excluída');
    }
    if (!userModel.usersAccounts[0].account.isActive) {
      throw new ForbiddenException('A conta que deseja acessar está inativa');
    }
  }
}
