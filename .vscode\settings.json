{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "material-icon-theme.activeIconPack": "nest", "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.useTabStops": true, "cSpell.words": ["autoincrement", "backffice", "backoffice", "BACKOFFICE", "datasource", "<PERSON><PERSON>", "dtos", "insighter", "marston", "<PERSON><PERSON>", "mysql", "<PERSON><PERSON><PERSON>", "plyrchat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qdrant"], "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}