import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ChatbotsService } from './chatbots.service';
import { CreateChatbotDto } from './dto/create-chatbot.dto';
import { UpdateChatbotDto } from './dto/update-chatbot.dto';
import { ChatbotListQueryDto, ChatbotMessagesQueryDto } from './dto/chatbot-query.dto';
import { ChatbotListItemDto } from './dto/chatbot-list.output.dto';
import { ChatbotDetailOutputDto, ChatbotMessageDto } from './dto/chatbot-detail.output.dto';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';

@ApiTags('Backoffice - Chatbots')
@Controller('chatbots')
@UseGuards(JwtGuard, RoleGuard)
@Roles(['BACKOFFICE'])
@Permissions(['backoffice_view'])
export class ChatbotsController {
  constructor(private readonly chatbotsService: ChatbotsService) {}

  @Post()
  @Permissions(['backoffice_create'])
  @ApiOperation({ summary: 'Create a new chatbot' })
  create(@Body() createChatbotDto: CreateChatbotDto) {
    return this.chatbotsService.create(createChatbotDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of all chatbots with analytics' })
  @ApiResponse({ type: IWithPagination })
  findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery() isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @Query() query: ChatbotListQueryDto
  ): Promise<IWithPagination<ChatbotListItemDto>> {
    return this.chatbotsService.findAll(paginationQuery, isActiveIsDeletedQuery, query);
  }

  @Get(':secureId')
  @ApiOperation({ summary: 'Get detailed chatbot information with message history' })
  @ApiResponse({ type: ChatbotDetailOutputDto })
  findOne(@Param('secureId') secureId: string): Promise<ChatbotDetailOutputDto> {
    return this.chatbotsService.findOne(secureId);
  }

  @Get(':secureId/messages')
  @ApiOperation({ summary: 'Get paginated messages for a specific chatbot' })
  @ApiResponse({ type: IWithPagination })
  async getChatbotMessages(
    @Param('secureId') secureId: string,
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @Query() query: ChatbotMessagesQueryDto
  ): Promise<IWithPagination<ChatbotMessageDto>> {
    const direction = query.messageDirection || 'sent';
    return this.chatbotsService.getChatbotMessagesBySecureId(secureId, direction, paginationQuery, query);
  }

  @Patch(':id')
  @Permissions(['backoffice_edit'])
  @ApiOperation({ summary: 'Update a chatbot' })
  update(@Param('id') id: string, @Body() updateChatbotDto: UpdateChatbotDto) {
    return this.chatbotsService.update(+id, updateChatbotDto);
  }

  @Delete(':id')
  @Permissions(['backoffice_delete'])
  @ApiOperation({ summary: 'Delete a chatbot' })
  remove(@Param('id') id: string) {
    return this.chatbotsService.remove(+id);
  }
}
