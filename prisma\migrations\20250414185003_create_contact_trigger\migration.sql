-- Gatilho para operações de INSERT em chat_sessions
CREATE TRIGGER `after_chat_session_insert_upsert_contact`
AFTER INSERT ON `chat_sessions` -- Nome da tabela atualizado
FOR EACH ROW
BEGIN
    DECLARE existing_contact_id INT;

    -- Prosseguir apenas se houver informações de contato e account_id
    IF NEW.account_id IS NOT NULL AND (NEW.customer_email IS NOT NULL OR NEW.customer_phone IS NOT NULL OR NEW.customer_document IS NOT NULL) THEN

        -- Tentar encontrar um contato existente com base em identificadores não nulos para a conta
        SELECT id INTO existing_contact_id
        FROM `contacts` -- Nome da tabela atualizado
        WHERE account_id = NEW.account_id -- Nome da coluna atualizado
          AND (
            (NEW.customer_email IS NOT NULL AND email = NEW.customer_email) OR -- Nomes das colunas atualizados
            (NEW.customer_phone IS NOT NULL AND phone = NEW.customer_phone) OR -- Nomes das colunas atualizados
            (NEW.customer_document IS NOT NULL AND document = NEW.customer_document) -- Nomes das colunas atualizados
          )
        LIMIT 1; -- Encontrar no máximo uma correspondência

        IF existing_contact_id IS NOT NULL THEN
            -- Atualizar o contato encontrado
            UPDATE `contacts` -- Nome da tabela atualizado
            SET
                -- Atualizar nome se um novo for fornecido, caso contrário, manter o existente
                name = COALESCE(NEW.customer_name, name), -- Nome da coluna atualizado
                -- Atualizar email se um novo for fornecido, caso contrário, manter o existente
                email = IF(NEW.customer_email IS NOT NULL, NEW.customer_email, email), -- Nomes das colunas atualizados
                -- Atualizar telefone se um novo for fornecido, caso contrário, manter o existente
                phone = IF(NEW.customer_phone IS NOT NULL, NEW.customer_phone, phone), -- Nomes das colunas atualizados
                -- Atualizar documento se um novo for fornecido, caso contrário, manter o existente
                document = IF(NEW.customer_document IS NOT NULL, NEW.customer_document, document), -- Nomes das colunas atualizados
                last_updated_session_id = NEW.id, -- Nome da coluna atualizado (assumindo @map)
                updated_at = CURRENT_TIMESTAMP(3) -- Nome da coluna atualizado
            WHERE id = existing_contact_id;
        ELSE
            -- Inserir um novo contato
            INSERT INTO `contacts` (secure_id, account_id, email, phone, document, name, last_updated_session_id, created_at, updated_at) -- Nomes da tabela e colunas atualizados
            VALUES (UUID(), NEW.account_id, NEW.customer_email, NEW.customer_phone, NEW.customer_document, NEW.customer_name, NEW.id, CURRENT_TIMESTAMP(3), CURRENT_TIMESTAMP(3)); -- Nomes das colunas atualizados
        END IF;
    END IF;
END;



-- Gatilho para operações de UPDATE em chat_sessions
CREATE TRIGGER `after_chat_session_update_upsert_contact`
AFTER UPDATE ON `chat_sessions` -- Nome da tabela atualizado
FOR EACH ROW
BEGIN
    DECLARE existing_contact_id INT;

    -- Verificar se account_id não é nulo, se algum campo relevante mudou (tratando NULLs)
    -- e se o novo registro contém pelo menos um identificador chave.
    IF NEW.account_id IS NOT NULL AND
       ( -- Verificar se algum campo relevante é diferente (NULL-safe)
         NOT (OLD.customer_name <=> NEW.customer_name) OR
         NOT (OLD.customer_email <=> NEW.customer_email) OR
         NOT (OLD.customer_phone <=> NEW.customer_phone) OR
         NOT (OLD.customer_document <=> NEW.customer_document) OR
         NOT (OLD.account_id <=> NEW.account_id)
       ) AND
       ( -- Garantir que o novo registro tenha pelo menos um identificador chave
         NEW.customer_email IS NOT NULL OR
         NEW.customer_phone IS NOT NULL OR
         NEW.customer_document IS NOT NULL
       )
    THEN
        -- Tentar encontrar um contato existente com base em identificadores não nulos para a NOVA conta
        SELECT id INTO existing_contact_id
        FROM `contacts` -- Nome da tabela atualizado
        WHERE account_id = NEW.account_id -- Nome da coluna atualizado
          AND (
            (NEW.customer_email IS NOT NULL AND email = NEW.customer_email) OR -- Nomes das colunas atualizados
            (NEW.customer_phone IS NOT NULL AND phone = NEW.customer_phone) OR -- Nomes das colunas atualizados
            (NEW.customer_document IS NOT NULL AND document = NEW.customer_document) -- Nomes das colunas atualizados
          )
        LIMIT 1; -- Encontrar no máximo uma correspondência

        IF existing_contact_id IS NOT NULL THEN
            -- Atualizar o contato encontrado
            UPDATE `contacts` -- Nome da tabela atualizado
            SET
                name = COALESCE(NEW.customer_name, name), -- Nome da coluna atualizado
                email = IF(NEW.customer_email IS NOT NULL, NEW.customer_email, email), -- Nomes das colunas atualizados
                phone = IF(NEW.customer_phone IS NOT NULL, NEW.customer_phone, phone), -- Nomes das colunas atualizados
                document = IF(NEW.customer_document IS NOT NULL, NEW.customer_document, document), -- Nomes das colunas atualizados
                last_updated_session_id = NEW.id, -- Nome da coluna atualizado (assumindo @map)
                updated_at = CURRENT_TIMESTAMP(3) -- Nome da coluna atualizado
            WHERE id = existing_contact_id;
        ELSE
            -- Inserir um novo contato para os detalhes da NOVA conta
            INSERT INTO `contacts` (secure_id, account_id, email, phone, document, name, last_updated_session_id, created_at, updated_at) -- Nomes da tabela e colunas atualizados
            VALUES (UUID(), NEW.account_id, NEW.customer_email, NEW.customer_phone, NEW.customer_document, NEW.customer_name, NEW.id, CURRENT_TIMESTAMP(3), CURRENT_TIMESTAMP(3)); -- Nomes das colunas atualizados
        END IF;
    END IF;
END;
