import { FormatMoney } from 'src/@shared/helpers/money/format-money';
import { AccountTransactionWithAccountAndSubscription } from '../account-transaction.contract';
import { AccountTransactionOutputDto } from '../dto/create-transaction.dto';

export class AccountTransactionMapper {
  static fromAccountTransactionWithAccountAndSubscriptionToOutputDto(
    model: AccountTransactionWithAccountAndSubscription,
  ): AccountTransactionOutputDto {
    return {
      secureId: model.secureId,

      amount: FormatMoney.fromModelToOutput(model.amount),
      status: model.status,

      account: {
        secureId: model.account.secureId,

        companyName: model.account.companyName,

        isActive: model.account.isActive,

        isDeleted: model.account.isDeleted,

        createdAt: model.account.createdAt,
        updatedAt: model.account.updatedAt,
      },

      subscription: {
        secureId: model.subscription.secureId,

        cycle: model.subscription.cycle,
        status: model.subscription.status,
        type: model.subscription.type,

        gatewaySubscriptionId: model.subscription.gatewaySubscriptionId,

        startsAt: model.subscription.startsAt,
        endsAt: model.subscription.endsAt,
        canceledAt: model.subscription.canceledAt,

        createdAt: model.subscription.createdAt,
        updatedAt: model.subscription.updatedAt,
      },

      payedAt: model.payedAt,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
