import { ProfileOutputDto } from '../dto/profile-output.dto';
import { UserWithUsersAccountsAndAccountModel } from '../profile.contract';

export class ProfileFactory {
  static createProfileOutputDtoBatchFromUserWithUserAccountsAndAccountModel(
    models: UserWithUsersAccountsAndAccountModel[],
  ): ProfileOutputDto[] {
    return models.map((model) => ({
      secureId: model.secureId,

      name: model.name,
      email: model.email,
      cpf: model.cpf,
      cellPhone: model.cellPhone,

      userAccounts: model.usersAccounts.map((userAccount) => ({
        secureId: userAccount.secureId,
        account: {
          secureId: userAccount.account.secureId,
          companyName: userAccount.account.companyName,
          isActive: userAccount.account.isActive,
          isDeleted: userAccount.account.isDeleted,
          createdAt: userAccount.account.createdAt,
          updatedAt: userAccount.account.updatedAt,
        },
        isOwner: userAccount.isOwner,
        isActive: userAccount.isActive,
        isDeleted: userAccount.isDeleted,
        createdAt: userAccount.createdAt,
        updatedAt: userAccount.updatedAt,
      })),

      isActive: model.isActive,
      isDeleted: model.isDeleted,

      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    }));
  }
}
