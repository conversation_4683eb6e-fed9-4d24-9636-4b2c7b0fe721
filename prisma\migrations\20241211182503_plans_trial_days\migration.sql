/*
  Warnings:

  - Added the required column `account_id` to the `subscriptions` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `permissions` MODIFY `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete', 'Account View', 'Account Create', 'Account Edit', 'Account Delete', 'Evo Integration View', 'Evo Integration Create', 'Evo Integration Edit', 'Evo Integration Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete', 'account_view', 'account_create', 'account_edit', 'account_delete', 'evo_integration_view', 'evo_integration_create', 'evo_integration_edit', 'evo_integration_delete') NOT NULL,
    MODIFY `group` ENUM('backoffice', 'app', 'account', 'evo_integration') NOT NULL;

-- AlterTable
ALTER TABLE `plans` ADD COLUMN `trial_days` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `subscriptions` ADD COLUMN `account_id` INTEGER NOT NULL,
    ADD COLUMN `is_active` BOOLEAN NOT NULL DEFAULT true;

-- AddForeignKey
ALTER TABLE `subscriptions` ADD CONSTRAINT `subscriptions_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
