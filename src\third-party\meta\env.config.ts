import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';
import { IMetaEnvs } from './meta.contract';

export default registerAs('storage-environment', () => {
  const values: IMetaEnvs = {
    metaApiUrl: process.env.META_API_URL,
  };

  // Joi validations
  const schema = Joi.object<IMetaEnvs>({
    metaApiUrl: Joi.string().required().messages({
      'any.required': `META_API_URL is missing ENV`,
    }),
  });

  // Validates our values using the schema.
  // Passing a flag to tell <PERSON><PERSON> to not stop validation on the
  // first error, we want all the errors found.
  const { error } = schema.validate(values, { abortEarly: false });

  // If the validation is invalid, "error" is assigned a
  // ValidationError object providing more information.
  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
				${error.message}`,
    );
  }

  // If the validation is valid, then the "error" will be
  // undefined and this will return successfully.
  return values;
});
