import { Prisma } from '@prisma/client';

import { EvoIntegrationOutputDto } from '../dto/evo-integration-output.dto';

import { EvoIntegrationMapper } from '../mappers/evo-integration.mapper';

type WhatsAppIntegrationModelWithChatBot =
  Prisma.WhatsAppIntegrationGetPayload<{
    include: {
      chatbot: true;
    };
  }>;

export class EvoIntegrationFactory {
  static fromBatchModelToIntegrationDtos(
    model: WhatsAppIntegrationModelWithChatBot[],
  ): EvoIntegrationOutputDto[] {
    return model.map((item) =>
      EvoIntegrationMapper.fromModelToIntegrationDto(item),
    );
  }
}
