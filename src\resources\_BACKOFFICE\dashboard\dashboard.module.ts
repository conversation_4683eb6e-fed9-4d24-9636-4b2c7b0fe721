import { Module } from '@nestjs/common';

import { BackofficeDashboardService } from './dashboard.service';

import { BackofficeDashboardController } from './dashboard.controller';

import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [BackofficeDashboardController],
  providers: [BackofficeDashboardService],
})
export class BackofficeDashboardModule {}
