import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

// XXX: Esse cara é responsável por verificar se o token JWT é existente e válido. Não importando a role ou permission
@Injectable()
export class TokenGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.user; // Payload do token JWT

    if (!token) {
      return true;
    }

    return token;
  }
}
