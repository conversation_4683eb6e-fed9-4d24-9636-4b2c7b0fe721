import { forbidden } from './forbidden';
import { isActiveStatusParam } from './is-active';
import { isDeletedStatusParam } from './is-deleted';
import { limitParam } from './limit';
import { pageParam } from './page';
import { response } from './response';
import { searchParam } from './search';
import { secureIdParam } from './secure-id';
import { unauthorized } from './unauthorized';

export const swaggerShared = {
  status: {
    unauthorized,
    forbidden,
  },

  query: {
    isActive: isActiveStatusParam,
    isDeleted: isDeletedStatusParam,
    search: searchParam,
    page: pageParam,
    limit: limitParam,
  },

  params: {
    secureId: secureIdParam,
  },

  response: response,
};
