import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AttendantController } from './attendant.controller';
import { AttendantService } from './attendant.service';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { HashModule } from 'src/resources/auth/jwt/hash/hash.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule, HashModule],
  controllers: [AttendantController],
  providers: [AttendantService],
})
export class AttendantModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('attendant');
  }
}
