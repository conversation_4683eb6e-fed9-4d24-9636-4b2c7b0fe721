import { Prisma } from "@prisma/client";

export type ChatSessionInfosBase = Prisma.ChatSessionsGetPayload<
	{
		select: {
			customerDocument: true,
			customerEmail: true,
			customerId: true,
			customerName: true,
			customerPhone: true,
			isLeadCaptured: true,
			isAIResponder: true,
			chatMessages: {
				select: {
					receiveMessage: true,
				},
			},
			chat: {
				select: {
					chatbot: {
						select: {
							IAPrompt: true,
							isAI: true,
							leadCaptureJson: true,
							leadCaptureMessage: true,
							leadCaptureThankYouMessage: true,
							leadTriggerMessageLimit: true,
							temperature: true,
							isLeadCaptureActive: true,
							KnowledgeBase: {
								select: {
									collectionName: true,
								},
							},
						},
					},
				},
			},
			account: {
				select: {
					secureId: true,
					openaiApiKey: true,
					prompt: true,
				},
			},
		},
	}
>
