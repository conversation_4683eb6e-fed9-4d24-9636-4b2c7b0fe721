import { Contacts, Prisma } from '@prisma/client';
import { ContactCreateDto } from '../dto/contact-create.dto';
import { ContactOutputDto } from '../dto/contact-output.dto';
import { v4 as uuid } from 'uuid';

export class CreateContactFactory {
  /**
   * Maps a ContactCreateDto and accountId to the data structure
   * expected by <PERSON>risma's contacts.create method.
   */
  static createFromCreateDtoToModel(
    dto: ContactCreateDto,
    accountId: number,
  ): Prisma.ContactsCreateInput {
    // Explicitly return the type <PERSON><PERSON><PERSON> expects for creation
    return {
      secureId: uuid(), // Generate a unique identifier for the contact
      account: { connect: { id: accountId } }, // Link to the account
      name: dto.name,
      email: dto.email,
      phone: dto.phone,
      document: dto.document,
      // isActive and isDeleted default to true/false in the schema or DB
    };
  }

  /**
   * Maps a Contacts Prisma model (after creation) to a ContactOutputDto
   * for the API response.
   */
  static createFromModelToOutputDto(model: Contacts): ContactOutputDto {
    return {
      secureId: model.secureId,
      name: model.name,
      email: model.email,
      phone: model.phone,
      document: model.document,
      isActive: model.isActive,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
