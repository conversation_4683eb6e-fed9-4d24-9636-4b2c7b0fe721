import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { EFIGetTokenModule } from 'src/third-party/efi/get-token/efi-get-token.module';
import { WebhookEfiNotificationController } from './webhook-efi-notification.controller';
import { WebhookEfiNotificationService } from './webhook-efi-notification.service';

@Module({
  imports: [PrismaModule, EFIGetTokenModule],
  controllers: [WebhookEfiNotificationController],
  providers: [WebhookEfiNotificationService],
})
export class WebhookEfiNotificationModule { }
