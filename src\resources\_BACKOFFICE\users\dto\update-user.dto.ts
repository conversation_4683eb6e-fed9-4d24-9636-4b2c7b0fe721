import { ApiProperty } from '@nestjs/swagger';
import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  <PERSON>Length,
  MinLength,
} from 'class-validator';
import { IsCPF } from 'src/@shared/decorators/validation/cpf/cpf.decorator';

import { backofficeUsersSwaggerDto } from '../docs/dto';

export class UpdateUserDto {
  @ApiProperty(backofficeUsersSwaggerDto.updateUser.name)
  @MaxLength(255, { message: 'O nome precisa ter no máximo 255 caracteres.' })
  @MinLength(3, { message: 'O nome precisa ter no mínimo 3 caracteres.' })
  @IsString({ message: 'O nome precisa ser uma string.' })
  @IsNotEmpty({
    message: 'Caso você queira atualizar o nome, ele não pode ser vazio.',
  })
  @IsOptional()
  name?: string;

  @ApiProperty(backofficeUsersSwaggerDto.updateUser.email)
  @MaxLength(255, { message: 'O email precisa ter no máximo 255 caracteres.' })
  @MinLength(5, { message: 'O email precisa ter no mínimo 5 caracteres.' })
  @IsEmail({}, { message: 'O email precisa ser um email válido.' })
  @IsOptional()
  email?: string;

  @ApiProperty(backofficeUsersSwaggerDto.updateUser.cpf)
  @IsCPF({ message: 'O CPF precisa ser um CPF válido.' })
  @IsOptional()
  cpf?: string;

  @ApiProperty(backofficeUsersSwaggerDto.updateUser.password)
  @MaxLength(100, {
    message: 'A senha deve ter no máximo 100 caracteres.',
  })
  @MinLength(6, { message: 'A senha precisa ter no mínimo 6 caracteres.' })
  @IsString({ message: 'A senha precisa ser uma string.' })
  @IsOptional()
  password?: string;

  @ApiProperty(backofficeUsersSwaggerDto.updateUser.isActive)
  @IsNotEmpty({ message: 'O campo isActive não pode ser vazio.' })
  @IsOptional()
  isActive?: boolean;

  @IsArray({ message: 'As permissões precisam ser um array.' })
  @IsNotEmpty({ message: 'As permissões não podem ser vazias.' })
  @IsOptional()
  permissions?: string[];

  @IsBoolean({
    message: 'O campo todas as permissões precisa ser verdadeiro ou falso.',
  })
  @IsOptional()
  hasAllPermissions?: boolean;
}
