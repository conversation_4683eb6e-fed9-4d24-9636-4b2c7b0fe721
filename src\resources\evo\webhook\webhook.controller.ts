import { Controller, Post, Body, HttpCode } from '@nestjs/common';
import { IncomingMessageWebhookDto } from './dto/incoming-message.dto';
import { RabbitMQService } from 'src/third-party/rabbitmq/rabbitmq.service';

@Controller('whatsapp/webhook')
export class WebhookController {
  constructor(private readonly rabbitMQService: RabbitMQService) {}

  @Post()
  @HttpCode(201)
  async incomingMessages(
    @Body() incomingMessageWebhookDto: IncomingMessageWebhookDto,
  ) {
    if (incomingMessageWebhookDto.event === 'connection.update') {
      if (incomingMessageWebhookDto.data?.state === 'connecting') {
        return { status: 'awaiting next event' };
      }
      await this.rabbitMQService.publish(
        'update-integration-status',
        incomingMessageWebhookDto,
      );
      return { status: 'integration status updated' };
    } else {
      await this.rabbitMQService.publish(
        'incoming-evo-messages',
        incomingMessageWebhookDto,
      );
    }
    return { status: 'queued' };
  }
}
