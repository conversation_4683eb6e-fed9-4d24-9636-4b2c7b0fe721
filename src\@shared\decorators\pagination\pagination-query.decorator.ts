import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IPaginationDecorator } from 'src/@shared/contracts/decorators/pagination/interface';

interface PaginationProps extends Headers {
  page?: string;
  limit?: string;
  search?: string;
}

export const PaginationQuery = createParamDecorator(
  (data: any, ctx: ExecutionContext): IPaginationDecorator => {
    const request = ctx.switchToHttp().getRequest();
    const query: PaginationProps = request.query;

    let page: number = 1;
    let limit: number = 15;
    let search: string | undefined;

    if (!query?.['page']) {
      page = 1;
    } else {
      page = Number(query['page']);
    }

    if (!query?.['limit']) {
      limit = 15;
    } else {
      limit = Number(query['limit']);
    }

    if (!query?.['search']) {
      search = undefined;
    } else {
      search = query['search'];
    }

    return {
      page: Number(page),
      limit: Number(limit),
      search: search ? String(search) : undefined,
    };
  },
);
