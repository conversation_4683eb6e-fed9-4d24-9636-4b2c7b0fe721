import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

import { IsCPF } from 'src/@shared/decorators/validation/cpf/cpf.decorator';

export class CreateBackofficeInputDto {
  @MaxLength(255, { message: 'O nome deve ter no máximo 255 caracteres' })
  @MinLength(3, { message: 'O nome deve ter no mínimo 3 caracteres' })
  @IsNotEmpty({ message: 'O nome é obrigatório' })
  name: string;

  @IsEmail({}, { message: 'O email informado é inválido' })
  @IsNotEmpty({ message: 'O email é obrigatório' })
  email: string;

  @MaxLength(255, { message: 'A senha deve ter no máximo 255 caracteres' })
  @MinLength(6, { message: 'A senha deve ter no mínimo 6 caracteres' })
  @IsNotEmpty({ message: 'A senha é obrigatória' })
  password: string;

  @IsCPF({ message: 'O CPF informado é inválido' })
  @MaxLength(14, { message: 'O CPF deve ter no máximo 14 caracteres' })
  @MinLength(11, { message: 'O CPF deve ter no mínimo 11 caracteres' })
  @IsNotEmpty({ message: 'O CPF é obrigatório' })
  cpf: string;

  @IsOptional()
  permissionsSecureIds: string[];

  @IsOptional()
  hasAllPermissions: boolean;
}
