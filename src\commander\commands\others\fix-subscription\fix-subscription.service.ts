import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class FixSubscriptionService {
  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Arrumando Subscription:`);
    const subscriptions = await prisma.subscriptions.findMany({
      select: {
        id: true,
        secureId: true,
        remainingSessions: true,
        trialEndsAt: true,
        createdAt: true,
        account: {
          select: {
            secureId: true,
          },
        },
        plan: {
          select: {
            secureId: true,
            name: true,
            trialDays: true,
            slug: true,
            iaMessagesLimit: true,
          },
        },
      },
    });

    const totalSubs = subscriptions.length;
    let processedSubs = 0;

    // Initial progress bar
    this.updateProgressBar(processedSubs, totalSubs);

    for (const subscription of subscriptions) {
      await prisma.subscriptions.update({
        where: {
          id: subscription.id,
        },
        data: {
          remainingSessions: subscription.plan.iaMessagesLimit,
          trialEndsAt:
            subscription.plan.trialDays > 0
              ? new Date(
                  new Date().setDate(
                    subscription.createdAt.getDate() +
                      subscription.plan.trialDays,
                  ),
                )
              : null,
        },
      });

      // Update progress after each subscription is processed
      processedSubs++;
      this.updateProgressBar(processedSubs, totalSubs);
    }

    console.log('\n\x1b[32m', `Concluído! ${totalSubs} mensagens atualizadas.`);
  }

  private updateProgressBar(current: number, total: number): void {
    const percentage = Math.floor((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.floor((percentage * barLength) / 100);

    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(barLength - filledLength);

    process.stdout.write(
      `\r[${filledBar}${emptyBar}] ${percentage}% (${current}/${total})`,
    );
  }
}
