import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { FixLastUpdatedMessageService } from './fix-last-updated-messages.service';

@Command({
  name: 'fix-last-updated-messages',
  description:
    'Arruma as sessões do sistema preenchendo o campo lastUpdated com o valor correto para cada mensagem, resolvendo o problema das mensagens no frontend',
})
@Injectable()
export class FixLastUpdatedMessagesCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(
    private readonly fixLastMessagesUpdatedService: FixLastUpdatedMessageService,
  ) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    await this.fixLastMessagesUpdatedService
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
