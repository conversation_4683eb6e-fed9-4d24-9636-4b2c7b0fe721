import {
  Controller,
  Get,
  Query,
  UseGuards,
  Post,
  Body,
  Param,
  ParseIntPipe,
  Patch,
  Delete,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { SubscriptionGuard } from 'src/@shared/guards/subscription/subscription.guard';
import { ContactService } from './contact.service';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { ContactOutputDto } from './dto/contact-output.dto';
import { ContactCreateDto } from './dto/contact-create.dto';
import { ContactUpdateDto } from './dto/contact-update.dto';

@Controller('contact')
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @UseGuards(JwtGuard, RoleGuard, SubscriptionGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_contact_create', 'app_create'])
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body() createContactDto: ContactCreateDto,
    @AccountIdExtractor() accountId: number,
  ): Promise<ContactOutputDto> {
    return await this.contactService.create(createContactDto, accountId);
  }

  @UseGuards(JwtGuard, RoleGuard, SubscriptionGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_contact_view', 'app_view'])
  @Get()
  @HttpCode(HttpStatus.OK)
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountIdExtractor() accountId: number,
    @Query('search') searchTerm?: string,
  ): Promise<IWithPagination<ContactOutputDto>> {
    return await this.contactService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountId,
      searchTerm,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, SubscriptionGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_contact_view', 'app_view'])
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async findOne(
    @Param('id') secureId: string,
    @AccountIdExtractor() accountId: number,
  ): Promise<ContactOutputDto> {
    return await this.contactService.findOne(secureId, accountId);
  }

  @UseGuards(JwtGuard, RoleGuard, SubscriptionGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_contact_edit', 'app_edit'])
  @Patch(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async update(
    @Param('id') secureId: string,
    @Body() updateContactDto: ContactUpdateDto,
    @AccountIdExtractor() accountId: number,
  ): Promise<void> {
    return await this.contactService.update(
      secureId,
      updateContactDto,
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, SubscriptionGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_contact_delete', 'app_delete'])
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id') secureId: string,
    @AccountIdExtractor() accountId: number,
  ): Promise<void> {
    await this.contactService.remove(secureId, accountId);
  }
}
