import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';

@Injectable()
export class UploadOriginGuard implements CanActivate {
  private allowedOrigins: string[];

  constructor() {
    const env =
      process.env.ALLOWED_UPLOAD_ORIGINS ||
      'localhost:3000,app.plyrchat.com.br';
    this.allowedOrigins = env
      .split(',')
      .map((url) => url.trim())
      .filter(Boolean);
  }

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const origin =
      request.headers['origin'] || request.headers['referer'] || '';
    if (!origin)
      throw new ForbiddenException('Não é possivel identificar a origem');
    const isAllowed = this.allowedOrigins.some((allowed) =>
      origin.includes(allowed),
    );
    if (!isAllowed) throw new ForbiddenException('Origem não permitida');
    return true;
  }
}
