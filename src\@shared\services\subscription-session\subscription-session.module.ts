import { Module } from '@nestjs/common';
import { SubscriptionSessionService } from './subscription-session.service';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { FreemiumSubscriptionModule } from '../freemium-subscription/freemium-subscription.module';

@Module({
  imports: [PrismaModule, FreemiumSubscriptionModule],
  providers: [SubscriptionSessionService],
  exports: [SubscriptionSessionService],
})
export class SubscriptionSessionModule {}