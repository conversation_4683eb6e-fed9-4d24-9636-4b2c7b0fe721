import { v4 as uuidV4 } from 'uuid';

type KnowledgeBaseConstructorProps = {
  id?: number;
  secureId?: string;

  accountId?: number;
  chatbotId?: number;

  content?: string;

  collectionName?: string;
  chunkSize: number;
  chunkOverlap: number;

  isActive?: boolean;
  isDeleted?: boolean;

  createdAt?: Date;
  updatedAt?: Date;
};

type KnowledgeBaseJson = {
  secureId: string;

  collectionName: string;
  chunkSize: number;
  chunkOverlap: number;

  isActive: boolean;
  isDeleted: boolean;

  content?: string;

  createdAt: Date;
  updatedAt: Date;
};

export class KnowledgeBase {
  id?: number;
  secureId?: string;

  accountId: number;
  chatbotId: number;
  uploadId?: number;

  collectionName?: string;
  chunkSize: number;
  chunkOverlap: number;

  isActive: boolean;
  isDeleted: boolean;

  content?: string;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: KnowledgeBaseConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.accountId = props?.accountId ? props.accountId : undefined;
    this.chatbotId = props?.chatbotId ? props.chatbotId : undefined;

    this.collectionName = props?.collectionName
      ? props.collectionName
      : uuidV4();
    this.chunkSize = props.chunkSize;
    this.chunkOverlap = props.chunkOverlap;

    this.isActive = props?.isActive ? props.isActive : true;
    this.isDeleted = props?.isDeleted ? props.isDeleted : false;

    this.content = props?.content ? props.content : undefined;

    this.createdAt = props?.createdAt ? props.createdAt : new Date();
    this.updatedAt = props?.updatedAt ? props.updatedAt : new Date();
  }

  toJson(): KnowledgeBaseJson {
    return {
      secureId: this.secureId,
      collectionName: this.collectionName,
      chunkSize: this.chunkSize,
      chunkOverlap: this.chunkOverlap,
      isActive: this.isActive,
      isDeleted: this.isDeleted,
      content: this.content,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
