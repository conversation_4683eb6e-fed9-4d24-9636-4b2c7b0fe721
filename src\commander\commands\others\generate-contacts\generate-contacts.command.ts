import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { GenerateContactsService } from './generate-contacts.service'; // Import the new service

@Command({
  name: 'generate-contacts', // Updated command name
  description:
    'Generates or updates contacts in the contacts table based on information from chat_sessions.', // Updated description
})
@Injectable()
export class GenerateContactsCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(
    private readonly generateContactsService: GenerateContactsService, // Inject the new service
  ) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();
    console.log('Initializing contact generation...');

    try {
      await this.generateContactsService.execute(this.prisma); // Execute the service logic
      console.log('Contact generation finished successfully.');
    } catch (error) {
      console.error('Error during contact generation:', error);
    } finally {
      await this.prisma.$disconnect();
      console.log('Database connection closed.');
    }
  }
}
