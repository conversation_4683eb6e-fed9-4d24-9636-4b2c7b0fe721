const name = {
  description: 'Nome do usuário',
  required: false,
  uniqueItems: false,
  maxLength: 255,
  minLength: 3,
  type: String,
  example: '<PERSON>',
};

const email = {
  description: 'Email do usuário. Deve ser um e-mail válido',
  required: false,
  uniqueItems: true,
  maxLength: 255,
  minLength: 3,
  type: String,
  example: '<EMAIL>',
};

const password = {
  description: 'Senha do usuário',
  required: false,
  uniqueItems: false,
  maxLength: 100,
  minLength: 6,
  type: String,
  example: 'MyPassword',
};

const cpf = {
  description: 'CPF do usuário. Deve ser um CPF válido',
  required: false,
  uniqueItems: true,
  maxLength: 11,
  minLength: 11,
  type: String,
  example: '123.456.789-01',
};

const isActive = {
  description: 'Define se o usuário está ativo ou não',
  required: false,
  type: Boolean,
  example: true,
};

const cellPhone = {
  description: 'Telefone do usuário',
  required: false,
  uniqueItems: false,
  maxLength: 20,
  type: String,
  example: '+55 (11) 9 9999-9999',
};

const companyName = {
  description:
    'Nome da empresa do usuário. Será alterado apenas para usuários do tipo OWNER da empresa.',
  required: false,
  uniqueItems: false,
  maxLength: 255,
  minLength: 3,
  type: String,
  example: 'My Company',
};

export const profileUpdateDto = {
  name,
  email,
  password,
  cpf,
  isActive,
  cellPhone,
  companyName,
};
