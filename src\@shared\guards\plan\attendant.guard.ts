import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o usuário pode cadastrar um novo Atendente.
 */
@Injectable()
export class CanAddNewAttendantGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user; // NOTE: Pequeno Gafanhoto isso aqui é para pegar o token no header.
    const prismaClient = request.prismaService;

    // NOTE: Gafanhotenho essa parada aqui é um BYPASS para master ;)
    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    if (!user.activeAccount.accountSecureId) {
      throw new BadRequestException(
        'Nenhuma Conta encontrada para esse usuário. Entre em contato com o suporte',
      );
    }

    const accountData = await prismaClient.accounts.findFirst({
      where: {
        secureId: { equals: user.activeAccount.accountSecureId },
      },

      select: {
        id: true,
        subscriptions: {
          where: {
            isActive: true,
            endsAt: { gte: new Date() },
          },

          select: {
            isActive: true,

            plan: {
              select: {
                attendantsLimit: true,
              },
            },
          },
        },
      },
    });

    const usersAccount = await prismaClient.usersAccounts.findMany({
      where: {
        accountId: accountData.id,
        isActive: true,
        isDeleted: false,
        isOwner: false,
      },

      select: {
        secureId: true,
      },
    });

    const totalAttendants = usersAccount.length;
    const attendantsLimit = accountData.subscriptions[0].plan.attendantsLimit;
    if (totalAttendants >= attendantsLimit) {
      throw new BadRequestException(
        'Você atingiu o limite de atendentes cadastrados.',
      );
    }

    return true;
  }
}
