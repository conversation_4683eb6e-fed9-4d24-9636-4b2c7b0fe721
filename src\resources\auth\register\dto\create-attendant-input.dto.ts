import {
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { IsCPF } from 'src/@shared/decorators/validation/cpf/cpf.decorator';

export class CreateAttendantInputDto {
  @MaxLength(255, {
    message: 'O nome do atendente deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O nome do atendente deve ter no mínimo 3 caracteres',
  })
  @IsNotEmpty({ message: 'O nome do atendente é obrigatório' })
  name: string;

  @IsEmail({}, { message: 'O email do atendente é inválido' })
  @IsNotEmpty({ message: 'O email do atendente é obrigatório' })
  email: string;

  @IsCPF({ message: 'O CPF do atendente é inválido' })
  @MaxLength(14, {
    message: 'O CPF do atendente deve ter no máximo 14 caracteres',
  })
  @MinLength(11, {
    message: 'O CPF do atendente deve ter no mínimo 11 caracteres',
  })
  @IsNotEmpty({ message: 'O CPF do atendente é obrigatório' })
  cpf: string;

  @MaxLength(20, {
    message: 'O telefone do atendente deve ter no máximo 20 caracteres',
  })
  @IsOptional()
  cellPhone?: string;

  @MinLength(6, {
    message: 'A senha do atendente deve ter no mínimo 6 caracteres',
  })
  @IsNotEmpty({ message: 'A senha do atendente é obrigatória' })
  password: string;

  @IsOptional()
  permissionsSecureIds: string[];

  @IsOptional()
  participatesInRotation: boolean;

  @IsOptional()
  hasAllPermissions: boolean;
}
