import { applyDecorators } from '@nestjs/common';
import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { swaggerShared } from 'src/@shared/docs/swagger';
import { backofficeDashboardSwaggerApi } from './api';

function FindAll() {
  return applyDecorators(
    ApiOperation({
      summary: 'Acessa as informações do dashboard para Backoffice',
      description:
        'Acessa as informações disponíveis para a conta de **backoffice**. Para poder utilizar esse recurso o usuário deve ter a __role__ de **BACKOFFICE** e a _permission_ de **dashboard view**.',
    }),

    ApiResponse(
      backofficeDashboardSwaggerApi.findAllDashboard.status.okResponse,
    ),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),

    ApiQuery({
      name: 'startDate',
      required: false,
      type: String,
      description:
        'Data de início para filtrar as informações. Seguindo o formato: YYYY-MM-DD',
      example: '2021-12-30',
    }),
    ApiQuery({
      name: 'endDate',
      required: false,
      type: String,
      description:
        'Data de fim para filtrar as informações. Seguindo o formato: YYYY-MM-DD',
      example: '2022-12-31',
    }),
  );
}

export const BackofficeDashboardSwagger = {
  FindAll,
};
