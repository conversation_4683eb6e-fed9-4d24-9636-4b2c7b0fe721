import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { LoginController } from './login.controller';

import { HashModule } from '../jwt/hash/hash.module';
import { TokenModule } from '../jwt/token/token.module';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';

import { LoginService } from './login.service';
import { PayloadService } from '../jwt/payload/payload.service';

import loginEnvs from './env.config';
import { JwtStrategyModule } from '../jwt/strategy/hash.module';

@Module({
  imports: [
    ConfigModule.forFeature(loginEnvs),
    HashModule,
    PrismaModule,
    TokenModule,
    JwtStrategyModule,

    JwtModule.registerAsync({
      imports: [ConfigModule.forFeature(loginEnvs)],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: { expiresIn: '10s' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [LoginController],
  providers: [LoginService, PayloadService],
  exports: [LoginService],
})
export class LoginModule {}
