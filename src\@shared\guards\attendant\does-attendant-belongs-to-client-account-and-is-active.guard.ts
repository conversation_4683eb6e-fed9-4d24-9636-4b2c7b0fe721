import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

/**
 * @description Esse guard é responsável por verificar se o atendente pertence a conta do Cliente.
 */
@Injectable()
export class DoesAttendantBelongsToClientAccountAndIsActiveGuard
  implements CanActivate
{
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user; // NOTE: Pequeno Gafanhoto isso aqui é para pegar o token no header.
    const params = context.switchToHttp().getRequest().params;
    if (!params.secureId) {
      throw new BadRequestException('Parâmetro secureId não encontrado');
    }
    if (params.secureId === user.subject) {
      throw new BadRequestException('Você não pode excluir a própria conta');
    }

    // NOTE: Gafanhotenho essa parada aqui é um BYPASS para master ;)
    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    if (!user.activeAccount.accountSecureId) {
      throw new BadRequestException(
        'Nenhuma Conta encontrada para esse usuário. Entre em contato com o suporte',
      );
    }

    const currentAccountModel = await this.prismaService.accounts.findFirst({
      where: {
        secureId: { equals: user.activeAccount.accountSecureId },
      },
    });
    if (!currentAccountModel) {
      throw new NotFoundException('Conta não encontrada');
    }
    if (currentAccountModel.isDeleted) {
      throw new NotFoundException('Conta excluída');
    }
    if (!currentAccountModel.isActive) {
      throw new NotFoundException('Conta inativa');
    }

    const attendantUserModel = await this.prismaService.users.findFirst({
      where: {
        secureId: { equals: params.secureId },
      },
    });
    if (!attendantUserModel) {
      throw new NotFoundException('Atendente não encontrado');
    }
    if (attendantUserModel.isDeleted) {
      throw new NotFoundException('Atendente excluído');
    }
    if (!attendantUserModel.isActive) {
      throw new NotFoundException('Atendente inativo');
    }

    const attendantAccountModel =
      await this.prismaService.usersAccounts.findFirst({
        where: {
          userId: { equals: attendantUserModel.id },
          accountId: { equals: currentAccountModel.id },
        },
      });
    if (!attendantAccountModel) {
      throw new NotFoundException('Atendente não pertence a conta do cliente');
    }
    if (attendantAccountModel.isDeleted) {
      throw new NotFoundException('Atendente já excluído dessa conta.');
    }
    if (!attendantAccountModel.isActive) {
      throw new NotFoundException('Atendente inativo');
    }

    return true;
  }
}
