import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { SyncAccountsService } from './sync-accounts.service';

@Command({
  name: 'sync-accounts',
  description:
    'Gera as accounts do sistema com base no arquivo dentro da pasta constants. Esse commando faz o cadastro caso não exista e atualiza caso exista a account. Para fazer isso ele faz com base no CompanyName.',
})
@Injectable()
export class SyncAccountsCommand extends CommandRunner {
  prisma: PrismaClient;
  constructor(private readonly syncAccounts: SyncAccountsService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    this.syncAccounts
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
