import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

import { IJwtStrategyEnvs } from './jwt-strategy.contract';

export default registerAs('jtw-strategy-envs', () => {
  const values: IJwtStrategyEnvs = {
    secret: process.env.JWT_SECRET,
  };

  const schema = Joi.object<IJwtStrategyEnvs>({
    secret: Joi.string().required().messages({
      'any.required': 'ENV: JWT_SECRET is required',
    }),
  });

  const { error } = schema.validate(values, { abortEarly: false });

  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
        ${error.message}`,
    );
  }

  return values;
});
