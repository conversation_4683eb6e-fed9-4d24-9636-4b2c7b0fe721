/*
  Warnings:

  - You are about to drop the column `chat_id` on the `chat_bots` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE `chat_bots` DROP FOREIGN KEY `chat_bots_chat_id_fkey`;

-- DropIndex
DROP INDEX `chat_bots_chat_id_key` ON `chat_bots`;

-- AlterTable
ALTER TABLE `chat_bots` DROP COLUMN `chat_id`,
    ADD COLUMN `is_ai` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `token_usage` INTEGER NULL;

-- AlterTable
ALTER TABLE `chats` ADD COLUMN `chatbot_id` INTEGER NULL;

-- CreateTable
CREATE TABLE `knowledge_base` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `account_id` INTEGER NOT NULL,
    `collection_name` VARCHAR(255) NOT NULL,
    `chunk_size` INTEGER NOT NULL,
    `chunk_overlap` INTEGER NOT NULL,
    `upload_id` INTEGER NULL,
    `chatbot_id` INTEGER NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `knowledge_base_id_key`(`id`),
    UNIQUE INDEX `knowledge_base_secure_id_key`(`secure_id`),
    UNIQUE INDEX `knowledge_base_upload_id_key`(`upload_id`),
    UNIQUE INDEX `knowledge_base_chatbot_id_key`(`chatbot_id`),
    INDEX `knowledge_base_id_secure_id_account_id_idx`(`id`, `secure_id`, `account_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `chats` ADD CONSTRAINT `chats_chatbot_id_fkey` FOREIGN KEY (`chatbot_id`) REFERENCES `chat_bots`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base` ADD CONSTRAINT `knowledge_base_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base` ADD CONSTRAINT `knowledge_base_upload_id_fkey` FOREIGN KEY (`upload_id`) REFERENCES `uploads`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base` ADD CONSTRAINT `knowledge_base_chatbot_id_fkey` FOREIGN KEY (`chatbot_id`) REFERENCES `chat_bots`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
