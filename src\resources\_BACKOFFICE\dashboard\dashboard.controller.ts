import { Controller, Get, Query, UseGuards } from '@nestjs/common';

import { BackofficeDashboardService } from './dashboard.service';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';

import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';

import { ApiBearerAuth } from '@nestjs/swagger';
import { BackofficeDashboardSwagger } from './swagger/dashboard.swagger.decorator';

@ApiBearerAuth()
@Controller('dashboard')
export class BackofficeDashboardController {
  constructor(private readonly dashboardService: BackofficeDashboardService) {}

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_dashboard_view', 'backoffice_view'])
  @Get()
  @BackofficeDashboardSwagger.FindAll()
  async findAll(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return await this.dashboardService.findAll(startDate, endDate);
  }
}
