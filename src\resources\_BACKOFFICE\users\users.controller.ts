import {
  Controller,
  Get,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  Res,
} from '@nestjs/common';
import { BackofficeUsersService } from './users.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { GetAllUsersOutputDto } from './dto/get-all-users-output.dto';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import {
  BackofficeUsersNameAndEmailSearchDecorator,
  IBackofficeUsersNameAndEmailSearchDecorator,
} from './decorators/find-all.decorator';
import { UserSecureIdExtractorDecorator } from 'src/@shared/decorators/token/user-secure-id-extractor.decorator';
import { BackofficeUsersUpdateCustomGuard } from './guards/update.guard';
import { CheckEmptyBodyGuard } from 'src/@shared/guards/body/body.guard';
import { Response } from 'express';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { BackofficeUsersSwagger } from './decorators/swagger.decorator';
import { ApiBearerAuth } from '@nestjs/swagger';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';

@ApiBearerAuth()
@Controller('users')
export class BackofficeUsersController {
  constructor(private readonly usersService: BackofficeUsersService) {}

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_user_view', 'backoffice_view'])
  @Get()
  @BackofficeUsersSwagger.FindAll()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @BackofficeUsersNameAndEmailSearchDecorator()
    searchQuery: IBackofficeUsersNameAndEmailSearchDecorator,
    @UserSecureIdExtractorDecorator() userSecureId: string,
  ): Promise<IWithPagination<GetAllUsersOutputDto>> {
    return await this.usersService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      searchQuery,
      userSecureId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_user_view', 'backoffice_view'])
  @Get(':secureId')
  @BackofficeUsersSwagger.FindOne()
  async findOne(@Param('secureId') secureId: string) {
    return await this.usersService.findOne(secureId);
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
    BackofficeUsersUpdateCustomGuard,
    new CheckEmptyBodyGuard(
      'O corpo da requisição não pode estar vazio. Você deve enviar ao menos uam propriedade para atualizar um usuário.',
    ),
  )
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_user_edit', 'backoffice_edit'])
  @Put(':secureId')
  @BackofficeUsersSwagger.Update()
  async update(
    @Param('secureId') secureId: string,
    @Body() updateUserDto: UpdateUserDto,
    @Res() response: Response,
    @UserIdExtractorDecorator() userId: number,
    @AccountIdExtractor() accountId: number,
  ) {
    await this.usersService.update(secureId, updateUserDto, accountId, userId);

    response.status(204).send();
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Roles(['BACKOFFICE'])
  @Permissions(['backoffice_user_delete', 'backoffice_delete'])
  @Delete(':secureId')
  @BackofficeUsersSwagger.Delete()
  async remove(@Param('secureId') secureId: string) {
    return await this.usersService.remove(secureId);
  }
}
