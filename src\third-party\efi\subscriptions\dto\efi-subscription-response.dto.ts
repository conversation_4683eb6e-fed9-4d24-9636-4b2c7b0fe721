import { ChargeStatus } from "./efi-subscription-input.dto";

export class EFISubscriptionResponseDto {
  code: number;
  data: {
    subscription_id: number;
    status: 'new' | 'active' | 'new_charge' | 'canceled' | 'expired';
    plan: {
      id: number;
      interval: number;
      repeats: number | null;
    };
    charge: {
      id: number;
      status: ChargeStatus;
      parcel: number;
      total: number;
    };
    first_execution: string;
    total: number;
    payment: 'credit_card' | 'banking_billet';
  };
}
