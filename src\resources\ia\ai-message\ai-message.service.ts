import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import ProcessMessageInputDTO from './dto/process-message-input.dto';
import { MessageChainService } from 'src/third-party/langchain/message-chain/message-chain.service';
import { Injectable } from '@nestjs/common';
import { ProcessMessageOutputDTO } from './dto/process-message-output.dto';
import { LeadCaptureGraphService } from '../lead-capture-graph/lead-capture-message.service';

@Injectable()
export class AIMessageService {
  constructor(
    private readonly leadCaptureGraphService: LeadCaptureGraphService,
  ) {}

  async processMessage({
    chatSessionSecureId,
    message,
  }: ProcessMessageInputDTO): Promise<ProcessMessageOutputDTO> {
    try {
      // Process message through the LangGraph flow
      return await this.leadCaptureGraphService.processMessage({
        chatSessionId: chatSessionSecureId,
        message,
      });
    } catch (e) {
      console.error(
        `Error processing message for session ${chatSessionSecureId}:`,
        e,
      );
      throw new Error('Error processing message');
    }
  }
}
