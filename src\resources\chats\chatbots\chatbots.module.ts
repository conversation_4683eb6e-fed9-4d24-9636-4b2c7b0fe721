import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ChatbotsService } from './chatbots.service';
import { ChatbotsController } from './chatbots.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { KnowledgeBaseModule } from '../knowledge-base/knowledge-base.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { RAGModule } from 'src/third-party/langchain/rag/rag.module';
import { MessageChainModule } from 'src/third-party/langchain/message-chain/message-chain.module';
import { ExtractorInfosModule } from 'src/third-party/langchain/extractor-infos/extractor-infos.module';
import { AnalyzeSentimentModule } from 'src/third-party/langchain/analyze-sentiment/analyze-sentiment.module';
import { PromptBuilderModule } from 'src/third-party/langchain/prompt-builder/prompt-builder.module';

@Module({
  imports: [
    PrismaModule,
    RAGModule,
    PromptBuilderModule,
    MessageChainModule,
    ExtractorInfosModule,
    AnalyzeSentimentModule,
  ],
  controllers: [ChatbotsController],
  providers: [ChatbotsService],
})
export class ChatbotsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('chatbots');
  }
}
