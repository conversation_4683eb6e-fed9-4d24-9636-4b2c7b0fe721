import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';

@Injectable()
export class CheckEmptyBodyGuard implements CanActivate {
  message: string;

  constructor(message?: string) {
    this.message = message || 'O corpo da requisição não pode estar vazio';
  }

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const body = request.body;

    if (Object.keys(body).length === 0) {
      throw new BadRequestException(this.message);
    }

    return true;
  }
}
