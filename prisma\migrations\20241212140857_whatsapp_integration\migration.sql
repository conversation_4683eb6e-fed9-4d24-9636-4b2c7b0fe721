-- CreateTable
CREATE TABLE `whatsapp_integration` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `account_id` INTEGER NOT NULL,
    `phone_number` VARCHAR(20) NOT NULL,
    `instance_name` VARCHAR(36) NOT NULL,
    `instance_id` VARCHAR(50) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `whatsapp_integration_id_key`(`id`),
    UNIQUE INDEX `whatsapp_integration_secure_id_key`(`secure_id`),
    UNIQUE INDEX `whatsapp_integration_phone_number_key`(`phone_number`),
    UNIQUE INDEX `whatsapp_integration_instance_name_key`(`instance_name`),
    UNIQUE INDEX `whatsapp_integration_instance_id_key`(`instance_id`),
    INDEX `whatsapp_integration_id_secure_id_idx`(`id`, `secure_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `whatsapp_integration` ADD CONSTRAINT `whatsapp_integration_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
