// export type LeadCaptureJson = {
//   isSendCaptureMessage: boolean;
//   collectCPF: boolean;
//   collectEmail: boolean;
//   collectName: boolean;
//   collectPhone: boolean;
// };

export type LeadCaptureJson = {

  collectCPF: boolean;
  collectEmail: boolean;
  collectName: boolean;
  collectPhone: boolean;
};

export const DefaultLeadCaptureJson: LeadCaptureJson = {
  collectCPF: false,
  collectEmail: false,
  collectName: false,
  collectPhone: false,
};
