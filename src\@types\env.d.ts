declare namespace NodeJS {
  interface ProcessEnv {
    // SERVER
    SERVER_PORT: string;
    SERVER_BASE_URL: string;

    // DaTABASE
    DATABASE_URL: string;

    // SECURITY
    HASH_ROUNDS: string;
    JWT_SECRET: string;
    JWT_EXPIRES_IN: string;

    // EFI SECRETS
    EFI_CLIENT_ID: string;
    EFI_CLIENT_SECRET: string;
    // EFI ENDPOINTS
    EFI_BASE_URL: string;
    EFI_AUTHORIZE_EP: string;
    EFI_PLANS_EP: string;
    // EFI_PLAN_EP: string;

    // EVO SECRETS
    EVO_API_KEY: string;

    // EVO ENDPOINTS
    EVO_BASE_URL: string;
  }
}
