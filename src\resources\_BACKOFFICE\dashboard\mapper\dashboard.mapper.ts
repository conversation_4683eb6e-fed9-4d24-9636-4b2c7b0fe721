import { FormatMoney } from 'src/@shared/helpers/money/format-money';
import { FindAllBackofficeDashboardOutputDto } from '../dto/find-all-dashboard-output.dto';
import { BackofficeDashboardVO } from '../value-object/dashboard.vo';

export class BackofficeDashboardMapper {
  static fromDashboardVOToDashboardOutputDto(
    vo: BackofficeDashboardVO,
  ): FindAllBackofficeDashboardOutputDto {
    return {
      accounts: {
        quantity: Number(vo.accountsQuantity),
      },

      chats: {
        quantity: Number(vo.chatsQuantity),
      },

      attendants: {
        quantity: Number(vo.attendantsQuantity),
      },

      transactions: {
        waitingAmount: {
          rawValue: vo?.transactionAmount?.totalWaiting || 0,
          formattedValue: FormatMoney.fromModelToOutput(
            String(
              vo?.transactionAmount?.totalWaiting
                ? vo.transactionAmount.totalWaiting
                : 0,
            ),
          ),
        },

        paidAmount: {
          rawValue: vo?.transactionAmount?.totalPaid || 0,
          formattedValue: FormatMoney.fromModelToOutput(
            String(
              vo?.transactionAmount?.totalPaid
                ? vo.transactionAmount.totalPaid
                : 0,
            ),
          ),
        },

        unpaidAmount: {
          rawValue: vo?.transactionAmount?.totalUnpaid || 0,
          formattedValue: FormatMoney.fromModelToOutput(
            String(
              vo?.transactionAmount?.totalUnpaid
                ? vo.transactionAmount.totalUnpaid
                : 0,
            ),
          ),
        },

        refundedAmount: {
          rawValue: vo?.transactionAmount?.totalRefunded || 0,
          formattedValue: FormatMoney.fromModelToOutput(
            String(
              vo?.transactionAmount?.totalRefunded
                ? vo.transactionAmount.totalRefunded
                : 0,
            ),
          ),
        },
      },

      subscriptions: {
        trials: Number(vo.subscriptions.trials),
        paid: Number(vo.subscriptions.paid),
        free: Number(vo.subscriptions.free),
        canceled: Number(vo.subscriptions.canceled),
      },
    };
  }
}
