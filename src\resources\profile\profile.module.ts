import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ProfileService } from './profile.service';
import { ProfileController } from './profile.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { HashModule } from '../auth/jwt/hash/hash.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule, HashModule],
  controllers: [ProfileController],
  providers: [ProfileService],
})
export class ProfileModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('profile');
  }
}
