import { Modu<PERSON> } from '@nestjs/common';
import { BackofficeDashboardModule } from './dashboard/dashboard.module';
import { RouterModule } from '@nestjs/core';
import { PlansModule } from './plans/plans.module';
import { BackofficeUsersModule } from './users/users.module';
import { AccountsModule } from './accounts/accounts.module';
import { ChatbotsModule } from './chatbots/chatbots.module';

@Module({
  imports: [
    BackofficeDashboardModule,
    PlansModule,
    BackofficeUsersModule,
    AccountsModule,
    ChatbotsModule,

    RouterModule.register([
      { path: 'backoffice', module: BackofficeDashboardModule },
      { path: 'backoffice', module: PlansModule },
      { path: 'backoffice', module: BackofficeUsersModule },
      { path: 'backoffice', module: ChatbotsModule },
      // { path: 'backoffice', module: AccountsModule },
    ]),
  ],
})
export class BackofficeModule {}
