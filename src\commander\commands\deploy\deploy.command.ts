import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';

import { SyncRoleRecordsService } from '../roles/sync-records/sync-role-records.service';
import { SyncPermissionRecordsService } from '../permissions/sync-records/sync-permission-records.service';
import { SyncAccountsService } from '../accounts/sync-records/sync-accounts.service';
import { SyncUsersRecordService } from '../users/sync-records/sync-users.service';

@Command({
  name: 'deploy',
  description: 'Faz a sincronização das tabelas necessárias para deploy',
})
export class DeployCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(
    private readonly syncRoles: SyncRoleRecordsService,
    private readonly syncPermissions: SyncPermissionRecordsService,
    private readonly syncAccounts: SyncAccountsService,
    private readonly syncUsers: SyncUsersRecordService,
  ) {
    super();
  }

  async run() {
    try {
      this.prisma = new PrismaClient();
      console.log('\x1b[34m', `Sincronizando em modo de deploy:\n`);

      await this.syncRoles.execute(this.prisma);
      await this.syncPermissions.execute(this.prisma);
      await this.syncAccounts.execute(this.prisma);

      await this.syncUsers.execute(this.prisma);

      await this.prisma.$disconnect();
    } catch (error) {
      console.error(
        '\x1b[31m',
        'Erro ao sincronizar tabelas para deploy:',
        error,
      );
    }
  }
}
