import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { AccountTransactionDecoratorOutput } from './account-transaction.contract';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { AccountTransactionsFactory } from './factory/account-transaction.factory';
import { AccountTransactionOutputDto } from './dto/create-transaction.dto';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { AccountTransactionMapper } from './mapper/account-transaction.mapper';

@Injectable()
export class AccountTransactionsService {
  constructor(private readonly prismaService: PrismaService) {}

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    accountTransactionQuery: AccountTransactionDecoratorOutput,
  ): Promise<IWithPagination<AccountTransactionOutputDto>> {
    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,

      status: accountTransactionQuery.transactionStatus,

      accountId: accountTransactionQuery.accountId,
      subscriptionId: accountTransactionQuery.subscriptionId,
    };

    const paginationHelper = new PaginationHelper<'Transactions'>({
      modelDelegate: this.prismaService.transactions,
      paginationQuery,
      whereClause: whereClause as never,
    });

    const transactionsModel = await this.prismaService.transactions.findMany({
      take: paginationHelper.take,
      skip: paginationHelper.skip,

      where: {
        ...whereClause,
      },

      include: {
        subscription: true,

        account: {
          select: {
            secureId: true,
            companyName: true,
            isActive: true,
            isDeleted: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    const meta = await paginationHelper.getPaginationMeta();
    const data = !transactionsModel
      ? ([] as AccountTransactionOutputDto[])
      : AccountTransactionsFactory.createAccountTransactionOutputDtoFromAccountTransactionWithAccountAndSubscriptionBatch(
          transactionsModel,
        );

    return {
      meta,
      data,
    };
  }

  async findOne(secureId: string): Promise<AccountTransactionOutputDto> {
    const transactionModel = await this.prismaService.transactions.findFirst({
      where: {
        secureId: { equals: secureId },
      },

      include: {
        subscription: true,

        account: {
          select: {
            secureId: true,
            companyName: true,
            isActive: true,
            isDeleted: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });
    if (!transactionModel) {
      throw new NotFoundException('Transaction não encontrada');
    }

    return AccountTransactionMapper.fromAccountTransactionWithAccountAndSubscriptionToOutputDto(
      transactionModel,
    );
  }
}
