import {
  SubscriptionStatus,
  SubscriptionType,
} from 'src/@shared/types/subscriptions';

export class AccountSubscriptionOutputDto {
  secureId: string;

  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;

  plan: {
    secureId: string;

    name: string;

    isActive: boolean;

    createdAt: Date;
    updatedAt: Date;
  };

  account: {
    secureId: string;

    companyName: string;

    isActive: boolean;

    isDeleted: boolean;

    createdAt: Date;
    updatedAt: Date;
  };

  gatewaySubscriptionId: number;

  startsAt: Date;
  endsAt: Date;
  canceledAt: Date | null;

  createdAt: Date;
  updatedAt: Date;
}
