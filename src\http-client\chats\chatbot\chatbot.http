@apiBaseUrl = http://localhost:3333
@loginEndPoint = /login
@loginApi = {{apiBaseUrl}}{{loginEndPoint}}
@chatbotEndPoint = /chatbots
@chatbotApi = {{apiBaseUrl}}{{chatbotEndPoint}}
@chatsEndPoint = /chats
@chatsAPI = {{apiBaseUrl}}{{chatsEndPoint}}

### Login
# @name login
POST {{loginApi}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "3z2io23m"
}


### Find All Chatbots
#  @name get_chatbots
GET {{chatbotApi}}
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}


### Find Chatbot by ID
#  @name get_chatbot_by_id
GET {{chatbotApi}}/dda39e83-b80f-494d-85cf-a3ed0136e9d5
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

### ---- Criação do chatbot e sincronização do chatbot com o chat ---------

### Create Chatbot
#  @name post_chatbot
POST {{chatbotApi}}
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

{
	"name": "OlafBot",
	"isAI": true
}

### Sync Chatbot To Chat
#  @name sync_chatbot
PUT  {{chatsAPI}}/404630c7-5f35-4ee5-bf35-e280f68f4dac
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

{
  "chatbotSecureId": "600b8969-1440-4b88-b270-0eb597f14413"
}

### ----------------------------------------------------------------------

### Update Chatbot
#  @name put_chatbot
PUT {{chatbotApi}}/fb448d6f-c5fc-4391-904d-f97a384083f6
Content-Type: application/json
Authorization: Bearer {{login.response.body.accessToken}}

{
	"isActive": true
}
