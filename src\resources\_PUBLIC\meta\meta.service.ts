import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateMetaDto } from './dto/create-meta.dto';
import { UpdateMetaDto } from './dto/update-meta.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { FacebookGraphService } from 'src/third-party/meta/facebook-graph/facebook-graph.service';
import { v4 as uuid } from 'uuid';
import { ChatsGateway } from 'src/resources/chats/chats.gateway';
import { S3Service } from 'src/third-party/s3/s3.service';
import { OpenIAService } from 'src/third-party/openIA/openIA.service';
import { getItemBase64 } from './service/get-item-base64';
import { AIMessageService } from 'src/resources/ia/ai-message/ai-message.service';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { MessageContent } from '@langchain/core/messages';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';
import { SubscriptionSessionService } from 'src/@shared/services/subscription-session/subscription-session.service';
import { NotificationService } from 'src/resources/notification/notification.service';

@Injectable()
export class MetaService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly facebookGraphService: FacebookGraphService,
    private readonly chatGateway: ChatsGateway,
    private readonly s3Service: S3Service,
    private readonly openIAService: OpenIAService,
    private readonly aiMessageService: AIMessageService,
    private readonly subscriptionSessionService: SubscriptionSessionService,
    private readonly notificationService: NotificationService,
  ) {}

  async validateWebhookToken(
    wppIntegrationId: number,
    token: string,
    accountId: number,
  ) {
    const wppIntegrationModel =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          id: wppIntegrationId,
          accountId: accountId,
        },
        select: { webhookToken: true },
      });

    if (!wppIntegrationModel || !wppIntegrationModel.webhookToken) {
      throw new NotFoundException('Nenhuma integração ativa foi encontrada');
    }

    return wppIntegrationModel.webhookToken === token;
  }

  async reciveMessage(
    createMetaDto: CreateMetaDto,
    accountId: number,
    wppIntegrationId: number,
  ) {
    const receivedNumberId = createMetaDto?.entry[0]?.id;
    const receivedName =
      createMetaDto?.entry[0]?.changes[0]?.value.contacts[0]?.profile?.name;
    const receivedPhoneNumber =
      createMetaDto?.entry[0]?.changes[0]?.value.contacts[0]?.wa_id;
    const receivedMessage =
      createMetaDto?.entry[0]?.changes[0]?.value?.messages[0];
    const receivedMessageId = receivedMessage?.id;

    if (!receivedMessage || !receivedNumberId || !receivedPhoneNumber) {
      throw new NotFoundException('Mensagem não encontrada');
    }

    const wppIntegration =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          id: wppIntegrationId,
          accountId: accountId,
        },
        include: {
          chatbot: true,
        },
      });

    // await this.facebookGraphService.markMessageAsRead(
    //   wppIntegration.token,
    //   receivedMessageId,
    //   wppIntegration.numberId,
    // );

    const {
      chatBotName,
      isFirstInteraction,
      isAIResponder,
      isIA,
      sessionSecureId,
    } = await this.ensureSession({
      accountId: accountId,
      wppIntegrationId: wppIntegrationId,
      receivedPhoneNumber,
      receivedName,
      chatBotIsAI: wppIntegration.chatbot?.isAI,
    });

    let messageContent = receivedMessage?.text?.body;
    switch (receivedMessage.type) {
      case 'text': {
        await this.saveCustomerMessage({
          sessionSecureId: sessionSecureId,
          message: receivedMessage.text.body,
        });
        break;
      }
      case 'audio': {
        const mediaResponse = await this.facebookGraphService.getMediaUrl(
          wppIntegration.token,
          receivedMessage.audio.id,
        );

        const base64Audio = await getItemBase64(
          mediaResponse.url,
          wppIntegration.token,
        );

        messageContent = await this.processAudioMessage({
          sessionSecureId: sessionSecureId,
          base64Audio: base64Audio,
          mimetype: mediaResponse.mime_type,
        });
        break;
      }
      case 'document': {
        const mediaResponse = await this.facebookGraphService.getMediaUrl(
          wppIntegration.token,
          receivedMessage.document.id,
        );

        const base64Audio = await getItemBase64(
          mediaResponse.url,
          wppIntegration.token,
        );

        await this.processDocumentMessage({
          sessionSecureId: sessionSecureId,
          base64Document: base64Audio,
          mimetype: mediaResponse.mime_type,
          type: 'file',
        });

        return; // termina o serviço
      }
      case 'image': {
        const mediaResponse = await this.facebookGraphService.getMediaUrl(
          wppIntegration.token,
          receivedMessage.image.id,
        );

        const base64Audio = await getItemBase64(
          mediaResponse.url,
          wppIntegration.token,
        );

        await this.processDocumentMessage({
          sessionSecureId: sessionSecureId,
          base64Document: base64Audio,
          mimetype: mediaResponse.mime_type,
          type: 'image',
        });
        return;
      }
    }

    if (!(isAIResponder && isIA)) {
      return;
    }

    if (isFirstInteraction) {
      const isSendGreeting = await this.sendGreetingMessage(sessionSecureId);
      if (isSendGreeting) {
        return;
      }
    }

    const { generatedAIAnswer, attachmentMessage, customerInfoRequest } =
      await this.aiMessageService.processMessage({
        message: messageContent,
        chatSessionSecureId: sessionSecureId,
      });

    const tokenUsage = {
      inputToken: 0,
      outputToken: 0,
    };

    let aiMessage = '';
    if (generatedAIAnswer) {
      for await (const chunk of generatedAIAnswer) {
        aiMessage += chunk.content;
        if (chunk.usage_metadata) {
          tokenUsage.inputToken += chunk.usage_metadata.input_tokens;
          tokenUsage.outputToken += chunk.usage_metadata.output_tokens;
        }
      }
    }

    const customerRequestTokenUsage = {
      inputToken: 0,
      outputToken: 0,
    };

    let customerRequestMessage = '';
    if (customerInfoRequest) {
      if (customerInfoRequest instanceof IterableReadableStream) {
        for await (const chunk of customerInfoRequest) {
          customerRequestMessage += chunk.content;
          if (chunk.usage_metadata) {
            customerRequestTokenUsage.inputToken +=
              chunk.usage_metadata.input_tokens;
            customerRequestTokenUsage.outputToken +=
              chunk.usage_metadata.output_tokens;
          }
        }
      } else {
        customerRequestMessage = customerInfoRequest;
      }
    }

    await this.saveAIMessage({
      message: aiMessage,
      sessionSecureId: sessionSecureId,
      customerPhone: receivedPhoneNumber,
      accessToken: wppIntegration.token,
      wppNumberId: wppIntegration.numberId,
      inputToken: tokenUsage.inputToken,
      outputToken: tokenUsage.outputToken,
      chatBotName: aiMessage !== '' ? chatBotName : undefined,
    });

    if (attachmentMessage) {
      await this.saveAIMessage({
        message: attachmentMessage,
        sessionSecureId: sessionSecureId,
        customerPhone: receivedPhoneNumber,
        accessToken: wppIntegration.token,
        wppNumberId: wppIntegration.numberId,
        chatBotName: aiMessage !== '' ? undefined : chatBotName,
      });
    }

    if (customerRequestMessage && customerRequestMessage !== '') {
      await this.saveAIMessage({
        message: customerRequestMessage,
        sessionSecureId: sessionSecureId,
        customerPhone: receivedPhoneNumber,
        accessToken: wppIntegration.token,
        wppNumberId: wppIntegration.numberId,
        inputToken: customerRequestTokenUsage.inputToken,
        outputToken: customerRequestTokenUsage.outputToken,
        chatBotName: aiMessage !== '' ? undefined : chatBotName,
      });
    }

    await this.saveTokenUsageInDb(
      sessionSecureId,
      tokenUsage.inputToken + customerRequestTokenUsage.inputToken,
      tokenUsage.outputToken + customerRequestTokenUsage.outputToken,
    );

    return;
  }

  async ensureSession(data: {
    receivedPhoneNumber: string;
    wppIntegrationId: number;
    accountId: number;
    receivedName?: string;
    chatBotIsAI?: boolean;
  }) {
    let {
      chatBotIsAI,
      accountId,
      receivedPhoneNumber,
      wppIntegrationId,
      receivedName,
    } = data;

    const activeSession = await this.prismaService.chatSessions.findFirst({
      where: {
        whatsappId: wppIntegrationId,
        accountId: accountId,
        customerId: receivedPhoneNumber,
        isActive: true,
      },
      include: {
        chatMessages: {
          where: {
            messageDirection: 'received',
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
        attendant: true,
        whatsapp: {
          include: {
            chatbot: true,
          },
        },
      },
    });
    if (activeSession && !activeSession?.isFinalized) {
      // Reutilizar a sessão existente por padrão
      const sessionData = {
        isAIResponder: activeSession.isAIResponder,
        sessionSecureId: activeSession.secureId,
        chatBotName: activeSession.whatsapp.chatbot?.name,
        isIA: activeSession.whatsapp.chatbot?.isAI,
        isFirstInteraction: false,
      };

      // Verificar a expiração apenas se houver mensagens
      const lastMessage = activeSession?.chatMessages[0];
      if (lastMessage) {
        const lastMessageTime = new Date(lastMessage.createdAt);
        const currentTime = new Date();
        const timeDifference =
          currentTime.getTime() - lastMessageTime.getTime();
        const hoursDifference = timeDifference / (1000 * 60 * 60);

        const is24HoursPassed = hoursDifference >= 24;

        if (!is24HoursPassed) {
          return sessionData;
        }
        await this.prismaService.chatSessions.update({
          where: {
            secureId: activeSession.secureId,
          },
          data: {
            isActive: false,
          },
        });
      } else {
        return sessionData;
      }
    }

    //Roleta de atendentes
    let attendantId: number | undefined = undefined;
    if (!chatBotIsAI) {
      const attendant = await this.prismaService.usersAccounts.findMany({
        where: {
          accountId: accountId,
          isActive: true,
          isDeleted: false,
          AND: {
            OR: [{ role: { name: 'Attendant' } }, { role: { name: 'App' } }],
            participatesInRotation: true,
          },
        },
      });

      if (attendant.length > 0) {
        const randomAttendant = Math.floor(Math.random() * attendant.length);
        attendantId = attendant[randomAttendant].id;
      }
    }

    const hasRemainingIASessions =
      await this.subscriptionSessionService.hasRemainingSessions({
        accountId: accountId,
      });

    const hasRemainingFreemiumTrialDays =
      await this.subscriptionSessionService.hasRemainingFreemiumTrialDays({
        accountId: accountId,
      });

    if (!hasRemainingIASessions || !hasRemainingFreemiumTrialDays) {
      chatBotIsAI = false;
    }

    const newSession = await this.prismaService.chatSessions.create({
      data: {
        secureId: uuid(),
        customerId: receivedPhoneNumber,
        customerName: receivedName,
        isAIResponder: chatBotIsAI ? true : false,
        customerPhone: receivedPhoneNumber,
        whatsappId: wppIntegrationId,
        accountId: accountId,
        source: ChatSourceEnum.whatsappBusiness,
        attendantId: attendantId,
      },
      include: {
        attendant: true,
        whatsapp: {
          include: {
            chatbot: true,
          },
        },
      },
    });

    if (newSession.isAIResponder) {
      await this.subscriptionSessionService.decrementRemainingSession({
        accountId: accountId,
      });
    }

    return {
      isAIResponder: newSession.isAIResponder,
      sessionSecureId: newSession.secureId,
      chatBotName: newSession.whatsapp.chatbot?.name,
      isIA: newSession.whatsapp.chatbot?.isAI,
      isFirstInteraction: true,
    };
  }

  async saveCustomerMessage({
    sessionSecureId,
    message,
  }: {
    sessionSecureId: string;
    message: string;
  }) {
    try {
      const newMessage = await this.prismaService.chatMessages.create({
        data: {
          secureId: uuid(),
          chatSession: {
            connect: {
              secureId: sessionSecureId,
            },
          },
          receiveMessage: message,
          messageDirection: 'received',
          messageType: 'text',
        },
        include: {
          chatSession: {
            select: {
              id: true,
              isAIResponder: true,
              customerName: true,
              attendant: {
                select: {
                  userId: true,
                },
              },
              accountId: true,
            },
          },
        },
      });

      if (
        newMessage.chatSession.accountId &&
        newMessage.chatSession.attendant?.userId &&
        !newMessage.chatSession.isAIResponder
      ) {
        await this.notificationService.createNotification({
          title: 'Nova mensagem recebida',
          message: `Nova mensagem recebida de ${newMessage.chatSession.customerName}`,
          userId: newMessage.chatSession.attendant?.userId,
          accountId: newMessage.chatSession.accountId,
          sessionId: newMessage.chatSession.id,
        });
      }

      await this.prismaService.chatSessions.update({
        where: {
          secureId: sessionSecureId,
        },
        data: {
          isArchived: false,
        },
      });

      this.chatGateway.broadcastChatMessageUpdate({
        createdAt: newMessage.createdAt,
        receiveMessage: newMessage.receiveMessage,
        secureId: newMessage.secureId,
        sessionId: sessionSecureId,
      });

      return;
    } catch (error) {
      throw new BadRequestException(
        'Erro ao salvar mensagem do cliente: ' + error.message,
      );
    }
  }

  async processAudioMessage({
    base64Audio,
    mimetype,
    // seconds,
    sessionSecureId,
  }: {
    sessionSecureId: string;
    base64Audio: string;
    mimetype: string;
    // seconds: number;
  }) {
    try {
      // Generate a unique filename
      const fileExtension = mimetype.split('/')[1]?.split(';')[0] || 'ogg';
      const fileName = `${uuid()}.${fileExtension}`;

      // Save to S3
      const uploadId = await this.s3Service.uploadBase64(
        base64Audio,
        mimetype,
        'audio',
        fileName,
      );

      if (!uploadId) {
        throw new Error('Failed to upload audio to S3');
      }

      const newMessage = await this.prismaService.chatMessages.create({
        data: {
          secureId: uuid(),
          chatSession: {
            connect: {
              secureId: sessionSecureId,
            },
          },
          messageType: 'audio',
          messageDirection: 'received',
          upload: {
            connect: {
              secureId: uploadId,
            },
          },
        },
        include: {
          upload: {
            select: {
              urlCdn: true,
            },
          },
          chatSession: {
            select: {
              id: true,
              isAIResponder: true,
              customerName: true,
              attendant: {
                select: {
                  userId: true,
                },
              },
              accountId: true,
            },
          },
        },
      });

      // Update session status
      await this.prismaService.chatSessions.update({
        where: {
          secureId: sessionSecureId,
        },
        data: {
          isArchived: false,
        },
      });

      await this.notificationService.createNotification({
        title: 'Nova mensagem de áudio recebida',
        message: `Nova mensagem de áudio recebida de ${newMessage.chatSession.customerName}`,
        userId: newMessage.chatSession.attendant?.userId,
        accountId: newMessage.chatSession.accountId,
        sessionId: newMessage.chatSession.id,
      });

      this.chatGateway.broadcastChatMessageUpdate({
        createdAt: newMessage.createdAt,
        secureId: newMessage.secureId,
        messageDirection: 'received',
        sessionId: sessionSecureId,
        type: 'audio',
        urlFile: newMessage.upload.urlCdn,
      });

      const transcription = await this.openIAService.transcribeAudio(
        base64Audio,
        mimetype,
      );

      return transcription;
    } catch (error) {
      console.error('Error processing audio message:', error);
      throw new BadRequestException(
        'Erro ao processar mensagem de áudio: ' + error.message,
      );
    }
  }

  async processDocumentMessage({
    base64Document,
    mimetype,
    type,
    sessionSecureId,
  }: {
    sessionSecureId: string;
    type: 'file' | 'image';
    base64Document: string;
    mimetype: string;
  }) {
    try {
      const fileExtension = mimetype.split('/')[1]?.split(';')[0] || 'pdf';
      const fileName = `${uuid()}.${fileExtension}`;

      const uploadId = await this.s3Service.uploadBase64(
        base64Document,
        mimetype,
        type,
        fileName,
      );

      if (!uploadId) {
        throw new Error('Erro ao fazer upload do arquivo para o S3');
      }

      const newMessage = await this.prismaService.chatMessages.create({
        data: {
          secureId: uuid(),
          chatSession: {
            connect: {
              secureId: sessionSecureId,
            },
          },
          messageType: type,
          messageDirection: 'received',
          upload: {
            connect: {
              secureId: uploadId,
            },
          },
        },
        include: {
          upload: {
            select: {
              urlCdn: true,
            },
          },
          chatSession: {
            select: {
              id: true,
              isAIResponder: true,
              customerName: true,
              attendant: {
                select: {
                  userId: true,
                },
              },
              accountId: true,
            },
          },
        },
      });

      await this.prismaService.chatSessions.update({
        where: {
          secureId: sessionSecureId,
        },
        data: {
          isArchived: false,
        },
      });

      await this.notificationService.createNotification({
        title: `Nova mensagem de ${type} recebida`,
        message: `Nova mensagem de ${type} recebida de ${newMessage.chatSession.customerName}`,
        userId: newMessage.chatSession.attendant?.userId,
        accountId: newMessage.chatSession.accountId,
        sessionId: newMessage.chatSession.id,
      });

      this.chatGateway.broadcastChatMessageUpdate({
        createdAt: newMessage.createdAt,
        secureId: newMessage.secureId,
        messageDirection: 'received',
        sessionId: sessionSecureId,
        type: type,
        urlFile: newMessage.upload.urlCdn,
      });

      return;
    } catch (e) {
      console.error('Error processing audio message:', e);
      throw new BadRequestException(
        'Erro ao processar mensagem de áudio: ' + e.message,
      );
    }
  }

  async saveAIMessage({
    message,
    wppNumberId,
    accessToken,
    customerPhone,
    sessionSecureId,
    inputToken,
    outputToken,
    chatBotName,
  }: {
    message: string | MessageContent;
    wppNumberId: string;
    accessToken: string;
    customerPhone: string;
    sessionSecureId: string;
    inputToken?: number;
    outputToken?: number;
    chatBotName?: string;
  }) {
    const newMessage = await this.prismaService.chatMessages.create({
      data: {
        secureId: uuid(),
        chatSession: {
          connect: {
            secureId: sessionSecureId,
          },
        },
        inputToken,
        outputToken,
        sendMessage: message.toString(),
        messageDirection: 'sent',
        messageType: 'text',
      },
    });
    //envia a mensagem resposta da AI para o whatsapp do cliente

    const aiResponseMessage = message.toString().replace(/\*\*/g, '*');

    const messages = aiResponseMessage.split('\n\n'); // Split the content into separate

    let messagesWithName = [];
    if (chatBotName) {
      messagesWithName.push(`*${chatBotName}:*\n\n${messages[0]}`);
      messagesWithName = messagesWithName.concat(messages.slice(1));
    } else {
      messagesWithName.concat(messages);
    }

    for await (const message of messagesWithName) {
      await this.facebookGraphService.sendWhatsAppText(
        accessToken,
        wppNumberId,
        customerPhone,
        message,
      );
    }

    //envia a mensagem para o websocket para acompanhar a conversa
    this.chatGateway.broadcastChatMessageUpdate({
      createdAt: newMessage.createdAt,
      sendMessage: newMessage.sendMessage,
      secureId: newMessage.secureId,
      sessionId: sessionSecureId,
    });
  }

  async saveTokenUsageInDb(
    sessionSecureId: string,
    inputToken: number,
    outputToken: number,
  ) {
    const {
      whatsapp: {
        chatbot: { id },
      },
    } = await this.prismaService.chatSessions.findFirst({
      where: {
        secureId: sessionSecureId,
      },
      select: {
        whatsapp: {
          select: {
            chatbot: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!id) {
      throw new NotFoundException('Chat não encontrado');
    }

    await this.prismaService.chatBots.update({
      data: {
        inputToken: {
          increment: inputToken,
        },
        outputToken: {
          increment: outputToken,
        },
      },
      where: {
        id: id,
      },
    });
  }

  async processIncomingMetaMessage({
    createMetaDto,
    accountId,
    wppIntegrationId,
  }: {
    createMetaDto: CreateMetaDto;
    accountId: number;
    wppIntegrationId: number;
  }) {
    return this.reciveMessage(createMetaDto, accountId, wppIntegrationId);
  }

  async sendGreetingMessage(sessionSecureId: string) {
    const session = await this.prismaService.chatSessions.findFirst({
      where: {
        secureId: sessionSecureId,
      },
      include: {
        whatsapp: {
          include: {
            chatbot: true,
          },
        },
        account: true,
      },
    });

    if (!session || !session.whatsapp) {
      throw new NotFoundException('Erro ao buscar informações da sessão');
    }

    if (
      !session.whatsapp?.chatbot ||
      !session.whatsapp?.chatbot.isAI ||
      !session.whatsapp?.chatbot.greetingMessage
    ) {
      return false;
    }

    await this.saveAIMessage({
      message: session.whatsapp.chatbot.greetingMessage,
      sessionSecureId: sessionSecureId,
      customerPhone: session.customerPhone,
      accessToken: session.whatsapp.token,
      wppNumberId: session.whatsapp.numberId,
      chatBotName: session.whatsapp.chatbot.name,
    });

    return true;
  }
}
