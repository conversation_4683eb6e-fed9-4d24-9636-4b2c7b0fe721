export const swaggerBadRequest = {
  examples: {
    nameRequired: {
      summary: 'Nome não fornecido',
      value: {
        message: [
          {
            property: 'name',
            message: 'O nome é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    nameMinValue: {
      summary: 'Nome não atinge os caracteres mínimos',
      value: {
        message: [
          {
            property: 'name',
            message: 'O nome deve ter no mínimo 3 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    nameMaxValue: {
      summary: 'Nome excede os caracteres máximos',
      value: {
        message: [
          {
            property: 'name',
            message: 'O nome deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    invalidEmail: {
      summary: 'E-mail não é válido',
      value: {
        message: [
          {
            property: 'email',
            message: 'O email informado é inválido',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    passwordRequired: {
      summary: 'Senha não fornecida',
      value: {
        message: [
          {
            property: 'password',
            message: 'A senha é obrigatória',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    passwordMinValue: {
      summary: 'Senha não atinge os caracteres mínimos',
      value: {
        message: [
          {
            property: 'password',
            message: 'A senha deve ter no mínimo 6 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    passwordMaxValue: {
      summary: 'Senha excede os caracteres máximos',
      value: {
        message: [
          {
            property: 'password',
            message: 'A senha deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    companyNameRequired: {
      summary: 'Nome da empresa não fornecido',
      value: {
        message: [
          {
            property: 'companyName',
            message: 'O nome da empresa é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    companyNameMinValue: {
      summary: 'Nome da empresa não atinge os caracteres mínimos',
      value: {
        message: [
          {
            property: 'companyName',
            message: 'O nome da empresa deve ter no mínimo 3 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    companyNameMaxValue: {
      summary: 'Nome da empresa excede os caracteres máximos',
      value: {
        message: [
          {
            property: 'companyName',
            message: 'O nome da empresa deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    cpfRequired: {
      summary: 'CPF não fornecido',
      value: {
        message: [
          {
            property: 'cpf',
            message: 'O CPF é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    cpfMinValue: {
      summary: 'CPF não atinge os caracteres mínimos',
      value: {
        message: [
          {
            property: 'cpf',
            message: 'O CPF deve ter no mínimo 11 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    cpfMaxValue: {
      summary: 'CPF excede os caracteres máximos',
      value: {
        message: [
          {
            property: 'cpf',
            message: 'O CPF deve ter no máximo 14 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    cellPhoneMaxValue: {
      summary: 'Telefone excede os caracteres máximos',
      value: {
        message: [
          {
            property: 'cellPhone',
            message: 'O telefone deve ter no máximo 20 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },
  },
};

export const swaggerConflict = {
  examples: {
    conflictEmail: {
      summary: 'E-mail já cadastrado',
      value: {
        message: 'Já existe um usuário com esse e-mail cadastrado',
        error: 'Conflict',
        statusCode: 409,
      },
    },

    conflictCompanyName: {
      summary: 'Empresa já cadastrada',
      value: {
        message: 'Essa empresa já possui uma conta cadastrada',
        error: 'Conflict',
        statusCode: 409,
      },
    },

    conflictCPF: {
      summary: 'CPF já cadastrado',
      value: {
        message: 'Já existe um usuário com esse CPF',
        error: 'Conflict',
        statusCode: 409,
      },
    },
  },
};

export const conflictEmail = {
  example: {
    message: 'Já existe um usuário com esse e-mail cadastrado',
    error: 'Conflict',
    statusCode: 409,
  },
};

export const conflictCompanyName = {
  example: {
    message: 'Essa empresa já possui uma conta cadastrada',
    error: 'Conflict',
    statusCode: 409,
  },
};

export const conflictCPF = {
  example: {
    message: 'Já existe um usuário com esse CPF',
    error: 'Conflict',
    statusCode: 409,
  },
};
