import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { CreateChatbotDto } from './dto/create-chatbot.dto';
import { UpdateChatbotDto } from './dto/update-chatbot.dto';
import { ChatbotListQueryDto, ChatbotMessagesQueryDto } from './dto/chatbot-query.dto';
import { ChatbotListItemDto } from './dto/chatbot-list.output.dto';
import {
  ChatbotDetailOutputDto,
  ChatbotConfigurationDto,
  ChatbotMessageDto
} from './dto/chatbot-detail.output.dto';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';

@Injectable()
export class ChatbotsService {
  constructor(private readonly prismaService: PrismaService) {}

  create(createChatbotDto: CreateChatbotDto) {
    return 'This action adds a new chatbot';
  }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    query: ChatbotListQueryDto
  ): Promise<IWithPagination<ChatbotListItemDto>> {
    const { isAI, accountOwnerName, sortBy = 'createdAt', sortOrder = 'desc' } = query;

    // Build where clause
    const where: any = {
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      isActive: isActiveIsDeletedQuery.isActive,
    };

    if (paginationQuery.search) {
      where.name = {
        contains: paginationQuery.search,
        mode: 'insensitive',
      };
    }

    if (isAI !== undefined) {
      where.isAI = isAI;
    }

    if (accountOwnerName) {
      where.account = {
        companyName: {
          contains: accountOwnerName,
          mode: 'insensitive',
        },
      };
    }

    // Setup pagination helper
    const paginationHelper = new PaginationHelper<'ChatBots'>({
      modelDelegate: this.prismaService.chatBots,
      paginationQuery,
      whereClause: where as never,
    });

    // Build order by clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // Execute queries
    const chatbots = await this.prismaService.chatBots.findMany({
      where,
      skip: paginationHelper.skip,
      take: paginationHelper.take,
      orderBy,
      include: {
        account: {
          select: {
            secureId: true,
            companyName: true,
          },
        },
        _count: {
          select: {
            chatMessages: {
              where: {
                sendMessage: { not: null },
                isDeleted: false,
              },
            },
          },
        },
      },
    });

    // Get session counts for each chatbot
    const chatbotIds = chatbots.map(cb => cb.id);
    const sessionCountMap = new Map();

    // Count sessions from WhatsApp integrations
    for (const chatbotId of chatbotIds) {
      const whatsappSessionCount = await this.prismaService.chatSessions.count({
        where: {
          whatsapp: {
            chatbotId: chatbotId,
          },
          isDeleted: false,
        },
      });

      const webChatSessionCount = await this.prismaService.chatSessions.count({
        where: {
          chat: {
            chatbotId: chatbotId,
          },
          isDeleted: false,
        },
      });

      sessionCountMap.set(chatbotId, whatsappSessionCount + webChatSessionCount);
    }

    const chatbotItems: ChatbotListItemDto[] = chatbots.map(chatbot => ({
      secureId: chatbot.secureId,
      name: chatbot.name,
      isAI: chatbot.isAI,
      isActive: chatbot.isActive,
      account: {
        secureId: chatbot.account.secureId,
        companyName: chatbot.account.companyName,
      },
      statistics: {
        tokensInput: chatbot.inputToken || 0,
        tokensOutput: chatbot.outputToken || 0,
        messagesSent: chatbot._count.chatMessages,
        sessionsResponded: sessionCountMap.get(chatbot.id) || 0,
      },
      createdAt: chatbot.createdAt,
      updatedAt: chatbot.updatedAt,
    }));

    const meta = await paginationHelper.getPaginationMeta();

    return {
      meta,
      data: chatbotItems,
    };
  }

  async findOne(paginationQuery: PaginationQueryType, secureId: string): Promise<ChatbotDetailOutputDto> {
    const chatbot = await this.prismaService.chatBots.findUnique({
      where: { secureId, isDeleted: false },
      include: {
        account: {
          select: {
            companyName: true,
          },
        },
        _count: {
          select: {
            chatMessages: {
              where: {
                sendMessage: { not: null },
                isDeleted: false,
              },
            },
          },
        },
      },
    });

    if (!chatbot) {
      throw new NotFoundException('Chatbot not found');
    }

    // Count sessions for this chatbot
    const whatsappSessionCount = await this.prismaService.chatSessions.count({
      where: {
        whatsapp: {
          chatbotId: chatbot.id,
        },
        isDeleted: false,
      },
    });

    const webChatSessionCount = await this.prismaService.chatSessions.count({
      where: {
        chat: {
          chatbotId: chatbot.id,
        },
        isDeleted: false,
      },
    });

    const totalSessionsResponded = whatsappSessionCount + webChatSessionCount;

    // Get configuration details
    const configuration: ChatbotConfigurationDto = {
      emotionalTone: chatbot.emotionalTone,
      mood: chatbot.mood,
      responseSize: chatbot.responseSize,
      responseStyle: chatbot.responseStyle,
      temperature: chatbot.temperature || 0,
      isLeadCaptureActive: chatbot.isLeadCaptureActive,
      leadTriggerMessageLimit: chatbot.leadTriggerMessageLimit,
      leadCaptureMessage: chatbot.leadCaptureMessage,
      leadCaptureThankYouMessage: chatbot.leadCaptureThankYouMessage,
      greetingMessage: chatbot.greetingMessage,
      AIPrompt: chatbot.IAPrompt,
      leadCaptureJson: chatbot.leadCaptureJson,
    };

    // Get sent messages (messages sent by the chatbot)
    const sentMessages = await this.getChatbotMessages(
      chatbot.id,
      'sent',
      paginationQuery,
      {}
    );

    // Get received messages (messages the chatbot responded to)
    const receivedMessages = await this.getChatbotMessages(
      chatbot.id,
      'received',
      paginationQuery,
      {}
    );

    return {
      secureId: chatbot.secureId,
      name: chatbot.name,
      isAI: chatbot.isAI,
      inputTokenCount: chatbot.inputToken || 0,
      outputTokenCount: chatbot.outputToken || 0,
      accountOwnerName: chatbot.account.companyName,
      totalMessagesSent: chatbot._count.chatMessages,
      totalSessionsResponded,
      isActive: chatbot.isActive,
      createdAt: chatbot.createdAt,
      updatedAt: chatbot.updatedAt,
      configuration,
      sentMessages,
      receivedMessages,
    };
  }

  async getChatbotMessages(
    chatbotId: number,
    direction: 'sent' | 'received',
    paginationQuery: PaginationQueryType,
    query: ChatbotMessagesQueryDto
  ): Promise<IWithPagination<ChatbotMessageDto>> {
    const { startDate, endDate } = query;

    const where: any = {
      chatBotId: chatbotId,
      isDeleted: false,
    };

    if (direction === 'sent') {
      where.sendMessage = { not: null };
      where.messageDirection = 'sent';
    } else {
      where.receiveMessage = { not: null };
      where.messageDirection = 'received';
    }

    if (startDate) {
      where.createdAt = { ...where.createdAt, gte: new Date(startDate) };
    }

    if (endDate) {
      where.createdAt = { ...where.createdAt, lte: new Date(endDate) };
    }

    // Setup pagination helper
    const paginationHelper = new PaginationHelper<'ChatMessages'>({
      modelDelegate: this.prismaService.chatMessages,
      paginationQuery,
      whereClause: where as never,
    });

    const messages = await this.prismaService.chatMessages.findMany({
      where,
      skip: paginationHelper.skip,
      take: paginationHelper.take,
      orderBy: { createdAt: 'desc' },
      include: {
        chatSession: {
          select: {
            secureId: true,
            customerName: true,
            customerId: true,
          },
        },
      },
    });

    const messageItems: ChatbotMessageDto[] = messages.map(message => ({
      secureId: message.secureId,
      sendMessage: message.sendMessage,
      receiveMessage: message.receiveMessage,
      messageDirection: message.messageDirection,
      sessionSecureId: message.chatSession.secureId,
      customerName: message.chatSession.customerName,
      customerId: message.chatSession.customerId,
      inputToken: message.inputToken,
      outputToken: message.outputToken,
      createdAt: message.createdAt,
    }));

    const meta = await paginationHelper.getPaginationMeta();

    return {
      meta,
      data: messageItems,
    };
  }

  async getChatbotMessagesBySecureId(
    secureId: string,
    direction: 'sent' | 'received',
    paginationQuery: PaginationQueryType,
    query: ChatbotMessagesQueryDto
  ): Promise<IWithPagination<ChatbotMessageDto>> {
    const chatbot = await this.prismaService.chatBots.findUnique({
      where: { secureId, isDeleted: false },
      select: { id: true },
    });

    if (!chatbot) {
      throw new NotFoundException('Chatbot not found');
    }

    return this.getChatbotMessages(chatbot.id, direction, paginationQuery, query);
  }

  update(id: number, updateChatbotDto: UpdateChatbotDto) {
    return `This action updates a #${id} chatbot`;
  }

  remove(id: number) {
    return `This action removes a #${id} chatbot`;
  }
}
