import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const badRequest: ApiResponseNoStatusOptions = {
  examples: {
    isActiveType: {
      summary: 'O campo isActive recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'isActive',
            message: 'O campo isActive deve ser do tipo booleano',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    priceType: {
      summary: 'O campo price recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'price',
            message: 'O campo price deve ser do tipo string',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    descriptionType: {
      summary: 'O campo description recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'description',
            message: 'O campo description deve ser do tipo string',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    descriptionMinLength: {
      summary:
        'O campo description recebe valor com tamanho menor que o permitido',
      value: {
        message: [
          {
            property: 'description',
            message: 'O campo description deve ter no mínimo 3 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    descriptionMaxLength: {
      summary:
        'O campo description recebe valor com tamanho maior que o permitido',
      value: {
        message: [
          {
            property: 'description',
            message: 'O campo description deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    detailsType: {
      summary: 'O campo details recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'details',
            message: 'O campo details deve ser do tipo string',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    detailsMinLength: {
      summary: 'O campo details recebe valor com tamanho menor que o permitido',
      value: {
        message: [
          {
            property: 'details',
            message: 'O campo details deve ter no mínimo 3 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    detailsMaxLength: {
      summary: 'O campo details recebe valor com tamanho maior que o permitido',
      value: {
        message: [
          {
            property: 'details',
            message: 'O campo details deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    slugType: {
      summary: 'O campo slug recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'slug',
            message: 'O campo slug deve ser do tipo string',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    slugMinLength: {
      summary: 'O campo slug recebe valor com tamanho menor que o permitido',
      value: {
        message: [
          {
            property: 'slug',
            message: 'O campo slug deve ter no mínimo 3 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    slugMaxLength: {
      summary: 'O campo slug recebe valor com tamanho maior que o permitido',
      value: {
        message: [
          {
            property: 'slug',
            message: 'O campo slug deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    slugRequired: {
      summary: 'O campo slug é obrigatório',
      value: {
        message: [
          {
            property: 'slug',
            message: 'O campo slug é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    nameType: {
      summary: 'O campo name recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'name',
            message: 'O campo name deve ser do tipo string',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    nameMinLength: {
      summary: 'O campo name recebe valor com tamanho menor que o permitido',
      value: {
        message: [
          {
            property: 'name',
            message: 'O campo name deve ter no mínimo 3 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    nameMaxLength: {
      summary: 'O campo name recebe valor com tamanho maior que o permitido',
      value: {
        message: [
          {
            property: 'name',
            message: 'O campo name deve ter no máximo 255 caracteres',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    nameRequired: {
      summary: 'O campo name é obrigatório',
      value: {
        message: [
          {
            property: 'name',
            message: 'O campo name é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    iaMessagesLimitType: {
      summary: 'O campo iaMessagesLimit recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'iaMessagesLimit',
            message: 'O campo iaMessagesLimit deve ser do tipo number',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    iaMessagesMinValue: {
      summary: 'O campo iaMessagesLimit recebe valor menor que o permitido',
      value: {
        message: [
          {
            property: 'iaMessagesLimit',
            message: 'O campo iaMessagesLimit deve ser maior que 0',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    iaMessagesMaxValue: {
      summary: 'O campo iaMessagesLimit recebe valor maior que o permitido',
      value: {
        message: [
          {
            property: 'iaMessagesLimit',
            message: 'O campo iaMessagesLimit deve ser menor que 100000',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    iaMessageRequired: {
      summary: 'O campo iaMessagesLimit é obrigatório',
      value: {
        message: [
          {
            property: 'iaMessagesLimit',
            message: 'O campo iaMessagesLimit é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    intervalType: {
      summary: 'O campo interval recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'interval',
            message: 'O campo intervalo deve ser do tipo number',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    intervalMinValue: {
      summary: 'O campo interval recebe valor menor que o permitido',
      value: {
        message: [
          {
            property: 'interval',
            message: 'O campo interval deve ser maior que 0',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    intervalMaxValue: {
      summary: 'O campo interval recebe valor maior que o permitido',
      value: {
        message: [
          {
            property: 'interval',
            message: 'O campo interval deve ser menor que 100000',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    knowledgeBaseLimitType: {
      summary: 'O campo knowledgeBaseLimit recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'knowledgeBaseLimit',
            message: 'O campo knowledgeBaseLimit deve ser do tipo number',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    knowledgeBaseMinValue: {
      summary: 'O campo knowledgeBaseLimit recebe valor menor que o permitido',
      value: {
        message: [
          {
            property: 'knowledgeBaseLimit',
            message: 'O campo knowledgeBaseLimit deve ser maior que 0',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    knowledgeBaseMaxValue: {
      summary: 'O campo knowledgeBaseLimit recebe valor maior que o permitido',
      value: {
        message: [
          {
            property: 'knowledgeBaseLimit',
            message: 'O campo knowledgeBaseLimit deve ser menor que 100000',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    knowledgeBaseLimitRequired: {
      summary: 'O campo knowledgeBaseLimit é obrigatório',
      value: {
        message: [
          {
            property: 'knowledgeBaseLimit',
            message: 'O campo knowledgeBaseLimit é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    chatbotsLimitType: {
      summary: 'O campo chatbotsLimit recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'chatbotsLimit',
            message: 'O campo chatbotsLimit deve ser do tipo number',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    chatbotsMinValue: {
      summary: 'O campo chatbotsLimit recebe valor menor que o permitido',
      value: {
        message: [
          {
            property: 'chatbotsLimit',
            message: 'O campo chatbotsLimit deve ser maior que 0',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    chatbotsMaxValue: {
      summary: 'O campo chatbotsLimit recebe valor maior que o permitido',
      value: {
        message: [
          {
            property: 'chatbotsLimit',
            message: 'O campo chatbotsLimit deve ser menor que 100000',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    chatbotsLimitRequired: {
      summary: 'O campo chatbotsLimit é obrigatório',
      value: {
        message: [
          {
            property: 'chatbotsLimit',
            message: 'O campo chatbotsLimit é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    whatsappNumberLimitType: {
      summary: 'O campo whatsappNumberLimit recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'whatsappNumberLimit',
            message: 'O campo whatsappNumberLimit deve ser do tipo number',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    whatsappNumberMinValue: {
      summary: 'O campo whatsappNumberLimit recebe valor menor que o permitido',
      value: {
        message: [
          {
            property: 'whatsappNumberLimit',
            message: 'O campo whatsappNumberLimit deve ser maior que 0',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    whatsappNumberMaxValue: {
      summary: 'O campo whatsappNumberLimit recebe valor maior que o permitido',
      value: {
        message: [
          {
            property: 'whatsappNumberLimit',
            message: 'O campo whatsappNumberLimit deve ser menor que 100000',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    whatsappNumberLimitRequired: {
      summary: 'O campo whatsappNumberLimit é obrigatório',
      value: {
        message: [
          {
            property: 'whatsappNumberLimit',
            message: 'O campo whatsappNumberLimit é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    attendantsLimitType: {
      summary: 'O campo attendantsLimit recebe valor do tipo errado',
      value: {
        message: [
          {
            property: 'attendantsLimit',
            message: 'O campo attendantsLimit deve ser do tipo number',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    attendantsMinValue: {
      summary: 'O campo attendantsLimit recebe valor menor que o permitido',
      value: {
        message: [
          {
            property: 'attendantsLimit',
            message: 'O campo attendantsLimit deve ser maior que 0',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    attendantsMaxValue: {
      summary: 'O campo attendantsLimit recebe valor maior que o permitido',
      value: {
        message: [
          {
            property: 'attendantsLimit',
            message: 'O campo attendantsLimit deve ser menor que 100000',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },

    attendantsLimitRequired: {
      summary: 'O campo attendantsLimit é obrigatório',
      value: {
        message: [
          {
            property: 'attendantsLimit',
            message: 'O campo attendantsLimit é obrigatório',
          },
        ],
        error: 'Bad Request',
        statusCode: 400,
      },
    },
  },
};

const conflict: ApiResponseNoStatusOptions = {
  examples: {
    nameAlreadyExists: {
      summary: 'O nome do plano já existe',
      value: {
        message: 'Já existe um plano com esse nome',
        error: 'Conflict',
        statusCode: 409,
      },
    },

    slugAlreadyExists: {
      summary: 'O slug do plano já existe',
      value: {
        message: 'Já existe um plano com esse slug',
        error: 'Conflict',
        statusCode: 409,
      },
    },
  },
};

export const createPlan = {
  status: {
    badRequest,
    conflict,
  },
};
