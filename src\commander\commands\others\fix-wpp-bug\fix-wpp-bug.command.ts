import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { FixWppBugService } from './fix-wpp-bug.service';

@Command({
  name: 'fix-wpp-bug',
  description: 'Arruma a integração com os numeros',
})
@Injectable()
export class FixWppBugCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(private readonly fixWppBugService: FixWppBugService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    await this.fixWppBugService
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
