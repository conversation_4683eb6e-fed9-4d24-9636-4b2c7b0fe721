import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('dashboard');
  }
}
