import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { BackofficeUsersService } from './users.service';
import { BackofficeUsersController } from './users.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { HashModule } from 'src/resources/auth/jwt/hash/hash.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule, HashModule],
  controllers: [BackofficeUsersController],
  providers: [BackofficeUsersService],
})
export class BackofficeUsersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('backoffice/users');
  }
}
