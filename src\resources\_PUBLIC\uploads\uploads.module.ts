import { Module } from '@nestjs/common';
import { UploadsController } from './uploads.controller';
import { S3Module } from 'src/third-party/s3/s3.module';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { UploadsService } from './uploads.service';

@Module({
  imports: [S3Module, PrismaModule],
  controllers: [UploadsController],
  providers: [UploadsService],
})
export class UploadsModule {}
