import { Modu<PERSON> } from '@nestjs/common';
import { KnowledgeBaseService } from './knowledge-base.service';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { RAGModule } from 'src/third-party/langchain/rag/rag.module';
import { KnowledgeBaseListener } from './knowledge-base-event.service';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [PrismaModule, RAGModule, EventEmitterModule.forRoot()],
  controllers: [KnowledgeBaseController],
  providers: [KnowledgeBaseService, KnowledgeBaseListener],
  exports: [KnowledgeBaseService],
})
export class KnowledgeBaseModule {}
