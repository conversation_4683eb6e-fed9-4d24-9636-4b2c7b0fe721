import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';

@Injectable()
export class FixWppBugService {
  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Arrumando sessões:`);

    const sessions = await prisma.chatSessions.findMany({
      where: {
        whatsappId: null,
        source: ChatSourceEnum.whatsapp,
      },
    });
    const totalSessions = sessions.length;
    let processedSessions = 0;

    // Initial progress bar
    this.updateProgressBar(processedSessions, totalSessions);

    for (const session of sessions) {
      const wppIntegrationActive = await prisma.whatsAppIntegration.findFirst({
        where: {
          accountId: session.accountId,
        },
      });

      if (!wppIntegrationActive) {
        console.log(
          '\x1b[31m',
          `Sessão ${session.secureId} não possui integração com o WhatsApp ativa.`,
        );
        continue;
      }

      await prisma.chatSessions.update({
        where: {
          id: session.id,
        },
        data: {
          whatsappId: wppIntegrationActive.id,
        },
      });

      // Update progress after each session is processed
      processedSessions++;
      this.updateProgressBar(processedSessions, totalSessions);
    }

    console.log(
      '\n\x1b[32m',
      `Concluído! ${totalSessions} mensagens atualizadas.`,
    );
  }

  private updateProgressBar(current: number, total: number): void {
    const percentage = Math.floor((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.floor((percentage * barLength) / 100);

    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(barLength - filledLength);

    process.stdout.write(
      `\r[${filledBar}${emptyBar}] ${percentage}% (${current}/${total})`,
    );
  }
}
