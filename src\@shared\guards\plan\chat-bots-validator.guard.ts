import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
} from '@nestjs/common';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

export class CanCreateNewChatBotGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: ReceivedUserTokenPayload = request.user;

    const prismaClient = request.prismaService;

    if (user.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    if (!user.activeAccount.accountSecureId) {
      throw new BadRequestException(
        'Nenhuma Conta encontrada para esse usuário. Entre em contato com o suporte',
      );
    }

    const accountData = await prismaClient.accounts.findFirst({
      where: {
        secureId: { equals: user.activeAccount.accountSecureId },
      },
      select: {
        companyName: true,
        subscriptions: {
          where: {
            isActive: true,
            endsAt: { gte: new Date() },
          },

          select: {
            isActive: true,

            plan: {
              select: {
                chatbotsLimit: true,
              },
            },
          },
        },

        ChatBots: {
          where: {
            isDeleted: false,
            isActive: true,
          },

          select: {
            secureId: true,
          },
        },
      },
    });

    const subscriptions = accountData.subscriptions;

    if (accountData.companyName === 'PlyrChat') {
      return true; // Bypass para PlyrChat, não precisa de assinatura
    }

    if (!subscriptions || subscriptions.length < 1) {
      throw new BadRequestException('Nenhuma inscrição ativa foi encontrada!');
    }

    const activeSubscription = subscriptions.find(
      (subscription) => subscription.isActive,
    );

    const chatbotsLimit = activeSubscription.plan.chatbotsLimit;
    const chatBots = accountData.ChatBots.length;

    if (chatBots >= chatbotsLimit) {
      throw new BadRequestException(
        'Você atingiu o limite de ChatBots disponíveis para o seu plano',
      );
    }

    return true;
  }
}
