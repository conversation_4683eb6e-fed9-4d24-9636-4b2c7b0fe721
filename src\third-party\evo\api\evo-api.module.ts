import { Module } from '@nestjs/common';
import { ConfigModule, ConfigType } from '@nestjs/config';

import { EvoApi } from './evo.api';

import evoApiEnvModule from './env.config';

@Module({
  imports: [ConfigModule.forFeature(evoApiEnvModule)],
  providers: [
    {
      provide: 'EVO-API',
      useFactory: (env: ConfigType<typeof evoApiEnvModule>) => {
        return EvoApi.instance(env);
      },
      inject: [evoApiEnvModule.KEY],
    },
  ],

  exports: ['EVO-API'],
})
export class EvoApiModule {}
