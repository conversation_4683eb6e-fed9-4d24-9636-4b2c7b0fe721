import { v4 as uuidV4 } from 'uuid';

type UploadConstructorProps = {
  id?: number;
  secureId?: string;

  fileName: string;
  fileType: string;
  type: string;

  urlCdn: string;
  bucket: string;
  isPrivate: boolean;

  createdAt?: Date;
  updatedAt?: Date;
};

type UploadJson = {
  secureId: string;

  fileName: string;
  fileType: string;
  type: string;

  urlCdn: string;
  bucket: string;
  isPrivate: boolean;

  createdAt: Date;
  updatedAt: Date;
};

export class Uploads {
  id?: number;
  secureId?: string;

  fileName: string;
  fileType: string;
  type: string;

  urlCdn: string;
  bucket: string;
  isPrivate: boolean;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: UploadConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.fileName = props.fileName;
    this.fileType = props.fileType;
    this.type = props.type;

    this.urlCdn = props.urlCdn;
    this.bucket = props.bucket;
    this.isPrivate = props.isPrivate;

    this.createdAt = props?.createdAt ? props.createdAt : new Date();
    this.updatedAt = props?.updatedAt ? props.updatedAt : new Date();
  }

  static toJson(props: Uploads): UploadJson {
    return {
      secureId: props.secureId,
      fileName: props.fileName,
      fileType: props.fileType,
      type: props.type,
      urlCdn: props.urlCdn,
      bucket: props.bucket,
      isPrivate: props.isPrivate,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    };
  }
}
