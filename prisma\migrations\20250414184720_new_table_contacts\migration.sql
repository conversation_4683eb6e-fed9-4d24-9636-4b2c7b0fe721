-- CreateTable
CREATE TABLE `contacts` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `account_id` INTEGER NOT NULL,
    `name` VA<PERSON>HAR(255) NULL,
    `email` VA<PERSON>HAR(255) NULL,
    `phone` VARCHAR(255) NULL,
    `document` VARCHAR(255) NULL,
    `last_updated_session_id` INTEGER NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `contacts_id_key`(`id`),
    UNIQUE INDEX `contacts_secure_id_key`(`secure_id`),
    INDEX `contacts_account_id_email_idx`(`account_id`, `email`),
    INDEX `contacts_account_id_phone_idx`(`account_id`, `phone`),
    INDEX `contacts_account_id_document_idx`(`account_id`, `document`),
    INDEX `contacts_account_id_name_idx`(`account_id`, `name`),
    INDEX `contacts_id_secure_id_account_id_idx`(`id`, `secure_id`, `account_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `contacts` ADD CONSTRAINT `contacts_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `contacts` ADD CONSTRAINT `contacts_last_updated_session_id_fkey` FOREIGN KEY (`last_updated_session_id`) REFERENCES `chat_sessions`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
