import { Plans as PlanModel } from '@prisma/client';

import { Plan } from '../entities/plan.entity';

import { FormatMoney } from 'src/@shared/helpers/money/format-money';

export class PlanFactory {
  static convertBatchFromModelToPlan(models: PlanModel[]): Plan[] {
    return models.map((model) => {
      const planOutput = new Plan({
        ...model,
        price: FormatMoney.fromModelToOutput(model.price),
      });

      return planOutput;
    });
  }
}
