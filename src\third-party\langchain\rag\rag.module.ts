import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import storageConfig from '../env.config';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { RAGService } from './rag.service';

@Module({
  imports: [ConfigModule.forFeature(storageConfig), PrismaModule],
  providers: [RAGService],
  exports: [RAGService],
})
export class RAGModule {}
