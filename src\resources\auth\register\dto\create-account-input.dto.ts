import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsO<PERSON>al,
  MaxLength,
  MinLength,
} from 'class-validator';
import { IsCPF } from 'src/@shared/decorators/validation/cpf/cpf.decorator';

export class CreateAccountInputDto {
  @ApiProperty({
    description: 'Nome do usuário',
    required: true,
    uniqueItems: false,
    maxLength: 255,
    minLength: 3,
    default: '',
    type: String,
    example: '<PERSON>',
  })
  @MaxLength(255, { message: 'O nome deve ter no máximo 255 caracteres' })
  @MinLength(3, { message: 'O nome deve ter no mínimo 3 caracteres' })
  @IsNotEmpty({ message: 'O nome é obrigatório' })
  name: string;

  @ApiProperty({
    description: 'Email do usuário',
    required: true,
    uniqueItems: true,
    maxLength: 255,
    minLength: 3,
    default: '',
    type: String,
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'O email informado é inválido' })
  @IsNotEmpty({ message: 'O email é obrigatório' })
  email: string;

  @ApiProperty({
    description: 'Senha do usuário',
    required: true,
    uniqueItems: false,
    maxLength: 255,
    minLength: 6,
    default: '',
    type: String,
    example: 'Doyles Tavern $5000',
  })
  @MaxLength(255, { message: 'A senha deve ter no máximo 255 caracteres' })
  @MinLength(6, { message: 'A senha deve ter no mínimo 6 caracteres' })
  @IsNotEmpty({ message: 'A senha é obrigatória' })
  password: string;

  @ApiProperty({
    description: 'Nome da empresa',
    required: true,
    uniqueItems: true,
    maxLength: 255,
    minLength: 3,
    default: '',
    type: String,
    example: 'Black Water Bank',
  })
  @MaxLength(255, {
    message: 'O nome da empresa deve ter no máximo 255 caracteres',
  })
  @MinLength(3, {
    message: 'O nome da empresa deve ter no mínimo 3 caracteres',
  })
  @IsNotEmpty({ message: 'O nome da empresa é obrigatório' })
  companyName: string;

  @ApiProperty({
    description: 'CPF do usuário',
    required: true,
    uniqueItems: true,
    maxLength: 14,
    minLength: 11,
    default: '',
    type: String,
    example: '123.456.789-01',
  })
  @IsCPF({ message: 'O CPF informado é inválido' })
  @MaxLength(14, { message: 'O CPF deve ter no máximo 14 caracteres' })
  @MinLength(11, { message: 'O CPF deve ter no mínimo 11 caracteres' })
  @IsNotEmpty({ message: 'O CPF é obrigatório' })
  cpf: string;

  @ApiProperty({
    description: 'Telefone do usuário',
    required: false,
    uniqueItems: false,
    maxLength: 20,
    default: '',
    type: String,
    example: '***********',
  })
  @MaxLength(20, { message: 'O telefone deve ter no máximo 20 caracteres' })
  @IsOptional()
  cellPhone?: string;
}
