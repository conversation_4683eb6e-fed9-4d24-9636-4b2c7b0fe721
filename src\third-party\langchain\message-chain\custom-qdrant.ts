import { Document } from 'langchain/document';
import { QdrantClient } from '@qdrant/js-client-rest';
import { OpenAIEmbeddings } from '@langchain/openai';

class CustomQdrant {
  private embeddings: OpenAIEmbeddings;
  private client: QdrantClient;
  private collectionName: string;

  constructor(
    embeddings: OpenAIEmbeddings,
    url: string,
    apiKey: string,
    urlPort: number,
    collectionName: string,
  ) {
    this.embeddings = embeddings;
    this.client = new QdrantClient({
      url: url,
      apiKey: apiKey,
      port: urlPort,
    });
    this.collectionName = collectionName;
  }

  async similaritySearch(
    query: string,
    k: number,
    scoreThreshold: number = 0.5,
  ): Promise<Document[]> {
    const queryEmbedding = await this.embeddings.embedQuery(query);
    const resultsQdrant = await this.client.search(this.collectionName, {
      vector: queryEmbedding,
      limit: k,
      // score_threshold: scoreThreshold,
    });

    // convert the resultsQdrant to list of Documents with pageContent and metadata
    return resultsQdrant.map((result: any) => {
      return new Document({
        // pageContent: result.payload.page_content,
        pageContent: result.payload.content,
        metadata: result.payload.metadata,
      });
    });
  }
}

export { CustomQdrant };
