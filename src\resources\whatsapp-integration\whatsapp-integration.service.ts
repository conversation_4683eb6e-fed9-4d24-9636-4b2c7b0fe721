import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { CreateWhatsappIntegrationDTO } from './dto/create-whatsapp-integration.dto';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { WhatsAppIntegration } from './entities/whatsapp-integration.entitie';
import { v4 as uuid } from 'uuid';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { PaginationHelper } from 'src/@shared/helpers/pagination/pagination.helper';
import { ListWhatsappIntegrationOutputDto } from './dto/list-whatsapp-integration-output.dto';
import { WhatsAppIntegrationMapper } from './mapper/whatsapp-integration.mapper';
import { UpdateWhatsappIntegrationDto } from './dto/update-whatsapp-integration.dto';

@Injectable()
export class WhatsappIntegrationService {
  constructor(private readonly prismaService: PrismaService) {}

  async findAll(
    accountId: number,
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    onlyType?: 'messager' | 'business',
  ): Promise<IWithPagination<ListWhatsappIntegrationOutputDto>> {
    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,
      isDeleted: isActiveIsDeletedQuery.isDeleted,
      accountId: { equals: accountId },
      phoneNumber: {
        contains: paginationQuery?.search && paginationQuery.search,
      },
    };

    if (onlyType) {
      whereClause['isBusiness'] = onlyType === 'business' ? true : false;
    }

    const paginationHelper = new PaginationHelper({
      modelDelegate: this.prismaService.whatsAppIntegration,
      paginationQuery: paginationQuery,
      whereClause: whereClause as never,
    });

    const integrationsModel =
      await this.prismaService.whatsAppIntegration.findMany({
        take: paginationHelper.take,
        skip: paginationHelper.skip,
        where: {
          accountId: { equals: accountId },
          ...whereClause,
        },
        include: {
          chatbot: true,
        },
      });

    const meta = await paginationHelper.getPaginationMeta();
    const data = !integrationsModel
      ? ([] as ListWhatsappIntegrationOutputDto[])
      : WhatsAppIntegrationMapper.fromBatchModelToIntegrationDtos(
          integrationsModel,
        );

    return {
      meta: meta,
      data: data,
    };
  }

  async findOne(
    secureId: string,
    accountId: number,
  ): Promise<ListWhatsappIntegrationOutputDto> {
    const integrationModel =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          accountId: { equals: accountId },
          secureId: { equals: secureId },
        },
        include: {
          chatbot: true,
        },
      });
    if (!integrationModel) {
      throw new NotFoundException('Integração não encontrada');
    }

    return WhatsAppIntegrationMapper.fromModelToIntegrationDto(
      integrationModel,
    );
  }

  async create(
    createWhatsappIntegrationDto: CreateWhatsappIntegrationDTO,
    accountId: number,
  ) {
    const phoneNumberAlreadyExists =
      await this.checkIfRecordExistsByPhoneNumber(
        createWhatsappIntegrationDto.phoneNumber,
      );

    if (phoneNumberAlreadyExists) {
      throw new ConflictException('Já existe um número de WhatsApp cadastrado');
    }

    const webhookToken = Math.random().toString(36).substring(2, 15);

    const newWppIntegration =
      await this.prismaService.whatsAppIntegration.create({
        data: {
          secureId: uuid(),
          phoneNumber: createWhatsappIntegrationDto.phoneNumber,
          token: createWhatsappIntegrationDto.token,
          webhookToken: webhookToken,
          businessId: createWhatsappIntegrationDto.businessId,
          isBusiness: true,
          numberId: createWhatsappIntegrationDto.numberId,
          accountId: accountId,
        },
      });

    return {
      secureId: newWppIntegration.secureId,
      webhookToken: webhookToken,
    };
  }

  async update(
    evoSecureId: string,
    updateEvoIntegrationDto: UpdateWhatsappIntegrationDto,
    accountId: number,
  ) {
    const integrationModel =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          accountId: { equals: accountId },
          secureId: { equals: evoSecureId },
        },
      });

    if (!integrationModel) {
      throw new NotFoundException('Integração não encontrada');
    }

    if (integrationModel.isDeleted) {
      throw new ConflictException('Integração já foi excluída');
    }

    if (!integrationModel.isActive) {
      throw new ConflictException('Integração já está desativada');
    }

    const checkChatBot = await this.checkIfChatBotExists(
      updateEvoIntegrationDto.chatBotSecureId,
    );

    if (!checkChatBot) {
      await this.prismaService.whatsAppIntegration.update({
        where: {
          secureId: evoSecureId,
        },
        data: {
          chatbot: {
            disconnect: {},
          },
        },
      });

      return;
    }
    await this.prismaService.whatsAppIntegration.update({
      where: {
        secureId: evoSecureId,
      },
      data: {
        chatbot: {
          connect: {
            id: checkChatBot,
          },
        },
      },
    });

    return;
  }

  private async checkIfRecordExistsByPhoneNumber(
    phoneNumber: string,
  ): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM whatsapp_integration WHERE phone_number = ? AND is_deleted = false) AS has;`,
      phoneNumber,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  private async checkIfChatBotExists(chatBotSecureId: string) {
    if (!chatBotSecureId) {
      return null;
    }

    const chatBotModel = await this.prismaService.chatBots.findFirst({
      where: {
        secureId: { equals: chatBotSecureId },
      },
    });

    if (!chatBotModel) {
      return null;
    }

    return chatBotModel.id;
  }
}
