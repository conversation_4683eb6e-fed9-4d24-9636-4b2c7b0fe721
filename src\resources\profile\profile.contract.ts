import { Prisma } from '@prisma/client';

export type UserWithUsersAccountsAndAccountModel = Prisma.UsersGetPayload<{
  include: {
    usersAccounts: {
      include: {
        account: {
          include: {
            subscriptions: {
              include: {
                plan: true;
              };
            };
            transactions: true;
          };
        };
      };
    };
  };
  // include: {
  //   usersAccounts: {
  //     include: {
  //       account: true;
  //     };
  //   };
  // };
}>;
