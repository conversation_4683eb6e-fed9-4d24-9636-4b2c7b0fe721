import { ChatMessages as MessageModel, Prisma } from '@prisma/client';
import { ChatMessages } from '../entities/message.entity';

type MessageModelWithReply = Prisma.ChatMessagesGetPayload<{
  include: {
    replyTo: true;
  };
}>;

export class MessageFactory {
  static convertBatchFromModelToMessage(
    models: MessageModel[],
  ): ChatMessages[] {
    return models.map((model) => {
      const messageOutput = new ChatMessages({
        ...model,
      });

      return messageOutput;
    });
  }

  static convertFromModelToMessage(model: MessageModelWithReply): ChatMessages {
    const messageOutput = new ChatMessages({
      ...model,
      replyTo: {
        secureId: model.replyTo?.secureId,
        messageJid: model.replyTo?.messageJid,
        messageDirection: model.replyTo?.messageDirection,
        message:
          model.replyTo?.messageDirection === 'sent'
            ? model.replyTo?.sendMessage
            : model.replyTo?.receiveMessage,
      },
    });

    return messageOutput;
  }
}
