/*
  Warnings:

  - The values [<PERSON> View,Partner Create,Partner Edit,Partner Delete,Insighter View,Insighter Create,Insighter Edit,Insighter Delete] on the enum `permissions_name` will be removed. If these variants are still used in the database, this will fail.
  - The values [partner_view,partner_create,partner_edit,partner_delete,insighter_view,insighter_create,insighter_edit,insighter_delete] on the enum `permissions_slug` will be removed. If these variants are still used in the database, this will fail.
  - The values [partner,insighter] on the enum `permissions_group` will be removed. If these variants are still used in the database, this will fail.
  - The values [Partner,Insighter] on the enum `roles_name` will be removed. If these variants are still used in the database, this will fail.
  - The values [PARTNER,INSIGHTER] on the enum `roles_slug` will be removed. If these variants are still used in the database, this will fail.

*/
-- DropIndex
DROP INDEX `accounts_id_secure_id_idx` ON `accounts`;

-- DropIndex
DROP INDEX `accounts_permissions_id_secure_id_idx` ON `accounts_permissions`;

-- DropIndex
DROP INDEX `users_id_secure_id_name_email_cpf_idx` ON `users`;

-- DropIndex
DROP INDEX `users_accounts_id_secure_id_idx` ON `users_accounts`;

-- AlterTable
ALTER TABLE `permissions` MODIFY `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete') NOT NULL,
    MODIFY `group` ENUM('backoffice', 'app') NOT NULL;

-- AlterTable
ALTER TABLE `roles` MODIFY `name` ENUM('Master', 'Backoffice', 'App') NOT NULL,
    MODIFY `slug` ENUM('MASTER', 'BACKOFFICE', 'APP') NOT NULL;

-- CreateTable
CREATE TABLE `plans` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `slug` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `price` VARCHAR(100) NOT NULL,
    `attendants_limit` INTEGER NOT NULL,
    `whatsapp_numbers_limit` INTEGER NOT NULL,
    `chatbots_limit` INTEGER NOT NULL,
    `knowledge_base_limit` INTEGER NOT NULL,
    `ia_messages_limit` INTEGER NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `plans_id_key`(`id`),
    UNIQUE INDEX `plans_secure_id_key`(`secure_id`),
    UNIQUE INDEX `plans_name_key`(`name`),
    UNIQUE INDEX `plans_slug_key`(`slug`),
    INDEX `plans_id_secure_id_idx`(`id`, `secure_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `accounts_id_secure_id_company_name_is_active_idx` ON `accounts`(`id`, `secure_id`, `company_name`, `is_active`);

-- CreateIndex
CREATE INDEX `accounts_permissions_id_secure_id_user_account_id_idx` ON `accounts_permissions`(`id`, `secure_id`, `user_account_id`);

-- CreateIndex
CREATE INDEX `users_id_secure_id_name_email_cpf_is_active_idx` ON `users`(`id`, `secure_id`, `name`, `email`, `cpf`, `is_active`);

-- CreateIndex
CREATE INDEX `users_accounts_id_secure_id_account_id_user_id_is_active_idx` ON `users_accounts`(`id`, `secure_id`, `account_id`, `user_id`, `is_active`);
