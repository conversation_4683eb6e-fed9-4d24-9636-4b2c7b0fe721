import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';

import { AccountTransactionsService } from './account-transactions.service';

import { AccountTransactionsController } from './account-transactions.controller';

import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule],
  controllers: [AccountTransactionsController],
  providers: [AccountTransactionsService],
})
export class AccountTransactionsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('accounts-transactions');
  }
}
