import { applyDecorators } from '@nestjs/common';
import {
  ApiForbiddenResponse,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { profileSwaggerApi } from '../docs/api';
import { swaggerShared } from 'src/@shared/docs/swagger';

function FindOne() {
  return applyDecorators(
    ApiOperation(profileSwaggerApi.profileFindOne.apiOperation),

    ApiOkResponse(profileSwaggerApi.profileFindOne.apiOkResponse),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

function Update() {
  return applyDecorators(
    ApiOperation(profileSwaggerApi.profileUpdate.apiOperation),

    ApiResponse(swaggerShared.response.noContent.updated),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

function Delete() {
  return applyDecorators(
    ApiOperation(profileSwaggerApi.profileDelete.apiOperation),

    ApiResponse(swaggerShared.response.noContent.deleted),

    ApiForbiddenResponse(swaggerShared.status.forbidden),
    ApiUnauthorizedResponse(swaggerShared.status.unauthorized),
  );
}

export const ProfileSwagger = {
  FindOne,
  Update,
  Delete,
};
