import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { getFileTypeWpp } from 'src/utils/getFileTypeWpp';

@Injectable()
export class EvoMessageService {
  constructor(
    @Inject('EVO-API')
    private readonly evoApi: AxiosInstance,
  ) {}

  async sendMessage(
    number: string,
    text: string,
    instanceName: string,
    time?: number,
    messageToReply?: any,
  ) {
    const body: any = {
      number,
      text,
      delay: time || 0,
      presence: 'composing',
    };

    if (messageToReply !== undefined) {
      body.quoted = messageToReply;
    }

    try {
      const response = await this.evoApi.post(
        `/message/sendText/${instanceName}`,
        body,
      );
      return;
    } catch (e) {
      console.error('Error sending message', e);
      throw new Error('Error sending message');
    }
  }

  async sendMediaUrl(data: {
    number: string;
    message: string;
    urlOrBase64: string;
    instanceName: string;
    messageToReply?: any;
    fileName?: string;
    fileType?: string;
    mimeType?: string;
  }) {
    const {
      number,
      message,
      urlOrBase64,
      instanceName,
      messageToReply,
      fileName,
      fileType,
      mimeType,
    } = data;

    let body: any = {};
    if (urlOrBase64.startsWith('data:')) {
      let mimeType = '';
      let contentType = '';
      const dataUrlRegex = /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/;
      const match = urlOrBase64.match(dataUrlRegex);
      if (match && match[1]) {
        mimeType = match[1];
      }

      if (mimeType.startsWith('image/')) {
        contentType = 'image';
      } else if (mimeType.startsWith('application/')) {
        contentType = 'document';
      }
      const base64Content = urlOrBase64.replace(
        /^data:([A-Za-z-+/]+);base64,/,
        '',
      );

      body = {
        number: number,
        mediatype: contentType,
        mimetype: mimeType,
        fileName: data.fileName || 'Documento',
        caption: message,
        media: base64Content,
      };
    } else {
      body = {
        number: number,
        mediatype: fileType ? getFileTypeWpp(fileType) : 'document',
        mimetype: mimeType,
        fileName: fileName || 'Documento',
        caption: message,
        media: urlOrBase64,
      };
    }
    if (messageToReply !== undefined) {
      body.quoted = messageToReply;
    }
    try {
      await this.evoApi.post(`/message/sendMedia/${instanceName}`, body);
    } catch (e) {
      console.error('Error sending media to evo api', e);
      throw new Error('Error on evo integration');
    }
  }

  async sendNarratedAudio(
    number: string,
    instanceName: string,
    urlOrBase64: string,
    delay?: number,
    messageToReply?: any,
  ) {
    let base64Content = urlOrBase64;
    if (urlOrBase64.startsWith('data:')) {
      base64Content = urlOrBase64.replace(
        /^data:([A-Za-z0-9-+/]+);base64,/,
        '',
      );
    }
    const body: any = {
      number,
      audio: base64Content,
      delay: delay || 1200,
    };
    if (messageToReply !== undefined) {
      body.quoted = messageToReply;
    }
    try {
      await this.evoApi.post(
        `/message/sendWhatsAppAudio/${instanceName}`,
        body,
      );
    } catch (e) {
      console.error('Error sending narrated audio to evo api', e);
      throw new Error('Error on evo integration');
    }
  }

  async markChatRead(
    remoteJid: string,
    messageId: string,
    instanceName: string,
  ) {
    const body = {
      readMessages: [
        {
          remoteJid,
          fromMe: false,
          id: messageId,
        },
      ],
    };

    const response = await this.evoApi.post(
      `/chat/markMessageAsRead/${instanceName}`,
      body,
    );

    return;
  }
}
