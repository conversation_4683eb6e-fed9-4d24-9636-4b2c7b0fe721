type EvoIntegrationVOConstructorProps = {
  accountId: number;

  phoneNumber: string;

  instanceName: string;
  instanceId: string;
  token?: string;
  numberId?: string;
  businessId?: string;
};

export class EvoIntegrationVO {
  accountId: number;

  phoneNumber: string;

  instanceName: string;
  instanceId: string;
  token?: string;
  numberId?: string;
  businessId?: string;

  constructor(props: EvoIntegrationVOConstructorProps) {
    Object.assign(this, props);
  }
}
