import { Injectable } from '@nestjs/common';

@Injectable()
export class ErrorNotificationTemplate {
  build(error: Error, request: any, timestamp: Date): string {
    const year = new Date().getFullYear();
    const formattedDate = timestamp.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
    
    // Extract useful information from the request
    const method = request?.method || 'N/A';
    const url = request?.url || 'N/A';
    const headers = request?.headers ? JSON.stringify(request.headers, null, 2) : 'N/A';
    const body = request?.body ? JSON.stringify(request.body, null, 2) : 'N/A';
    const user = request?.user ? JSON.stringify(request.user, null, 2) : 'N/A';
    
    // Format the error stack trace
    const errorStack = error.stack || 'No stack trace available';
    const errorMessage = error.message || 'No error message available';
    const errorName = error.name || 'Unknown Error';

    return `
      <!DOCTYPE html>
      <html>
      <head>
          <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Erro na Aplicação PlyrChat</title>
          <style type="text/css">
              @import url('https://fonts.mailersend.com/css?family=Inter:400,600');
              
              .code-block {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-family: monospace;
                padding: 10px;
                white-space: pre-wrap;
                word-break: break-all;
                margin: 10px 0;
                overflow-x: auto;
              }
          </style>
      </head>
      <body style="font-family:'Inter',Arial,sans-serif;background-color:#ffffff;margin:0;padding:0;text-align:center;">
          <table width="100%" cellpadding="0" cellspacing="0" border="0" align="center" style="max-width:600px;margin:0 auto;padding:20px;">
              <tr>
                  <td align="center" style="padding:20px 0;">
                      <h1 style="color:#FD2264;font-size:24px;margin:0;">Erro Detectado na Aplicação PlyrChat</h1>
                  </td>
              </tr>
              <tr>
                  <td style="background-color:#ffffff;padding:20px;text-align:left;border-radius:8px;border:1px solid #e0e0e0;">
                      <p style="color:#4a5566;margin:10px 0;font-size:14px;line-height:24px;">
                          <strong>Data e Hora:</strong> ${formattedDate}
                      </p>
                      <p style="color:#4a5566;margin:10px 0;font-size:14px;line-height:24px;">
                          <strong>Tipo de Erro:</strong> ${errorName}
                      </p>
                      <p style="color:#4a5566;margin:10px 0;font-size:14px;line-height:24px;">
                          <strong>Mensagem de Erro:</strong> ${errorMessage}
                      </p>
                      
                      <h3 style="color:#4a5566;margin:20px 0 10px;font-size:16px;">Detalhes da Requisição:</h3>
                      <p style="color:#4a5566;margin:5px 0;font-size:14px;line-height:24px;">
                          <strong>Método:</strong> ${method}
                      </p>
                      <p style="color:#4a5566;margin:5px 0;font-size:14px;line-height:24px;">
                          <strong>URL:</strong> ${url}
                      </p>
                      
                      <h3 style="color:#4a5566;margin:20px 0 10px;font-size:16px;">Dados do Usuário:</h3>
                      <div class="code-block">${user}</div>
                      
                      <h3 style="color:#4a5566;margin:20px 0 10px;font-size:16px;">Corpo da Requisição:</h3>
                      <div class="code-block">${body}</div>
                      
                      <h3 style="color:#4a5566;margin:20px 0 10px;font-size:16px;">Headers:</h3>
                      <div class="code-block">${headers}</div>
                      
                      <h3 style="color:#4a5566;margin:20px 0 10px;font-size:16px;">Stack Trace:</h3>
                      <div class="code-block">${errorStack}</div>
                  </td>
              </tr>
              <tr>
                  <td align="center" style="padding:20px 0;color:#8492a6;font-size:12px;">
                      <p>&copy; ${year} PlyrChat. Todos os direitos reservados.</p>
                  </td>
              </tr>
          </table>
      </body>
      </html>
    `;
  }
}
