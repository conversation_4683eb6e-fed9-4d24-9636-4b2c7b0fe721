import { Module } from '@nestjs/common';

import { PlansService } from './plans.service';

import { PlansController } from './plans.controller';

import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { EfiPlansModule } from 'src/third-party/efi/plans/efi-plans.module';

@Module({
  imports: [PrismaModule, EfiPlansModule],
  controllers: [PlansController],
  providers: [PlansService],
})
export class PlansModule {}
