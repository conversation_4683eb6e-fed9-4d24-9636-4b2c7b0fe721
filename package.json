{"name": "plyrchat-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky", "pre-commit-prettier": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "pre-commit-eslint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "husky": "yarn pre-commit-prettier && yarn pre-commit-eslint", "dev": "run-script-os", "devm": "NODE_ENV=development nest start --watch", "dev:win32": "set NODE_ENV=development&& nest start --watch", "prod": "NODE_ENV=production nest start --watch", "commander": "ts-node -r tsconfig-paths/register src/commander/main.ts", "docker:build": "docker build -t plyrchat-backend-app -f Dockerfile.dev .", "docker:run": "docker run --env-file .env -p 3333:3333  --name plyrchat-backend-app --detach plyrchat-backend-app", "docker:clear": "docker stop plyrchat-backend-app && docker rmi plyrchat-backend-app"}, "dependencies": {"@aws-sdk/client-s3": "^3.705.0", "@langchain/anthropic": "^0.3.11", "@langchain/community": "^0.3.23", "@langchain/core": "^0.3.27", "@langchain/langgraph": "^0.2.38", "@langchain/openai": "^0.3.16", "@langchain/qdrant": "^0.1.1", "@langchain/textsplitters": "^0.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^11.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-socket.io": "^10.4.13", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^8.0.7", "@nestjs/websockets": "^10.4.13", "@prisma/client": "^5.22.0", "@qdrant/js-client-rest": "^1.12.0", "ai": "^4.0.27", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.7", "axios": "^1.7.9", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "html-to-text": "^9.0.5", "joi": "^17.13.3", "langchain": "^0.3.9", "nest-commander": "^3.15.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "peggy": "^4.2.0", "reflect-metadata": "^0.2.0", "run-script-os": "^1.1.6", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "ts-node": "^10.9.1", "uuid": "^11.0.2", "zod": "^3.25.25"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/html-to-text": "^9.0.4", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.6", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.22.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "resolutions": {"zod": "^3.25.25"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}