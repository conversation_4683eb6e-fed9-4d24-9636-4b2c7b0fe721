import { createAccount } from './api/create';
import { deleteAccount } from './api/delete';
import { findAllAccounts } from './api/find-all';
import { findOneAccount } from './api/find-one';
import { updateAccount } from './api/update';

export const swaggerAccounts = {
  api: {
    status: {
      create: createAccount.status,
      findAll: findAllAccounts.status,
      findOne: findOneAccount.status,
      update: updateAccount.status,
      delete: deleteAccount.status,
    },
  },

  dto: {},
};
