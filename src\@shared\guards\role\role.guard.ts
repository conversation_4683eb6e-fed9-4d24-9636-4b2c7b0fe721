import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { ReceivedUserTokenPayload } from 'src/@shared/types/token/user-token-payload';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const roles = this.reflector.get<string>('roles', context.getHandler());
    if (!roles || roles.length < 1) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token: ReceivedUserTokenPayload = request.user;
    if (token.activeAccount.roleSlug === 'MASTER') {
      return true;
    }

    // return user.roles.some((role: string) => roles.includes(role));
    return roles.includes(token.activeAccount.roleSlug);
  }
}
