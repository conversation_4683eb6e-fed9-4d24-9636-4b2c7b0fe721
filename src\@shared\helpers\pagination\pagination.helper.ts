import { PrismaClient, Prisma } from '@prisma/client';

import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { PaginationMetaOutput } from 'src/@shared/types/pagination/pagination-meta';

interface PaginationModelParams<T> {
  page: number;
  limit: number;
  where?: T;
}

export class PaginationModelHelper {
  async getPaginationModelMeta<T>(
    modelDelegate: {
      count: (args?: { where?: T }) => Promise<number>;
    },
    paginationParams: PaginationModelParams<T>,
  ): Promise<{
    totalItems: number;
    totalPages: number;
    currentPage: number;
    itemsPerPage: number;
  }> {
    const { page, limit, where } = paginationParams;

    const totalItems = await modelDelegate.count({ where });

    const totalPages = Math.ceil(totalItems / limit);

    return {
      totalItems,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
    };
  }
}

type ModelName = Prisma.ModelName;

type WhereClause<T extends ModelName> = T extends keyof PrismaClient
  ? T extends { where: any }
    ? T['where']
    : never
  : never;

type ModelDelegate<T extends ModelName> = {
  count: (args?: { where?: WhereClause<T> }) => Promise<number>;
};

type PaginationHelperConstructorProps = {
  paginationQuery: PaginationQueryType;
  modelDelegate: ModelDelegate<ModelName>;
  whereClause: WhereClause<ModelName>;
};

export class PaginationHelper<T extends ModelName> {
  private _take: number;
  private _skip: number;
  private _currentPage: number;

  private _modelDelegate: ModelDelegate<T>;
  private _whereClause: WhereClause<T>;

  constructor(props: PaginationHelperConstructorProps) {
    this._take = props.paginationQuery.limit;
    this._skip = (props.paginationQuery.page - 1) * props.paginationQuery.limit;
    this._currentPage = props.paginationQuery.page;

    this._modelDelegate = props.modelDelegate;
    this._whereClause = props.whereClause;
  }

  get take() {
    return this._take;
  }

  get skip() {
    return this._skip;
  }

  async getPaginationMeta(): Promise<PaginationMetaOutput> {
    const totalItems = await this._modelDelegate.count({
      where: this._whereClause,
    });

    const totalPages = Math.ceil(totalItems / this._take);

    return {
      totalItems,
      totalPages,
      currentPage: this._currentPage,
      itemsPerPage: this._take,
    };
  }
}
