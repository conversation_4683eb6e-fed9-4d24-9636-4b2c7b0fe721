import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

import { Permission } from 'src/resources/permissions/entities/permission.entity';

import { permissions } from 'src/constants/permissions';
import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';

@Injectable()
export class SyncPermissionRecordsService extends ISyncRecordCommand {
  entity: string = 'Permissions';

  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Sincronizando ${this.entity}:`);

    for (const permission of permissions) {
      this.logRecord(permission.name);
      await this.createRecord(permission, prisma);
    }

    console.log('\x1b[32m', `\n${this.entity} sincronizado!\n`);
  }

  logRecord(entityName: string) {
    console.log('\x1b[32m', `\t${entityName}`);
  }

  async createRecord(record: Permission, prisma: PrismaClient) {
    await prisma.permissions.upsert({
      where: { slug: record.slug },
      create: {
        secureId: record.secureId,
        name: record.name,
        slug: record.slug,
        group: record.group,
        description: record.description,
      },
      update: {
        // secureId: record.secureId, // Esse campo foi desabilitado para não alterar o secureID quando alterar os dados quando fizer um update
        name: record.name,
        slug: record.slug,
        group: record.group,
        description: record.description,
      },
    });
  }
}
