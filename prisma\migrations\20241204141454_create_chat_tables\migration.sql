-- CreateTable
CREATE TABLE `chats` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `account_id` INTEGER NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `welcome_message` TEXT NULL,
    `avatar_url` VARCHAR(255) NULL,
    `description` TEXT NULL,
    `chat_background_color` VARCHAR(255) NULL,
    `input_chat_bg_color` VARCHAR(255) NULL,
    `input_chat_text_color` VARCHAR(255) NULL,
    `input_button_color` VARCHAR(255) NULL,
    `input_placeholder` VARCHAR(255) NULL,
    `customer_message_bubble_color` VARCHAR(255) NULL,
    `customer_message_text_color` VARCHAR(255) NULL,
    `attendant_message_bubble_color` VARCHAR(255) NULL,
    `attendant_message_text_color` VARCHAR(255) NULL,
    `message_time_color` VARCHAR(255) NULL,
    `message_status_color` VARCHAR(255) NULL,
    `is_active` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT true,
    `is_deleted` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `chats_id_key`(`id`),
    UNIQUE INDEX `chats_secure_id_key`(`secure_id`),
    UNIQUE INDEX `chats_name_key`(`name`),
    INDEX `chats_id_secure_id_account_id_is_active_idx`(`id`, `secure_id`, `account_id`, `is_active`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `chat_sessions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `chat_id` INTEGER NOT NULL,
    `account_id` INTEGER NOT NULL,
    `customer_id` VARCHAR(255) NOT NULL,
    `customer_name` VARCHAR(255) NULL,
    `customer_email` VARCHAR(255) NULL,
    `customer_phone` VARCHAR(255) NULL,
    `customer_document` VARCHAR(255) NULL,
    `session_description` TEXT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `chat_sessions_id_key`(`id`),
    UNIQUE INDEX `chat_sessions_secure_id_key`(`secure_id`),
    INDEX `chat_sessions_id_secure_id_chat_id_is_active_customer_id_acc_idx`(`id`, `secure_id`, `chat_id`, `is_active`, `customer_id`, `account_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `chat_bots` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `account_id` INTEGER NOT NULL,
    `chat_id` INTEGER NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `chat_bots_id_key`(`id`),
    UNIQUE INDEX `chat_bots_secure_id_key`(`secure_id`),
    UNIQUE INDEX `chat_bots_chat_id_key`(`chat_id`),
    INDEX `chat_bots_id_secure_id_account_id_is_active_idx`(`id`, `secure_id`, `account_id`, `is_active`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `chat_messages` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `receive_message` TEXT NULL,
    `send_message` TEXT NULL,
    `chat_session_id` INTEGER NOT NULL,
    `user_account_id` INTEGER NULL,
    `chat_bot_id` INTEGER NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `chat_messages_id_key`(`id`),
    UNIQUE INDEX `chat_messages_secure_id_key`(`secure_id`),
    INDEX `chat_messages_id_secure_id_chat_session_id_chat_bot_id_is_ac_idx`(`id`, `secure_id`, `chat_session_id`, `chat_bot_id`, `is_active`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `chats` ADD CONSTRAINT `chats_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_sessions` ADD CONSTRAINT `chat_sessions_chat_id_fkey` FOREIGN KEY (`chat_id`) REFERENCES `chats`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_sessions` ADD CONSTRAINT `chat_sessions_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_bots` ADD CONSTRAINT `chat_bots_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_bots` ADD CONSTRAINT `chat_bots_chat_id_fkey` FOREIGN KEY (`chat_id`) REFERENCES `chats`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_messages` ADD CONSTRAINT `chat_messages_chat_session_id_fkey` FOREIGN KEY (`chat_session_id`) REFERENCES `chat_sessions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_messages` ADD CONSTRAINT `chat_messages_user_account_id_fkey` FOREIGN KEY (`user_account_id`) REFERENCES `users_accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_messages` ADD CONSTRAINT `chat_messages_chat_bot_id_fkey` FOREIGN KEY (`chat_bot_id`) REFERENCES `chat_bots`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
