import { PermissionSlug } from 'src/@shared/types/permissions';
import { RoleSlug } from 'src/@shared/types/role';

type UserTokenPayloadActiveAccount = {
  accountSecureId: string;
  companyName: string;
  userName: string;
  roleSlug: RoleSlug;

  viewPermissionsSlugs: PermissionSlug[];
  trialEndAt: Date | null;
  planSlug: string | null;
};

type UserTokenPayloadConstructorProps = {
  subject: string;
  email: string;

  activeAccount: {
    accountSecureId: string;
    companyName: string;
    userName: string;
    roleSlug: RoleSlug;

    trialEndAt: Date | null;
    planSlug: string | null;

    permissions: PermissionSlug[];
  };
};

export class UserTokenPayload {
  subject: string;
  email: string;

  activeAccount: UserTokenPayloadActiveAccount;

  constructor(props: UserTokenPayloadConstructorProps) {
    this.subject = props.subject;
    this.email = props.email;

    this.activeAccount = {
      accountSecureId: props.activeAccount.accountSecureId,
      companyName: props.activeAccount.companyName,
      userName: props.activeAccount.userName,
      roleSlug: props.activeAccount.roleSlug,

      trialEndAt: props.activeAccount.trialEndAt,
      planSlug: props.activeAccount.planSlug,

      viewPermissionsSlugs: props.activeAccount.permissions,
    };
  }
}
