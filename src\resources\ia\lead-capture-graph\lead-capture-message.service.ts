import { StateGraph, Annotation } from '@langchain/langgraph';
import { AIMessageChunk } from '@langchain/core/messages';
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { Inject, Injectable } from '@nestjs/common';
import {
  DefaultLeadCaptureJson,
  LeadCaptureJson,
} from 'src/resources/chats/chatbots/constants/leadCaptureJson';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { concat } from '@langchain/core/utils/stream';
import storageEnvs from './../env.config';
import { ConfigType } from '@nestjs/config';
import { CustomQdrant } from 'src/third-party/langchain/message-chain/custom-qdrant';
import { ensureCompleteJson } from 'src/utils/ensureCompleteJson';
import { MessageChainService } from 'src/third-party/langchain/message-chain/message-chain.service';
import { ExtractorInfosService } from 'src/third-party/langchain/extractor-infos/extractor-infos.service';
import { AnalyzeSentimentService } from 'src/third-party/langchain/analyze-sentiment/analyze-sentiment.service';

// Define state types for the graph
interface CustomerData {
  name?: string;
  email?: string;
  phone?: string;
  document?: string;
}

// Create annotations for the state
const LeadCaptureState = Annotation.Root({
  // Input state
  sessionSecureId: Annotation<string>,
  messageHistory: Annotation<string[]>,
  currentMessage: Annotation<string>,

  // Lead capture configuration
  leadCaptureConfig: Annotation<{
    isActive: boolean;
    triggerMessageLimit: number;
    collectName: boolean;
    collectEmail: boolean;
    collectPhone: boolean;
    collectDocument: boolean;
    leadCaptureMessage: string;
    thankYouMessage: string;
  }>,

  chatBotConfig: Annotation<{
    prompt: string;
    temperature: number;
    alternativeOpenIAKey: string;
    collectionName: string;
  }>,

  // Processing state
  customerData: Annotation<CustomerData>,
  interestLevel: Annotation<number>,
  isLeadCaptured: Annotation<boolean>,
  isLeadCaptureMessageSent: Annotation<boolean>,
  messageCount: Annotation<number>,

  dataExtracted: Annotation<boolean>,

  // Output state
  aiResponse: Annotation<IterableReadableStream<AIMessageChunk>>,
  attachmentMessage: Annotation<string | null>,
  customerInfoRequest:
    Annotation<IterableReadableStream<AIMessageChunk> | null>,
});

@Injectable()
export class LeadCaptureGraphService {
  private openAIModel: ChatOpenAI;
  private customerInfoRequestPrompts: Record<string, ChatPromptTemplate>;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly messageChainService: MessageChainService,
    private readonly extractorInfoService: ExtractorInfosService,
    private readonly analyzeSentimentService: AnalyzeSentimentService,
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
  ) {
    this.openAIModel = new ChatOpenAI({
      model: 'gpt-4.1-mini-2025-04-14',
      temperature: 0,
      openAIApiKey: this.storageConfig.openApiKey,
    });

    this.customerInfoRequestPrompts = {
      name: ChatPromptTemplate.fromTemplate(
        'Gere uma mensagem curta, eficaz e amigável para obter o nome do cliente. Evite começar com "Olá" ou qualquer saudação genérica. Retorne apenas a mensagem gerada, mais nada.',
      ),
      email: ChatPromptTemplate.fromTemplate(
        'Gere uma mensagem curta, eficaz e amigável para obter o email do cliente. Se um nome for fornecido, use-o diretamente na mensagem para torná-la mais pessoal. Caso contrário, não mencione o nome. Evite começar com "Olá" ou qualquer saudação genérica. Retorne apenas a mensagem gerada, sem placeholders. Nome (se disponível): {customerName}',
      ),
      phone: ChatPromptTemplate.fromTemplate(
        'Gere uma mensagem curta, eficaz e amigável para obter o telefone do cliente. Se um nome for fornecido, use-o diretamente na mensagem para torná-la mais pessoal. Caso contrário, não mencione o nome. Evite começar com "Olá" ou qualquer saudação genérica. Retorne apenas a mensagem gerada, sem placeholders. Nome (se disponível): {customerName}',
      ),
      document: ChatPromptTemplate.fromTemplate(
        'Gere uma mensagem curta, eficaz e amigável para obter o cpf do cliente. Se um nome for fornecido, use-o diretamente na mensagem para torná-la mais pessoal. Caso contrário, não mencione o nome. Evite começar com "Olá" ou qualquer saudação genérica. Retorne apenas a mensagem gerada, sem placeholders. Nome (se disponível): {customerName}',
      ),
    };
  }

  async buildLeadCaptureGraph() {
    const graph = new StateGraph(LeadCaptureState)

      // Define nodes

      // 1. Check if lead capture should be triggered
      .addNode('shouldCaptureLead', this.shouldCaptureLead.bind(this))

      // 2. Extract customer info from message
      .addNode('extractCustomerInfo', this.extractCustomerInfo.bind(this))

      // 3. Analyze customer interest
      .addNode('analyzeInterest', this.analyzeInterest.bind(this))

      // 4. Generate standard AI response
      .addNode('generateAIResponse', this.generateAIResponse.bind(this))

      // 5. Request specific customer info nodes
      .addNode('requestName', this.requestName.bind(this))
      .addNode('requestEmail', this.requestEmail.bind(this))
      .addNode('requestPhone', this.requestPhone.bind(this))
      .addNode('requestDocument', this.requestDocument.bind(this))

      // 6. Send thank you message
      .addNode('sendThankYou', this.sendThankYou.bind(this))

      // 7. Update customer data in database
      .addNode('updateDatabase', this.updateDatabase.bind(this))

      // Define edges with conditional routing

      // Start with checking if we should capture lead
      .addEdge('__start__', 'shouldCaptureLead')

      // If we shouldn't capture lead, go directly to AI response
      .addConditionalEdges('shouldCaptureLead', (state) => {
        if (
          !state.leadCaptureConfig.isActive ||
          state.messageCount < state.leadCaptureConfig.triggerMessageLimit ||
          state.isLeadCaptured
        ) {
          return 'generateAIResponse';
        }
        return 'extractCustomerInfo';
      })

      // After extracting info, check what was found
      .addConditionalEdges('extractCustomerInfo', (state) => {
        const { dataExtracted } = state;

        // If we extracted meaningful information, update database and send thank you
        if (dataExtracted) {
          return 'updateDatabase';
        }

        // Otherwise, analyze interest level
        return 'analyzeInterest';
      })

      // Based on interest level, either request info or just generate response
      .addConditionalEdges('analyzeInterest', (state) => {
        if (state.interestLevel >= 7 || state.isLeadCaptureMessageSent) {
          // Determine which info to request first based on config and what's missing
          const { customerData, leadCaptureConfig } = state;

          if (leadCaptureConfig.collectName && !customerData.name)
            return 'requestName';
          if (leadCaptureConfig.collectEmail && !customerData.email)
            return 'requestEmail';
          if (leadCaptureConfig.collectPhone && !customerData.phone)
            return 'requestPhone';
          if (leadCaptureConfig.collectDocument && !customerData.document)
            return 'requestDocument';
        }

        // If interest isn't high enough or we already have all needed info
        return 'generateAIResponse';
      })

      // Connect all request nodes
      .addConditionalEdges('requestName', (state) => {
        if (state.dataExtracted) {
          return '__end__';
        }
        return 'generateAIResponse';
      })
      .addConditionalEdges('requestEmail', (state) => {
        if (state.dataExtracted) {
          return '__end__';
        }
        return 'generateAIResponse';
      })
      .addConditionalEdges('requestPhone', (state) => {
        if (state.dataExtracted) {
          return '__end__';
        }
        return 'generateAIResponse';
      })
      .addConditionalEdges('requestDocument', (state) => {
        if (state.dataExtracted) {
          return '__end__';
        }
        return 'generateAIResponse';
      })

      // After updating database, send thank you
      .addConditionalEdges('updateDatabase', (state) => {
        if (state.isLeadCaptured) {
          return 'sendThankYou';
        }
        return 'analyzeInterest';
      })

      // Thank you message is followed by AI response
      .addConditionalEdges('sendThankYou', (state) => {
        if (state.dataExtracted) {
          return '__end__';
        }
        return 'generateAIResponse';
      })

      // AI response is the end of the
      .addEdge('generateAIResponse', '__end__');

    return graph.compile();
  }
  // Node implementations

  async shouldCaptureLead(state: typeof LeadCaptureState.State) {
    const session = await this.prismaService.chatSessions.findFirst({
      where: {
        secureId: { equals: state.sessionSecureId },
      },
      select: {
        customerDocument: true,
        customerEmail: true,
        customerId: true,
        customerName: true,
        customerPhone: true,
        isLeadCaptureMessageSent: true,
        isLeadCaptured: true,
        isAIResponder: true,
        chatMessages: {
          select: {
            receiveMessage: true,
          },
        },
        chat: {
          select: {
            chatbot: {
              select: {
                IAPrompt: true,
                isAI: true,
                leadCaptureJson: true,
                leadCaptureMessage: true,
                leadCaptureThankYouMessage: true,
                leadTriggerMessageLimit: true,
                temperature: true,
                isLeadCaptureActive: true,
                KnowledgeBase: {
                  select: {
                    collectionName: true,
                  },
                },
              },
            },
          },
        },
        whatsapp: {
          select: {
            chatbot: {
              select: {
                IAPrompt: true,
                isAI: true,
                leadCaptureJson: true,
                leadCaptureMessage: true,
                leadCaptureThankYouMessage: true,
                leadTriggerMessageLimit: true,
                temperature: true,
                isLeadCaptureActive: true,
                KnowledgeBase: {
                  select: {
                    collectionName: true,
                  },
                },
              },
            },
          },
        },
        account: {
          select: {
            secureId: true,
            openaiApiKey: true,
            prompt: true,
          },
        },
      },
    });

    if (!session) {
      throw new Error('Chat session not found');
    }

    // Get the appropriate chatbot info based on message source
    const chatBotInfos = session.whatsapp?.chatbot
      ? session.whatsapp?.chatbot
      : session.chat?.chatbot;

    if (!chatBotInfos) {
      throw new Error('Chatbot information not found');
    }

    const {
      chatMessages,
      account,
      customerDocument,
      customerEmail,
      customerName,
      customerPhone,
      isLeadCaptureMessageSent,
      isLeadCaptured,
    } = session;

    const chatBotLeadJson = ensureCompleteJson(
      chatBotInfos.leadCaptureJson,
      DefaultLeadCaptureJson,
    );

    const hasCustomerName = customerName?.includes('Usuário') ?? false;

    const hasCollectedEmail = chatBotLeadJson.collectEmail
      ? !!customerEmail
      : true;
    const hasCollectedName = chatBotLeadJson.collectName
      ? !!(customerName && !hasCustomerName)
      : true;
    const hasCollectedDocument = chatBotLeadJson.collectCPF
      ? !!customerDocument
      : true;
    const hasCollectedPhone = chatBotLeadJson.collectPhone
      ? !!customerPhone
      : true;

    return {
      isLeadCaptured: isLeadCaptured,
      isLeadCaptureMessageSent: isLeadCaptureMessageSent,
      messageCount: chatMessages.length,
      messageHistory: chatMessages.map((item) => item.receiveMessage),
      customerData: {
        name: hasCustomerName ? undefined : customerName,
        phone: customerPhone,
        email: customerEmail,
        document: customerDocument,
      },
      chatBotConfig: {
        prompt: account.prompt ? account.prompt : chatBotInfos.IAPrompt,
        temperature: chatBotInfos.temperature,
        collectionName: chatBotInfos.KnowledgeBase.collectionName,
        alternativeOpenIAKey: account.openaiApiKey,
      },
      leadCaptureConfig: {
        isActive: chatBotInfos.isLeadCaptureActive,
        triggerMessageLimit: chatBotInfos.leadTriggerMessageLimit,
        collectName: !hasCollectedName,
        collectEmail: !hasCollectedEmail,
        collectPhone: !hasCollectedPhone,
        collectDocument: !hasCollectedDocument,
        leadCaptureMessage: chatBotInfos.leadCaptureMessage,
        thankYouMessage: chatBotInfos.leadCaptureThankYouMessage,
      },
    };
  }

  private async extractCustomerInfo(state: typeof LeadCaptureState.State) {
    const extractedCustomerInfos =
      await this.extractorInfoService.extractorInfos(
        state.currentMessage,
        state.chatBotConfig.alternativeOpenIAKey,
      );

    const customerData = {
      ...state.customerData,
      ...extractedCustomerInfos,
    };

    const dataExtracted = !!(
      extractedCustomerInfos.email ||
      extractedCustomerInfos.phone ||
      extractedCustomerInfos.document ||
      extractedCustomerInfos.name
    );

    return { customerData, dataExtracted };
  }

  private async analyzeInterest(state: typeof LeadCaptureState.State) {
    if (state.isLeadCaptureMessageSent) {
      return {};
    }

    const interestLevel =
      await this.analyzeSentimentService.analyzeConversationSentiment(
        state.messageHistory,
        state.chatBotConfig.alternativeOpenIAKey,
      );

    return { interestLevel: interestLevel.desire };
  }

  private async generateAIResponse(state: typeof LeadCaptureState.State) {
    const { chatBotConfig, messageHistory, currentMessage } = state;

    const aiMessageResponse = await this.messageChainService.streamMessage({
      collectionName: chatBotConfig.collectionName,
      temperature: chatBotConfig.temperature,
      messagesHistory: messageHistory,
      question: currentMessage,
      alternativeApiKey: chatBotConfig.alternativeOpenIAKey,
      generatedPrompt: chatBotConfig.prompt,
    });

    return { aiResponse: aiMessageResponse };
  }

  private async requestName(state: typeof LeadCaptureState.State) {
    const { leadCaptureConfig, isLeadCaptureMessageSent } = state;

    const messages = await this.customerInfoRequestPrompts.name.invoke({});
    const stream = await this.openAIModel.stream(messages);

    if (!isLeadCaptureMessageSent) {
      await this.prismaService.chatSessions.update({
        where: {
          secureId: state.sessionSecureId,
        },
        data: {
          isLeadCaptureMessageSent: true,
        },
      });
    }

    return {
      attachmentMessage: !isLeadCaptureMessageSent
        ? leadCaptureConfig.leadCaptureMessage
        : null,
      customerInfoRequest: stream,
    };
  }

  private async requestEmail(state: typeof LeadCaptureState.State) {
    const {
      leadCaptureConfig,
      isLeadCaptureMessageSent,
      customerData: { name },
    } = state;

    const messages = await this.customerInfoRequestPrompts.email.invoke({
      customerName: name || '',
    });
    const stream = await this.openAIModel.stream(messages);

    if (!isLeadCaptureMessageSent) {
      await this.prismaService.chatSessions.update({
        where: {
          secureId: state.sessionSecureId,
        },
        data: {
          isLeadCaptureMessageSent: true,
        },
      });
    }

    return {
      attachmentMessage: !isLeadCaptureMessageSent
        ? leadCaptureConfig.leadCaptureMessage
        : null,
      customerInfoRequest: stream,
    };
  }

  private async requestPhone(state: typeof LeadCaptureState.State) {
    const {
      leadCaptureConfig,
      isLeadCaptureMessageSent,
      customerData: { name },
    } = state;

    const messages = await this.customerInfoRequestPrompts.phone.invoke({
      customerName: name || '',
    });
    const stream = await this.openAIModel.stream(messages);

    if (!isLeadCaptureMessageSent) {
      await this.prismaService.chatSessions.update({
        where: {
          secureId: state.sessionSecureId,
        },
        data: {
          isLeadCaptureMessageSent: true,
        },
      });
    }

    return {
      attachmentMessage: !isLeadCaptureMessageSent
        ? leadCaptureConfig.leadCaptureMessage
        : null,
      customerInfoRequest: stream,
    };
  }

  private async requestDocument(state: typeof LeadCaptureState.State) {
    const {
      leadCaptureConfig,
      isLeadCaptureMessageSent,
      customerData: { name },
    } = state;

    const messages = await this.customerInfoRequestPrompts.document.invoke({
      customerName: name || '',
    });
    const stream = await this.openAIModel.stream(messages);

    if (!isLeadCaptureMessageSent) {
      await this.prismaService.chatSessions.update({
        where: {
          secureId: state.sessionSecureId,
        },
        data: {
          isLeadCaptureMessageSent: true,
        },
      });
    }

    return {
      attachmentMessage: !isLeadCaptureMessageSent
        ? leadCaptureConfig.leadCaptureMessage
        : null,
      customerInfoRequest: stream,
    };
  }

  private async updateDatabase(state: typeof LeadCaptureState.State) {
    // Save extracted customer data to database
    // Only mark as completely captured if we have all the required fields
    const isLeadCaptured = this.isLeadCompletelyCaptures(
      state.customerData,
      state.leadCaptureConfig,
    );

    await this.prismaService.chatSessions.update({
      where: {
        secureId: state.sessionSecureId,
      },
      data: {
        customerName: state.customerData.name,
        customerEmail: state.customerData.email,
        customerPhone: state.customerData.phone,
        customerDocument: state.customerData.document,
        isLeadCaptureMessageSent: true,
        isLeadCaptured,
      },
    });

    return { isLeadCaptured };
  }

  private isLeadCompletelyCaptures(
    data: CustomerData,
    config: typeof LeadCaptureState.State.leadCaptureConfig,
  ) {
    return (
      (!config.collectName || !!data.name) &&
      (!config.collectEmail || !!data.email) &&
      (!config.collectPhone || !!data.phone) &&
      (!config.collectDocument || !!data.document)
    );
  }

  private async sendThankYou(state: typeof LeadCaptureState.State) {
    let thankYouMessage = state.leadCaptureConfig.thankYouMessage;

    // Replace placeholders with actual data
    if (state.customerData.name) {
      thankYouMessage = thankYouMessage.replace(
        '{{nome}}',
        state.customerData.name,
      );
    }

    return { attachmentMessage: thankYouMessage };
  }

  // Main method to process a message
  async processMessage(input: { chatSessionId: string; message: string }) {
    const graph = this.buildLeadCaptureGraph();

    const result = await (
      await graph
    ).invoke({
      sessionSecureId: input.chatSessionId,
      currentMessage: input.message,
      messageHistory: [],
      customerData: {},
      interestLevel: 0,
      isLeadCaptured: false,
      isLeadCaptureMessageSent: false,
      dataExtracted: false,
      messageCount: 0,
      leadCaptureConfig: {
        isActive: false,
        triggerMessageLimit: 0,
        collectName: false,
        collectEmail: false,
        collectPhone: false,
        collectDocument: false,
        leadCaptureMessage: '',
        thankYouMessage: '',
      },
      chatBotConfig: {
        prompt: '',
        temperature: 0,
        alternativeOpenIAKey: '',
        collectionName: '',
      },
      aiResponse: null,
      attachmentMessage: null,
      customerInfoRequest: null,
    });

    return {
      generatedAIAnswer: result.aiResponse,
      attachmentMessage: result.attachmentMessage,
      customerInfoRequest: result.customerInfoRequest,
    };
  }
}
