import {
  Prisma,
  WhatsAppIntegration as WhatsAppIntegrationModel,
} from '@prisma/client';

import { EvoIntegrationVO } from 'src/resources/evo/integration/value-object/evo-integration.vo';
import { WhatsAppIntegration } from '../entities/whatsapp-integration.entitie';
import { ******************************** } from '../dto/list-whatsapp-integration-output.dto';

type WhatsAppIntegrationModelWithChatBot =
  Prisma.WhatsAppIntegrationGetPayload<{
    include: {
      chatbot: true;
    };
  }>;

type WhatsAppIntegrationModelWithOptionalChatBot =
  Prisma.WhatsAppIntegrationGetPayload<{
    include: {
      chatbot?: true;
    };
  }>;

export class WhatsAppIntegrationMapper {
  static fromEvoIntegrationVOToModelCreation(
    evoIntegrationVO: EvoIntegrationVO,
  ): WhatsAppIntegrationModel {
    const entity = new WhatsAppIntegration({
      instanceId: evoIntegrationVO.instanceId,
      instanceName: evoIntegrationVO.instanceName,
      phoneNumber: evoIntegrationVO.phoneNumber,
      businessId: evoIntegrationVO.businessId,
      numberId: evoIntegrationVO.numberId,
      token: evoIntegrationVO.token,
      isBusiness: evoIntegrationVO.businessId ? true : false,
      isActive: false,
      isDeleted: false,
    });

    return {
      secureId: entity.secureId,

      accountId: evoIntegrationVO.accountId,

      phoneNumber: entity.phoneNumber,
      instanceId: entity.instanceId,
      instanceName: entity.instanceName,

      businessId: entity.businessId,
      numberId: entity.numberId,

      isBusiness: entity.isBusiness,
      isActive: entity.isActive,
      isDeleted: entity.isDeleted,
    } as WhatsAppIntegrationModel;
  }

  static fromBatchModelToIntegrationDtos(
    model: WhatsAppIntegrationModelWithChatBot[],
  ): ********************************[] {
    return model.map((item) => this.fromModelToIntegrationDto(item));
  }

  static fromModelToIntegrationDto(
    model: WhatsAppIntegrationModelWithOptionalChatBot,
  ): ******************************** {
    return {
      secureId: model.secureId,
      phoneNumber: model.phoneNumber,
      isBusiness: model.isBusiness,
      isActive: model.isActive,
      isDeleted: model.isDeleted,
      webhookToken: model.webhookToken,
      chatBotSecureId: model.chatbot?.secureId || null,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }
}
