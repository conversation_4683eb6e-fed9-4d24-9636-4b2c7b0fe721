import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateEvoIntegrationDto } from './dto/create-evo-integration.dto';
import { EvoInstancesService } from 'src/third-party/evo/instances/instances.service';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { CreateEvoInstance } from 'src/third-party/evo/instances/entities/create-instance.entity';
import { EvoIntegrationVO } from './value-object/evo-integration.vo';
import { WhatsAppIntegrationMapper } from 'src/resources/whatsapp-integration/mapper/whatsapp-integration.mapper';
import {
  CreateEvoIntegrationBusinessOutputDto,
  CreateEvoIntegrationOutputDto,
} from './dto/create-evo-integration-output.dto';
import { InstanceQRCode } from 'src/third-party/evo/instances/entities/instance-qrcode.entity';

@Injectable()
export class EvoIntegrationService {
  constructor(
    private readonly evoInstanceService: EvoInstancesService,
    private readonly prismaService: PrismaService,
  ) {}

  async create(
    createEvoIntegrationDto: CreateEvoIntegrationDto,
    accountId: number,
  ): Promise<
    CreateEvoIntegrationOutputDto | CreateEvoIntegrationBusinessOutputDto
  > {
    const phoneNumberAlreadyExists =
      await this.checkIfRecordExistsByPhoneNumber(
        createEvoIntegrationDto.phoneNumber,
      );
    if (phoneNumberAlreadyExists) {
      throw new ConflictException('Já existe um número de WhatsApp cadastrado');
    }

    // Gera os dados para criar a instância
    const createEvoInstance = new CreateEvoInstance({
      phoneNumber: createEvoIntegrationDto.phoneNumber,
    });

    // Cria a instância e em seguida o objeto de integração
    const evoInstance = await this.evoInstanceService.create(createEvoInstance);

    const evoIntegrationVO = new EvoIntegrationVO({
      accountId: accountId,

      instanceId: evoInstance.instance.instanceId,
      instanceName: evoInstance.instance.instanceName,
      phoneNumber: createEvoIntegrationDto.phoneNumber,
    });

    try {
      const whatsappIntegrationCreation =
        WhatsAppIntegrationMapper.fromEvoIntegrationVOToModelCreation(
          evoIntegrationVO,
        );

      await this.prismaService.$transaction(async (prisma) => {
        await prisma.whatsAppIntegration.create({
          data: whatsappIntegrationCreation,
        });
      });

      // Aqui é criado o objeto de retorno para o cliente.
      const qrCodeResponse = new CreateEvoIntegrationOutputDto();
      qrCodeResponse.qrcodeBase64 = evoInstance.qrcode.base64;
      qrCodeResponse.pairingCode = evoInstance.qrcode.pairingCode;

      return qrCodeResponse;
    } catch (error) {
      await this.evoInstanceService.logout(createEvoInstance.name);

      console.error(error);
      throw new InternalServerErrorException(
        'Erro ao criar a integração. Entre em contato com o suporte.',
      );
    }
  }

  async remove(secureId: string, accountId: number): Promise<void> {
    const integrationModel =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          accountId: { equals: accountId },
          secureId: { equals: secureId },
        },
      });
    if (!integrationModel) {
      throw new NotFoundException('Integração não encontrada');
    }

    if (integrationModel.isDeleted) {
      throw new ConflictException('Integração já foi excluída');
    }
    if (!integrationModel.isActive) {
      throw new ConflictException('Integração já está desativada');
    }

    const today = new Date();
    const formattedToday = `${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;
    const formattedTodayTime = `${today.getHours()}:${today.getMinutes() < 10 ? today.getMinutes().toString().padStart(2, '0') : today.getMinutes()}:${today.getSeconds()}`;

    const newPhone = `${integrationModel.phoneNumber} - excluído em: ${formattedToday} às ${formattedTodayTime}`;

    try {
      await this.prismaService.$transaction(async (prisma) => {
        await prisma.whatsAppIntegration.update({
          where: {
            secureId: secureId,
          },
          data: {
            isDeleted: true,
            isActive: false,
            phoneNumber: newPhone,
          },
        });

        await this.evoInstanceService.remove(integrationModel.instanceName);
      });
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException(
        'Erro ao excluir a integração. Entre em contato com o suporte.',
      );
    }
  }

  async logout(integrationSecureId: string, accountId: number): Promise<void> {
    const integrationModel =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          accountId: { equals: accountId },
          secureId: { equals: integrationSecureId },
          isActive: true,
          isDeleted: false,
        },
      });
    if (!integrationModel) {
      throw new NotFoundException('Integração não encontrada');
    }

    await this.evoInstanceService.logout(integrationModel.instanceName);

    return;
  }

  async login(
    integrationSecureId: string,
    accountId: number,
  ): Promise<InstanceQRCode> {
    const integrationModel =
      await this.prismaService.whatsAppIntegration.findFirst({
        where: {
          accountId: { equals: accountId },
          secureId: { equals: integrationSecureId },
          isDeleted: false,
        },
      });
    if (!integrationModel) {
      throw new NotFoundException('Integração não encontrada');
    }

    return await this.evoInstanceService.login(integrationModel.instanceName);
  }

  private async checkIfRecordExistsByPhoneNumber(
    phoneNumber: string,
  ): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM whatsapp_integration WHERE phone_number = ? AND is_deleted = false) AS has;`,
      phoneNumber,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }
}
