import { AccountPermissionsFromUserModel } from '../account-permissions.contract';
import { Permission } from 'src/resources/permissions/entities/permission.entity';

export class AccountPermissionsMapper {
  static fromModelToPermissionEntity(
    model: AccountPermissionsFromUserModel,
  ): Permission[] {
    return model.usersAccounts[0].accountsPermissions.map(
      (accountPermission) => {
        return new Permission({
          id: accountPermission.permission.id,
          secureId: accountPermission.permission.secureId,
          name: accountPermission.permission.name,
          slug: accountPermission.permission.slug,
          group: accountPermission.permission.group,
          description: accountPermission.permission.description,
          createdAt: accountPermission.permission.createdAt,
          updatedAt: accountPermission.permission.updatedAt,
        });
      },
    );
  }
}
