import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IShowAllDecorator } from 'src/@shared/contracts/decorators/show-all/interface';

interface SessionsParamsProps extends Headers {
  showAll?: string;
  showOnlyArchived?: string;
  showOnlyFinalized?: string;
  sessionTypeFilter?: string;
  sortOrder?: 'asc' | 'desc';
}

export const SessionsParamsDecorator = createParamDecorator(
  (data: any, ctx: ExecutionContext): IShowAllDecorator => {
    const request = ctx.switchToHttp().getRequest();
    const query: SessionsParamsProps = request.query;
    let showAll: boolean | undefined;
    let showOnlyArchived: boolean | undefined;
    let showOnlyFinalized: boolean | undefined;
    let sessionTypeFilter: string | undefined;
    let sortOrder: 'asc' | 'desc' | undefined;

    if (!query?.['showAll']) {
      showAll = undefined;
    } else {
      showAll = query['showAll'] === 'true' ? true : false;
    }

    if (!query?.['showOnlyArchived']) {
      showOnlyArchived = undefined;
    } else {
      showOnlyArchived = query['showOnlyArchived'] === 'true' ? true : false;
    }

    if (!query?.['showOnlyFinalized']) {
      showOnlyFinalized = undefined;
    } else {
      showOnlyFinalized = query['showOnlyFinalized'] === 'true' ? true : false;
    }

    if (!query?.['sessionTypeFilter']) {
      sessionTypeFilter = undefined;
    } else {
      sessionTypeFilter = query['sessionTypeFilter'];
    }

    if (!query?.['sortOrder']) {
      sortOrder = undefined;
    } else {
      sortOrder = query['sortOrder'];
    }

    return {
      showAll,
      showOnlyArchived,
      showOnlyFinalized,
      sessionTypeFilter,
      sortOrder,
    };
  },
);
