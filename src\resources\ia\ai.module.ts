import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { AIMessageService } from './ai-message/ai-message.service';
import { MessageChainModule } from 'src/third-party/langchain/message-chain/message-chain.module';
import { LeadCaptureGraphService } from './lead-capture-graph/lead-capture-message.service';
import { ConfigModule } from '@nestjs/config';
import storageConfig from './env.config';
import { ExtractorInfosModule } from 'src/third-party/langchain/extractor-infos/extractor-infos.module';
import { AnalyzeSentimentModule } from 'src/third-party/langchain/analyze-sentiment/analyze-sentiment.module';

@Module({
  imports: [
    ConfigModule.forFeature(storageConfig),
    PrismaModule,
    MessageChainModule,
    ExtractorInfosModule,
    AnalyzeSentimentModule,
  ],
  providers: [AIMessageService, LeadCaptureGraphService],
  exports: [AIMessageService, LeadCaptureGraphService],
})
export class AIModule {}
