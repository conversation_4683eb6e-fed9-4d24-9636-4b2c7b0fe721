import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class PrintTokenPayload implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    if (process.env.NODE_ENV === 'development') {
      const request = context.switchToHttp().getRequest();
      const user = request.user; // Payload do token JWT

      return true;
    }
  }
}
