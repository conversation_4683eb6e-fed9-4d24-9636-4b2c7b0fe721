import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from 'class-validator';

export class CreateAccountInputDto {
  @ApiProperty({
    description: 'Nome da empresa para a Account. Esse nome deve ser único.',
    required: true,
    uniqueItems: true,
    maxLength: 255,
    minLength: 3,
    default: '',
    type: String,
    example: 'Plyrchat',
  })
  @MinLength(3, {
    message: 'O Nome da empresa deve ter no mínimo 3 caracteres',
  })
  @MaxLength(255, {
    message: 'O Nome da empresa deve ter no máximo 255 caracteres',
  })
  @IsString({ message: 'O Nome da empresa deve ser uma string' })
  @IsNotEmpty({ message: 'O Nome da empresa é obrigatório' })
  companyName: string;

  @ApiProperty({
    description:
      'Campo que indica se a conta está ativa. Para criar uma conta desativada passe esse campo como false',
    required: false,
    default: true,
    type: Boolean,
    example: true,
  })
  @IsBoolean({ message: 'Deve estar no formato booleano.' })
  @IsOptional()
  isActive: boolean;

  @ApiProperty({
    description:
      'Campo que indica se a conta está excluída. Para criar uma conta que venha com padrão excluída passe esse campo como true',
    required: false,
    default: false,
    type: Boolean,
    example: false,
  })
  @IsBoolean({ message: 'Deve estar no formato booleano.' })
  @IsOptional()
  isDeleted: boolean;
}
