import { Example } from './types';

export const extractorExamples: Example[] = [
  {
    input: '<PERSON><PERSON><PERSON>, meu nome é <PERSON> e meu email é <EMAIL>.',
    toolCallOutputs: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
    ],
  },
  {
    input:
      'Meu nome é <PERSON>, telefone (11) 91234-5678, email <EMAIL>.',
    toolCallOutputs: [
      {
        name: '<PERSON>',
        phone: '(11) 91234-5678',
        email: '<EMAIL>',
      },
    ],
  },
  {
    input: 'Sou <PERSON>. Contato: <EMAIL>.',
    toolCallOutputs: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
    ],
  },
  {
    input:
      'Gostaria de me apresentar. Meu nome é <PERSON> e meu telefone é 21987654321.',
    toolCallOutputs: [
      {
        name: '<PERSON>',
        phone: '21987654321',
      },
    ],
  },
  {
    input:
      'Ei, eu sou Luísa. Email: <EMAIL> e telefone: (31) 99876-5432.',
    toolCallOutputs: [
      {
        name: 'Luísa',
        email: '<EMAIL>',
        phone: '(31) 99876-5432',
      },
    ],
  },
  {
    input: 'Chamo-me Pedro. Meu email é <EMAIL>.',
    toolCallOutputs: [
      {
        name: 'Pedro',
        email: '<EMAIL>',
      },
    ],
  },
  {
    input: 'Olá, sou Beatriz. Telefone: 31912345678.',
    toolCallOutputs: [
      {
        name: 'Beatriz',
        phone: '31912345678',
      },
    ],
  },
  {
    input:
      'Meu nome é Rafael Costa, email <EMAIL>, telefone (85) 98765-4321.',
    toolCallOutputs: [
      {
        name: 'Rafael Costa',
        email: '<EMAIL>',
        phone: '(85) 98765-4321',
      },
    ],
  },
  {
    input: 'Sou Daniela, contato: <EMAIL>.',
    toolCallOutputs: [
      {
        name: 'Daniela',
        email: '<EMAIL>',
      },
    ],
  },
  {
    input: 'Meu nome é Marcelo, telefone: (21) 91234-5678.',
    toolCallOutputs: [
      {
        name: 'Marcelo',
        phone: '(21) 91234-5678',
      },
    ],
  },
  {
    input:
      'Meu nome é Rodrigo, CPF: 123.456.789-00, email: <EMAIL>.',
    toolCallOutputs: [
      {
        name: 'Rodrigo',
        cpf: '12345678900',
        email: '<EMAIL>',
      },
    ],
  },
  {
    input: 'Olá, sou Camila. CPF: 98765432100 e telefone: (21) 99876-5432.',
    toolCallOutputs: [
      {
        name: 'Camila',
        cpf: '98765432100',
        phone: '(21) 99876-5432',
      },
    ],
  },
  {
    input: 'Chamo-me Eduardo, CPF: 111.222.333-44 e email: <EMAIL>.',
    toolCallOutputs: [
      {
        name: 'Eduardo',
        cpf: '11122233344',
        email: '<EMAIL>',
      },
    ],
  },
];
