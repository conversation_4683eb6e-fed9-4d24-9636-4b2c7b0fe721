import {
  ApiOperationOptions,
  ApiQueryOptions,
  ApiResponseNoStatusOptions,
} from '@nestjs/swagger';

type ProfileDelete = {
  apiOperation: ApiOperationOptions;
  apiQuerySecureId: ApiQueryOptions;
  apiForbiddenResponse: ApiResponseNoStatusOptions;
};

export const profileDelete: ProfileDelete = {
  apiOperation: {
    summary: 'Usuário pode deletar seu próprio perfil.',
    description: 'Usuário pode deletar seu próprio perfil.',
  },

  apiQuerySecureId: {
    name: 'secureId',
    required: true,
    type: String,
    description: 'Identificador único do perfil. Sendo o secureId do usuário.',
    example: 'd2b6b0b2-2b4a-4c5d-8c6e-6b7a8b9c0d1e',
  },

  apiForbiddenResponse: {
    examples: {
      anotherAccount: {
        summary:
          'Usuário não tem permissão para deletar um perfil que não seja o dele',
        value: {
          message: 'Você não pode acessar o perfil de outro usuário',
          error: 'Forbidden',
          statusCode: 403,
        },
      },

      unauthorized: {
        summary: 'Usuário não tem permissão para acessar o recurso',
        value: {
          message: 'Forbidden resource',
          error: 'Forbidden',
          statusCode: 403,
        },
      },
    },
  },
};
