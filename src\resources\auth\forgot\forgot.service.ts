import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { SendForgotPasswordEmailDto } from './dto/send-email-forgot.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { UserMapper } from 'src/resources/users/mapper/user.mapper';
import { PayloadService } from '../jwt/payload/payload.service';
import { JwtTokenService } from '../jwt/token/token.service';
import { HashService } from '../jwt/hash/hash.service';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { OneSignalService } from 'src/third-party/one-signal/one-signal.service';
import { ResetPasswordTemplate } from 'src/resources/notification/templates/reset-password.template';
import { NotificationService } from 'src/resources/notification/notification.service';

@Injectable()
export class ForgotService {
  constructor(
    private prisma: PrismaService,
    private readonly payloadService: PayloadService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly hashService: HashService,
    private readonly resetPasswordTemplate: ResetPasswordTemplate,
    private readonly notificationService: NotificationService,
  ) { }
  async sendForgotPasswordEmail(sendForgotPasswordEmailDto: SendForgotPasswordEmailDto) {
    const modelUser = await this.prisma.users.findFirst({
      where: {
        email: sendForgotPasswordEmailDto.email,
      },
      include: {
        usersAccounts: {
          select: {
            role: true,
            account: true,
            isActive: true,
            isDeleted: true,

            accountsPermissions: {
              select: {
                permission: true,
              }
            }
          }
        }
      }
    });

    if (!modelUser) {
      throw new NotFoundException('Email não encontrado!');
    }

    const account = await this.prisma.accounts.findFirst({
      where: {
        id: modelUser.usersAccounts[0].account.id,
        isActive: true
      }
    })

    if (!account) {
      throw new NotFoundException('Conta não encontrada!');
    }

    const user = UserMapper.fromModelToEntity(modelUser);

    const userTokenPayload = this.payloadService.generateUserTokenPayload(
      user,
      account.secureId,
    );

    const resetToken = this.jwtTokenService.generate(userTokenPayload);

    const expiresAt = new Date(); // TODO: DATAS VINDO COM 3 HORAS A MAIS
    expiresAt.setHours(expiresAt.getHours() + 1);

    await this.prisma.userKey.create({
      data: {
        userId: user.id,
        token: resetToken,
        expiresAt,
      },
    });
    
    const resetLink = `${sendForgotPasswordEmailDto.redirectUrl}reset-password?token=${encodeURIComponent(resetToken)}`;

    // Construindo o corpo do email
    const content = this.resetPasswordTemplate.build(user.name, resetLink);

    await this.notificationService.sendEmail({
      to: sendForgotPasswordEmailDto.email,
      subject: 'Recuperação de Senha',
      body: content,
    });
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, newPassword } = resetPasswordDto;

    try {
      const decoded = this.jwtTokenService.verify(token);

      const userKey = await this.prisma.userKey.findFirst({
        where: {
          token,
          expiresAt: { gt: new Date() },
          usedAt: null
        }
      });

      if (!userKey) {
        throw new BadRequestException('Token inválido ou expirado');
      }

      const user = await this.prisma.users.findUnique({
        where: { secureId: decoded.subject },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado.');
      }

      const hashedPassword = await this.hashService.generate(newPassword);

      await this.prisma.users.update({
        where: { id: user.id },
        data: { password: hashedPassword },
      });

      await this.prisma.userKey.update({
        where: { id: userKey.id },
        data: { usedAt: new Date() },
      });

      await this.prisma.userKey.updateMany({
        where: {
          userId: user.id,
          usedAt: null,
          expiresAt: { gt: new Date() },
        },
        data: {
          usedAt: new Date(),
        },
      });

      return { message: 'Senha redefinida com sucesso.' };
    } catch (error) {
      throw new BadRequestException('Token inválido ou expirado.');
    }
  }
}
