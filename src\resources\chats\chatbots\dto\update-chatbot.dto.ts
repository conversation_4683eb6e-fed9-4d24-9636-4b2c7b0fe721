import { PartialType } from '@nestjs/swagger';
import { CreateChatbotDto } from './create-chatbot.dto';
import { IsEnum, IsObject, IsOptional } from 'class-validator';
import {
  EnumChatBotEmotionalTone,
  EnumChatBotMood,
  EnumChatBotResponseSize,
  EnumChatBotResponseStyle,
} from '../entities/enums';
import { LeadCaptureJson } from '../constants/leadCaptureJson';

export class UpdateChatbotDto extends PartialType(CreateChatbotDto) {
  isActive: boolean;

  leadTriggerMessageLimit: number;

  leadCaptureMessage: string;

  leadCaptureThankYouMessage: string;

  temperature: number;

  @IsEnum(EnumChatBotEmotionalTone)
  @IsOptional()
  emotionalTone?: EnumChatBotEmotionalTone;

  @IsEnum(EnumChatBotMood)
  @IsOptional()
  mood?: EnumChatBotMood;

  @IsEnum(EnumChatBotResponseSize)
  @IsOptional()
  responseSize?: EnumChatBotResponseSize;

  @IsEnum(EnumChatBotResponseStyle)
  @IsOptional()
  responseStyle?: EnumChatBotResponseStyle;

  @IsObject()
  @IsOptional()
  leadCaptureJson: LeadCaptureJson;

  @IsOptional()
  greetingMessage: string;
}
