import { Modu<PERSON> } from "@nestjs/common";
import { EFIGetTokenModule } from "../get-token/efi-get-token.module";

import envModule from './env.config';
import { ConfigModule } from "@nestjs/config";
import { EfiSubscriptionsService } from "./efi-subscriptions.service";

@Module({
  imports: [EFIGetTokenModule, ConfigModule.forFeature(envModule)],
  providers: [EfiSubscriptionsService],
  exports: [EFIGetTokenModule, EfiSubscriptionsService]
})
export class EfiSubscriptionsModule { }