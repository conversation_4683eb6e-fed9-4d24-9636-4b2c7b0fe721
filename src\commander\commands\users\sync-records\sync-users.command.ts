import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { SyncUsersRecordService } from './sync-users.service';

@Command({
  name: 'sync-users',
  description:
    'Sincroniza os usuários do sistema com base no arquivo de usuários dentro da pasta constants. Esse comando além de cadastrar os usuários cadastra também os relacionamentos com as roles e accounts. \n⚠️ CUIDADO: Esse comando cria o users_account com base no id do usuário e no id da companyName. Portanto apenas a role pode ser trocada! ⚠️',
})
@Injectable()
export class SyncUserRecordsCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(private readonly syncUsers: SyncUsersRecordService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    this.syncUsers.execute(this.prisma).then(() => this.prisma.$disconnect());
  }
}
