import { Inject, Injectable } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import storageEnvs from './env.config';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

@Injectable()
export class OpenIAService {
  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
  ) {}

  async transcribeAudio(
    base64Audio: string,
    mimeType: string,
  ): Promise<string> {
    try {
      const tempDir = os.tmpdir();
      const fileName = `audio-${Date.now()}.${mimeType.split('/')[1] || 'ogg'}`;
      const filePath = path.join(tempDir, fileName);

      // Remove data:audio prefix if exists
      const base64Data = base64Audio.replace(/^data:audio\/\w+;base64,/, '');

      // Write base64 to temporary file
      fs.writeFileSync(filePath, Buffer.from(base64Data, 'base64'));

      const url = 'https://api.openai.com/v1/audio/transcriptions';
      const formData = new FormData();

      formData.append('model', 'gpt-4o-mini-transcribe');
      formData.append(
        'file',
        new Blob([fs.readFileSync(filePath)], { type: mimeType }),
      );

      const response = await axios.post(url, formData, {
        headers: {
          Authorization: `Bearer ${this.storageConfig.openApiKey}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      // Clean up temp file
      fs.unlinkSync(filePath);

      return response.data.text || undefined;
    } catch (error) {
      console.error('Error transcribing audio:', error);
      throw new Error('Failed to transcribe audio: ' + error.message);
    }
  }
}
