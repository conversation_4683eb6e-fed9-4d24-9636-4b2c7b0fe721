import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const badRequest: ApiResponseNoStatusOptions = {
  examples: {
    companyNameRequired: {
      summary: 'CompanyName não foi fornecido',
      value: {
        property: 'companyName',
        message: 'O Nome da empresa é obrigatório',
      },
    },

    companyNameMinLength: {
      summary: 'CompanyName tem tamanho menor que o permitido',
      value: {
        property: 'companyName',
        message: 'O Nome da empresa deve ter no mínimo 3 caracteres',
      },
    },

    companyNameMaxLength: {
      summary: 'CompanyName tem tamanho maior que o permitido',
      value: {
        property: 'companyName',
        message: 'O Nome da empresa deve ter no máximo 255 caracteres',
      },
    },

    companyNameInvalidType: {
      summary: 'CompanyName não é do tipo correto',
      value: {
        property: 'companyName',
        message: 'O Nome da empresa deve ser uma string',
      },
    },

    isActiveInvalidType: {
      summary: 'IsActive não é do tipo correto',
      value: {
        property: 'isActive',
        message: 'Deve estar no formato booleano.',
      },
    },

    isDeletedInvalidType: {
      summary: 'IsDeleted não é do tipo correto',
      value: {
        property: 'isDeleted',
        message: 'Deve estar no formato booleano.',
      },
    },
  },
};

const conflict: ApiResponseNoStatusOptions = {
  examples: {
    companyNameAlreadyExists: {
      summary: 'Foi passado um nome de empresa, para CompanyName que já existe',
      value: {
        message: 'Essa empresa já possui conta',
        error: 'Conflict',
        statusCode: 409,
      },
    },
  },
};

const created: ApiResponseNoStatusOptions = {
  examples: {
    created: {
      summary: 'Conta criada com sucesso',
      value: {},
    },
  },
};

export const createAccount = {
  status: {
    badRequest,
    conflict,
    created,
  },
};
