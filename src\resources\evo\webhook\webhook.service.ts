import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { v4 as uuidV4 } from 'uuid';
import { ChatsGateway } from 'src/resources/chats/chats.gateway';
import { format } from 'date-fns';
import { ChatSourceEnum } from 'src/@shared/types/chat-source';
import { EvoMessageService } from 'src/third-party/evo/message/message.service';
import { MessageContent } from '@langchain/core/messages';
import { AIMessageService } from 'src/resources/ia/ai-message/ai-message.service';
import { S3Service } from 'src/third-party/s3/s3.service';
import { OpenIAService } from 'src/third-party/openIA/openIA.service';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { extractMessage } from 'src/utils/extractMessage';
import { SubscriptionSessionService } from 'src/@shared/services/subscription-session/subscription-session.service';
import { NotificationService } from 'src/resources/notification/notification.service';
import formatPhoneNumber from 'src/utils/formatPhoneNumber';

@Injectable()
export class WebhookService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly chatGateway: ChatsGateway,
    private readonly evoService: EvoMessageService,
    private readonly aiService: AIMessageService,
    private readonly openIAService: OpenIAService,
    private readonly s3Service: S3Service, // Add S3Service dependency
    private readonly subscriptionSessionService: SubscriptionSessionService,
    private readonly notificationService: NotificationService,
  ) {}

  async markMessageAsRead(
    remoteJid: string,
    messageId: string,
    instanceName: string,
  ) {
    try {
      await this.evoService.markChatRead(remoteJid, messageId, instanceName);
    } catch (e) {
      return;
    }
  }

  async ensureSession(data: {
    instanceId: string;
    instanceName: string;
    customerPhone: string;
    receivePhoneNumber?: string;
    customerName?: string;
  }) {
    const {
      customerPhone,
      instanceId,
      instanceName,
      receivePhoneNumber,
      customerName,
    } = data;
    const formattedPhoneNumber = receivePhoneNumber.replace(/[^0-9]/g, '');

    const wppIntegrationData = await this.prisma.whatsAppIntegration.findFirst({
      where: {
        instanceId: instanceId,
        instanceName: instanceName,
        phoneNumber: formattedPhoneNumber,
      },
      select: {
        id: true,
        secureId: true,
        isBusiness: true,
        account: {
          select: {
            id: true,
            secureId: true,
          },
        },
        chatbot: {
          select: {
            secureId: true,
            isAI: true,
            isLeadCaptureActive: true,
            name: true,
          },
        },
      },
    });

    if (!wppIntegrationData) {
      throw new NotFoundException('Número de whatsapp não encontrado');
    }

    const sessions = await this.prisma.chatSessions.findFirst({
      where: {
        customerId: customerPhone,
        isDeleted: false,
        isActive: true,
        OR: [
          {
            whatsappId: wppIntegrationData.id,
          },
          {
            whatsapp: null,
          },
        ],
      },
      orderBy: {
        lastMessageAt: 'desc',
      },
      select: {
        secureId: true,
        isAIResponder: true,
        whatsappId: true,
      },
    });

    const { chatbot, account, isBusiness, secureId } = wppIntegrationData;

    const isAI = chatbot?.isAI;

    if (!sessions) {
      const newSession = await this.createSession({
        accountSecureId: account.secureId,
        customerPhone,
        whatsappIntegrationSecureId: secureId,
        isBusiness,
        chatBotIsAI: isAI,
        customerName: customerName,
      });

      return {
        sessionSecureId: newSession,
        isAIResponder: isAI,
        isIA: isAI,
        chatBotName: chatbot?.name,
        isFirstInteraction: true,
      };
    }

    if (!sessions?.whatsappId) {
      await this.alterSessionWppIntegration(sessions.secureId, secureId);
    }

    return {
      sessionSecureId: sessions.secureId,
      isAIResponder: sessions.isAIResponder,
      isIA: chatbot?.isAI,
      chatBotName: chatbot?.name,
      isFirstInteraction: false,
    };
  }

  async createSession({
    accountSecureId,
    whatsappIntegrationSecureId,
    customerName,
    customerPhone,
    isBusiness,
    chatBotIsAI,
  }: {
    accountSecureId: string;
    customerPhone: string;
    whatsappIntegrationSecureId: string;
    customerName?: string;
    isBusiness?: boolean;
    chatBotIsAI?: boolean;
  }): Promise<string> {
    const formattedIncomingNumber = customerPhone.replace(/[^0-9]/g, '');
    const customerNameCheck =
      customerName || `Usuário ${format(new Date(), 'dd/MM/yy HH:mm')}`;

    //Roleta de atendentes
    let attendantId: string | undefined = undefined;
    if (!chatBotIsAI) {
      const attendant = await this.prisma.usersAccounts.findMany({
        where: {
          account: {
            secureId: accountSecureId,
          },
          isActive: true,
          isDeleted: false,
          AND: {
            OR: [{ role: { name: 'Attendant' } }, { role: { name: 'App' } }],
            participatesInRotation: true,
          },
        },
      });

      if (attendant.length > 0) {
        const randomAttendant = Math.floor(Math.random() * attendant.length);
        attendantId = attendant[randomAttendant].secureId;
      }
    }

    const contactExists = await this.prisma.contacts.findFirst({
      where: {
        phone: formattedIncomingNumber,
        account: {
          secureId: accountSecureId,
        },
      },
    });

    const hasRemainingIASessions =
      await this.subscriptionSessionService.hasRemainingSessions({
        accountSecureId: accountSecureId,
      });

    const hasRemainingFreemiumTrialDays =
      await this.subscriptionSessionService.hasRemainingFreemiumTrialDays({
        accountSecureId: accountSecureId,
      });

    if (!hasRemainingIASessions || !hasRemainingFreemiumTrialDays) {
      chatBotIsAI = false;
    }

    const sessions = await this.prisma.chatSessions.create({
      data: {
        account: {
          connect: {
            secureId: accountSecureId,
          },
        },
        customerId: customerPhone,
        secureId: uuidV4(),
        customerName: contactExists?.name || customerNameCheck,
        customerPhone: formattedIncomingNumber,
        isAIResponder: chatBotIsAI,
        attendant: attendantId
          ? {
              connect: {
                secureId: attendantId,
              },
            }
          : undefined,
        source: isBusiness
          ? ChatSourceEnum.whatsappBusiness
          : ChatSourceEnum.whatsapp,
        whatsapp: {
          connect: {
            secureId: whatsappIntegrationSecureId,
          },
        },
      },
      select: {
        secureId: true,
      },
    });

    if (chatBotIsAI) {
      await this.subscriptionSessionService.decrementRemainingSession({
        accountSecureId: accountSecureId,
      });
    }

    return sessions.secureId;
  }

  async saveCustomerMessage({
    sessionSecureId,
    message,
    messageJid,
    messageReplied,
  }: {
    sessionSecureId: string;
    message: string;
    messageJid: string;
    messageReplied?: string;
  }) {
    try {
      let messageRepliedId;
      if (messageReplied) {
        const messageRepliedData = await this.prisma.chatMessages.findFirst({
          where: {
            OR: [
              {
                sendMessage: messageReplied,
              },
              {
                receiveMessage: messageReplied,
              },
            ],
            AND: {
              chatSession: {
                secureId: sessionSecureId,
              },
            },
          },
          select: {
            id: true,
          },
        });

        if (!messageRepliedData) {
          throw new NotFoundException('Mensagem não encontrada');
        }

        messageRepliedId = messageRepliedData.id;
      }

      const newMessage = await this.prisma.chatMessages.create({
        data: {
          secureId: uuidV4(),
          chatSession: {
            connect: {
              secureId: sessionSecureId,
            },
          },
          receiveMessage: message,
          messageJid: messageJid,
          messageDirection: 'received',
          messageType: 'text',
          replyTo: messageRepliedId
            ? {
                connect: {
                  id: messageRepliedId,
                },
              }
            : undefined,
        },
        include: {
          replyTo: {
            select: {
              secureId: true,
            },
          },
          chatSession: {
            select: {
              id: true,
              customerName: true,
              attendant: {
                select: {
                  userId: true,
                },
              },
              accountId: true,
            },
          },
        },
      });

      await this.notificationService.createNotification({
        title: 'Nova mensagem recebida',
        message: `Nova mensagem recebida de ${newMessage.chatSession.customerName}`,
        userId: newMessage.chatSession.attendant?.userId,
        accountId: newMessage.chatSession.accountId,
        sessionId: newMessage.chatSession.id,
      });

      await this.prisma.chatSessions.update({
        where: {
          secureId: sessionSecureId,
        },
        data: {
          isArchived: false,
        },
      });

      this.chatGateway.broadcastChatMessageUpdate({
        createdAt: newMessage.createdAt,
        receiveMessage: newMessage.receiveMessage,
        secureId: newMessage.secureId,
        sessionId: sessionSecureId,
        replyTo: newMessage.replyTo?.secureId,
      });

      return;
    } catch (error) {
      throw new BadRequestException(
        'Erro ao salvar mensagem do cliente: ' + error.message,
      );
    }
  }

  async sendToProcessAIMessage(data: {
    chatSessionSecureId: string;
    message: string;
  }) {
    return this.aiService.processMessage({
      chatSessionSecureId: data.chatSessionSecureId,
      message: data.message,
    });
  }

  async saveAIMessage({
    message,
    instanceName,
    customerPhone,
    sessionSecureId,
    inputToken,
    outputToken,
    chatBotName,
  }: {
    message: string | MessageContent;
    instanceName: string;
    customerPhone: string;
    sessionSecureId: string;
    inputToken?: number;
    outputToken?: number;
    chatBotName?: string;
  }) {
    const newMessage = await this.prisma.chatMessages.create({
      data: {
        secureId: uuidV4(),
        chatSession: {
          connect: {
            secureId: sessionSecureId,
          },
        },
        inputToken,
        outputToken,
        sendMessage: message.toString(),
        messageDirection: 'sent',
        messageType: 'text',
      },
    });
    //envia a mensagem resposta da AI para o whatsapp do cliente

    const aiResponseMessage = message.toString().replace(/\*\*/g, '*');

    const messages = aiResponseMessage.split('\n\n'); // Split the content into separate

    let messagesWithName = [];
    if (chatBotName) {
      messagesWithName.push(`*${chatBotName}:*\n\n${messages[0]}`);
      messagesWithName = messagesWithName.concat(messages.slice(1));
    } else {
      messagesWithName.concat(messages);
    }

    for await (const message of messagesWithName) {
      const time = message.length * 20;
      await this.evoService.sendMessage(
        customerPhone,
        message,
        instanceName,
        time,
      );
    }

    //envia a mensagem para o websocket para acompanhar a conversa
    this.chatGateway.broadcastChatMessageUpdate({
      createdAt: newMessage.createdAt,
      sendMessage: newMessage.sendMessage,
      secureId: newMessage.secureId,
      sessionId: sessionSecureId,
    });
  }

  async saveTokenUsageInDb(
    sessionSecureId: string,
    inputToken: number,
    outputToken: number,
  ) {
    const {
      whatsapp: {
        chatbot: { id },
      },
    } = await this.prisma.chatSessions.findFirst({
      where: {
        secureId: sessionSecureId,
      },
      select: {
        whatsapp: {
          select: {
            chatbot: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!id) {
      throw new NotFoundException('Chat não encontrado');
    }

    await this.prisma.chatBots.update({
      data: {
        inputToken: {
          increment: inputToken,
        },
        outputToken: {
          increment: outputToken,
        },
      },
      where: {
        id: id,
      },
    });
  }

  async processAudioMessage({
    base64Audio,
    mimetype,
    sessionSecureId,
    messageJid,
    messageReplied,
  }: {
    sessionSecureId: string;
    base64Audio: string;
    mimetype: string;
    messageJid?: string;
    messageReplied?: string;
  }) {
    try {
      // Generate a unique filename
      const fileExtension = mimetype.split('/')[1]?.split(';')[0] || 'ogg';
      const fileName = `${uuidV4()}.${fileExtension}`;

      // Save to S3
      const uploadId = await this.s3Service.uploadBase64(
        base64Audio,
        mimetype,
        'audio',
        fileName,
      );

      if (!uploadId) {
        throw new Error('Failed to upload audio to S3');
      }

      let messageRepliedId;
      if (messageReplied) {
        const messageRepliedData = await this.prisma.chatMessages.findFirst({
          where: {
            OR: [
              {
                sendMessage: messageReplied,
              },
              {
                receiveMessage: messageReplied,
              },
            ],
            AND: {
              chatSession: {
                secureId: sessionSecureId,
              },
            },
          },
          select: {
            id: true,
          },
        });

        if (!messageRepliedData) {
          throw new NotFoundException('Mensagem não encontrada');
        }

        messageRepliedId = messageRepliedData.id;
      }

      const newMessage = await this.prisma.chatMessages.create({
        data: {
          secureId: uuidV4(),
          chatSession: {
            connect: {
              secureId: sessionSecureId,
            },
          },
          replyTo: messageRepliedId
            ? {
                connect: {
                  id: messageRepliedId,
                },
              }
            : undefined,
          messageType: 'audio',
          messageDirection: 'received',
          messageJid,
          upload: {
            connect: {
              secureId: uploadId,
            },
          },
        },
        include: {
          upload: {
            select: {
              urlCdn: true,
            },
          },
          replyTo: {
            select: {
              secureId: true,
            },
          },
          chatSession: {
            select: {
              id: true,
              customerName: true,
              attendant: {
                select: {
                  userId: true,
                },
              },
              accountId: true,
            },
          },
        },
      });

      await this.notificationService.createNotification({
        title: 'Nova mensagem de áudio recebida',
        message: `Nova mensagem de áudio recebida de ${newMessage.chatSession.customerName}`,
        userId: newMessage.chatSession.attendant?.userId,
        accountId: newMessage.chatSession.accountId,
        sessionId: newMessage.chatSession.id,
      });

      // Update session status
      await this.prisma.chatSessions.update({
        where: {
          secureId: sessionSecureId,
        },
        data: {
          isRead: false,
          isArchived: false,
        },
      });

      this.chatGateway.broadcastChatMessageUpdate({
        createdAt: newMessage.createdAt,
        secureId: newMessage.secureId,
        messageDirection: 'received',
        sessionId: sessionSecureId,
        type: 'audio',
        urlFile: newMessage.upload.urlCdn,
        replyTo: newMessage.replyTo?.secureId,
      });

      const transcription = await this.openIAService.transcribeAudio(
        base64Audio,
        mimetype,
      );

      return transcription;
    } catch (error) {
      console.error('Error processing audio message:', error);
      throw new BadRequestException(
        'Erro ao processar mensagem de áudio: ' + error.message,
      );
    }
  }

  async processDocumentMessage({
    base64Document,
    mimetype,
    type,
    sessionSecureId,
    messageJid,
    messageReplied,
  }: {
    sessionSecureId: string;
    type: 'file' | 'image';
    base64Document: string;
    mimetype: string;
    messageJid?: string;
    messageReplied?: string;
  }) {
    try {
      const fileExtension = mimetype.split('/')[1]?.split(';')[0] || 'pdf';
      const fileName = `${uuidV4()}.${fileExtension}`;

      const uploadId = await this.s3Service.uploadBase64(
        base64Document,
        mimetype,
        type,
        fileName,
      );

      if (!uploadId) {
        throw new Error('Erro ao fazer upload do arquivo para o S3');
      }

      let messageRepliedId;
      if (messageReplied) {
        const messageRepliedData = await this.prisma.chatMessages.findFirst({
          where: {
            OR: [
              {
                sendMessage: messageReplied,
              },
              {
                receiveMessage: messageReplied,
              },
            ],
            AND: {
              chatSession: {
                secureId: sessionSecureId,
              },
            },
          },
          select: {
            id: true,
          },
        });

        if (!messageRepliedData) {
          throw new NotFoundException('Mensagem não encontrada');
        }

        messageRepliedId = messageRepliedData.id;
      }

      const newMessage = await this.prisma.chatMessages.create({
        data: {
          secureId: uuidV4(),
          chatSession: {
            connect: {
              secureId: sessionSecureId,
            },
          },
          replyTo: messageRepliedId
            ? {
                connect: {
                  id: messageRepliedId,
                },
              }
            : undefined,
          messageType: type,
          messageDirection: 'received',
          messageJid,
          upload: {
            connect: {
              secureId: uploadId,
            },
          },
        },
        include: {
          upload: {
            select: {
              urlCdn: true,
            },
          },
          replyTo: {
            select: {
              secureId: true,
            },
          },
          chatSession: {
            select: {
              id: true,
              customerName: true,
              attendant: {
                select: {
                  userId: true,
                },
              },
              accountId: true,
            },
          },
        },
      });

      await this.notificationService.createNotification({
        title: 'Nova mensagem recebida',
        message: `Nova mensagem recebida de ${newMessage.chatSession.customerName}`,
        userId: newMessage.chatSession.attendant?.userId,
        accountId: newMessage.chatSession.accountId,
        sessionId: newMessage.chatSession.id,
      });

      await this.prisma.chatSessions.update({
        where: {
          secureId: sessionSecureId,
        },
        data: {
          isRead: false,
          isArchived: false,
        },
      });

      this.chatGateway.broadcastChatMessageUpdate({
        createdAt: newMessage.createdAt,
        secureId: newMessage.secureId,
        messageDirection: 'received',
        sessionId: sessionSecureId,
        type: type,
        urlFile: newMessage.upload.urlCdn,
        replyTo: newMessage.replyTo?.secureId,
      });

      return;
    } catch (e) {
      console.error('Error processing audio message:', e);
      throw new BadRequestException(
        'Erro ao processar mensagem de áudio: ' + e.message,
      );
    }
  }

  async processIncomingEvoMessage(incomingMessageWebhookDto: any) {
    // Copia a lógica do método incomingMessages do controller
    const remoteJid = incomingMessageWebhookDto.data.key.remoteJid;

    if (!remoteJid.includes('@s.whatsapp.net')) {
      throw new NotFoundException('Tipo de mensagem não suportado');
    }

    if (incomingMessageWebhookDto.event !== 'messages.upsert') {
      throw new NotFoundException('Event not supported');
    }

    if (incomingMessageWebhookDto.data.key.fromMe) {
      return;
    }

    const {
      messageType,
      message: { conversation },
    } = incomingMessageWebhookDto.data;

    const quotedRawMessage =
      incomingMessageWebhookDto.data.contextInfo?.quotedMessage?.conversation ||
      undefined;

    const quotedMessage = quotedRawMessage
      ? extractMessage(quotedRawMessage)
      : null;

    await this.markMessageAsRead(
      incomingMessageWebhookDto.data.key.remoteJid,
      incomingMessageWebhookDto.data.key.id,
      incomingMessageWebhookDto.instance,
    );

    const {
      isAIResponder,
      sessionSecureId,
      chatBotName,
      isIA,
      isFirstInteraction,
    } = await this.ensureSession({
      customerPhone: incomingMessageWebhookDto.data.key.remoteJid,
      instanceId: incomingMessageWebhookDto.data.instanceId,
      instanceName: incomingMessageWebhookDto.instance,
      customerName: incomingMessageWebhookDto.data.pushName,
      receivePhoneNumber: incomingMessageWebhookDto.sender,
    });

    let message = conversation;
    if (messageType === 'conversation') {
      await this.saveCustomerMessage({
        sessionSecureId: sessionSecureId,
        message: incomingMessageWebhookDto.data.message.conversation,
        messageJid: incomingMessageWebhookDto.data.key.id,
        messageReplied: quotedMessage,
      });
    } else if (messageType === 'audioMessage') {
      message = await this.processAudioMessage({
        sessionSecureId: sessionSecureId,
        base64Audio: incomingMessageWebhookDto.data.message.base64,
        mimetype: incomingMessageWebhookDto.data.message.audioMessage.mimetype,
        messageJid: incomingMessageWebhookDto.data.key.id,
      });
    } else if (
      messageType === 'documentMessage' ||
      messageType === 'imageMessage'
    ) {
      await this.processDocumentMessage({
        sessionSecureId: sessionSecureId,
        base64Document: incomingMessageWebhookDto.data.message.base64,
        type: messageType === 'documentMessage' ? 'file' : 'image',
        mimetype:
          incomingMessageWebhookDto.data.message?.documentMessage?.mimetype ||
          incomingMessageWebhookDto.data.message?.imageMessage?.mimetype,
        messageJid: incomingMessageWebhookDto.data.key.id,
        messageReplied: quotedMessage,
      });
      return;
    } else {
      throw new NotFoundException('Message type not supported');
    }

    if (isFirstInteraction) {
      const isSendGreeting = await this.sendGreetingMessage(sessionSecureId);
      if (isSendGreeting) {
        return;
      }
    }

    if (isAIResponder && isIA) {
      const { generatedAIAnswer, attachmentMessage, customerInfoRequest } =
        await this.sendToProcessAIMessage({
          chatSessionSecureId: sessionSecureId,
          message: message,
        });

      const tokenUsage = {
        inputToken: 0,
        outputToken: 0,
      };

      let aiMessage = '';
      if (generatedAIAnswer) {
        for await (const chunk of generatedAIAnswer) {
          aiMessage += chunk.content;
          if (chunk.usage_metadata) {
            tokenUsage.inputToken += chunk.usage_metadata.input_tokens;
            tokenUsage.outputToken += chunk.usage_metadata.output_tokens;
          }
        }
      }

      const customerRequestTokenUsage = {
        inputToken: 0,
        outputToken: 0,
      };

      let customerRequestMessage = '';
      if (customerInfoRequest) {
        if (customerInfoRequest instanceof IterableReadableStream) {
          for await (const chunk of customerInfoRequest) {
            customerRequestMessage += chunk.content;
            if (chunk.usage_metadata) {
              customerRequestTokenUsage.inputToken +=
                chunk.usage_metadata.input_tokens;
              customerRequestTokenUsage.outputToken +=
                chunk.usage_metadata.output_tokens;
            }
          }
        } else {
          customerRequestMessage = customerInfoRequest;
        }
      }

      await this.saveAIMessage({
        message: aiMessage,
        sessionSecureId: sessionSecureId,
        customerPhone: incomingMessageWebhookDto.data.key.remoteJid,
        instanceName: incomingMessageWebhookDto.instance,
        inputToken: tokenUsage.inputToken,
        outputToken: tokenUsage.outputToken,
        chatBotName: aiMessage !== '' ? chatBotName : undefined,
      });

      if (attachmentMessage) {
        await this.saveAIMessage({
          message: attachmentMessage,
          sessionSecureId: sessionSecureId,
          customerPhone: incomingMessageWebhookDto.data.key.remoteJid,
          instanceName: incomingMessageWebhookDto.instance,
          chatBotName: aiMessage !== '' ? undefined : chatBotName,
        });
      }

      if (customerRequestMessage && customerRequestMessage !== '') {
        await this.saveAIMessage({
          message: customerRequestMessage,
          sessionSecureId: sessionSecureId,
          customerPhone: incomingMessageWebhookDto.data.key.remoteJid,
          instanceName: incomingMessageWebhookDto.instance,
          inputToken: customerRequestTokenUsage.inputToken,
          outputToken: customerRequestTokenUsage.outputToken,
          chatBotName: aiMessage !== '' ? undefined : chatBotName,
        });
      }

      await this.saveTokenUsageInDb(
        sessionSecureId,
        tokenUsage.inputToken + customerRequestTokenUsage.inputToken,
        tokenUsage.outputToken + customerRequestTokenUsage.outputToken,
      );
      return;
    }
    return;
  }

  async alterSessionWppIntegration(
    sessionSecureId: string,
    wppSecureId: string,
  ) {
    const session = await this.prisma.chatSessions.findFirst({
      where: {
        secureId: sessionSecureId,
      },
    });

    if (!session) {
      throw new NotFoundException('Sessão não encontrada');
    }

    await this.prisma.chatSessions.update({
      where: {
        secureId: sessionSecureId,
      },
      data: {
        whatsapp: {
          connect: {
            secureId: wppSecureId,
          },
        },
      },
    });
  }

  async sendGreetingMessage(sessionSecureId: string) {
    const session = await this.prisma.chatSessions.findFirst({
      where: {
        secureId: sessionSecureId,
      },
      include: {
        whatsapp: {
          include: {
            chatbot: true,
          },
        },
        account: true,
      },
    });

    if (!session || !session.whatsapp) {
      throw new NotFoundException('Erro ao buscar informações da sessão');
    }

    if (
      !session.whatsapp?.chatbot ||
      !session.whatsapp?.chatbot.isAI ||
      !session.whatsapp?.chatbot.greetingMessage
    ) {
      return false;
    }

    await this.saveAIMessage({
      message: session.whatsapp.chatbot.greetingMessage,
      sessionSecureId: sessionSecureId,
      customerPhone: session.customerPhone,
      instanceName: session.whatsapp.instanceName,
      chatBotName: session.whatsapp.chatbot.name,
    });

    return true;
  }

  async processUpdateIntegrationStatus(incomingEventWebhookDto: any) {
    const integration = await this.prisma.whatsAppIntegration.findFirst({
      where: {
        instanceName: incomingEventWebhookDto.instance as string,
      },
    });
    if (incomingEventWebhookDto.data.state === 'close') {
      if (!integration) {
        throw new Error('Integração não encontrada');
      }
      console.log(integration.id);
      await this.prisma.whatsAppIntegration.update({
        where: {
          id: integration.id,
        },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });
      await this.notificationService.sendToAllUsersInAccount({
        accountId: integration.accountId,
        title: 'Integração desativada',
        message: `A integração do WhatsApp com o número ${formatPhoneNumber(integration.phoneNumber)} foi desativada.`,
      });
    }
    if (incomingEventWebhookDto.data.state === 'open') {
      if (!integration) {
        throw new NotFoundException('Integração não encontrada');
      }
      await this.prisma.whatsAppIntegration.update({
        where: {
          id: integration.id,
        },
        data: {
          isActive: true,
          updatedAt: new Date(),
        },
      });
      await this.notificationService.sendToAllUsersInAccount({
        accountId: integration.accountId,
        title: 'Integração ativada',
        message: `A integração do WhatsApp com o número ${formatPhoneNumber(integration.phoneNumber)} foi ativada com sucesso.`,
      });
    }
    return;
  }
}
