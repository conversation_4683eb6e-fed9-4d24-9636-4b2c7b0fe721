/**
 * @description Esse Dto é uma representação da assinatura recebida pela EFI
 */
export class EFISubscriptionInputDto {
  subscription_id: number;
  value: number;
  status: 'new' | 'active' | 'new_charge' | 'canceled' | 'expired';
  custom_id: string | null; //secureId subscription
  notification_url: string | null; //ngrok
  payment_method: 'banking_billet' | 'credit_card' | null;
  next_execution: string | null;
  next_expire_at: string | null;
  plan: {
    plan_id: number;
    name: string;
    interval: number;
    repeats: number | null;
  };
  // plan: EFIPlanInputDto;
  occurrences: number;
  created_at: string;
  history: {
    charge_id: number;
    status: ChargeStatus;
    created_at: string;
  }[];
}

export type ChargeStatus =
  | 'new'
  | 'waiting'
  | 'identified'
  | 'approved'
  | 'paid'
  | 'unpaid'
  | 'refunded'
  | 'contested'
  | 'canceled'
  | 'settled'
  | 'link'
  | 'expired';

