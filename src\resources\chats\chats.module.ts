import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { SessionsModule } from './sessions/sessions.module';
import { ChatsGateway } from './chats.gateway';
import { ChatsService } from './chats.service';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { ChatsController } from './chats.controller';
import { ChatsOrchestratorService } from './chats-orchestrator.service';
import { S3Module } from 'src/third-party/s3/s3.module';
import { EvoMessageModule } from 'src/third-party/evo/message/message.module';
import { ChatbotsModule } from './chatbots/chatbots.module';
import { KnowledgeBaseModule } from './knowledge-base/knowledge-base.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { MetaModule } from 'src/third-party/meta/meta.module';
import { MessagesModule } from './messages/messages.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    SessionsModule,
    PrismaModule,
    S3Module,
    EvoMessageModule,
    MetaModule,
    ChatbotsModule,
    MessagesModule,
    NotificationModule,
    KnowledgeBaseModule,
  ],
  providers: [ChatsGateway, ChatsService, ChatsOrchestratorService],
  controllers: [ChatsController],
  exports: [ChatsGateway],
})
export class ChatsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('chats');
  }
}
