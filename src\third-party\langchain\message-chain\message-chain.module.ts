import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import storageConfig from '../env.config';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { MessageChainService } from './message-chain.service';

@Module({
  imports: [ConfigModule.forFeature(storageConfig), PrismaModule],
  providers: [MessageChainService],
  exports: [MessageChainService],
})
export class MessageChainModule {}
