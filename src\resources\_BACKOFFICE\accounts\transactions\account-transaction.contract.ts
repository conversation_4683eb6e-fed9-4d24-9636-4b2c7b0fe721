import { Prisma } from '@prisma/client';
import { TransactionStatus } from 'src/@shared/types/transaction';

export interface AccountTransactionDecoratorOutput {
  transactionStatus?: TransactionStatus;
  subscriptionId?: number;
  accountId?: number;
}

export type AccountTransactionWithAccountAndSubscription =
  Prisma.TransactionsGetPayload<{
    include: {
      subscription: true;

      account: {
        select: {
          secureId: true;
          companyName: true;
          isActive: true;
          isDeleted: true;
          createdAt: true;
          updatedAt: true;
        };
      };
    };
  }>;
