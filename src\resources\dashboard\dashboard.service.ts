import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { AppDashboardVO } from './value-object/dashboard.vo';
import { AppDashboardMapper } from './mapper/dashboard.mapper';

@Injectable()
export class DashboardService {
  constructor(private readonly prismaService: PrismaService) {}

  async findAll(accountId: number, startDate?: string, endDate?: string) {
    const createdAt = startDate
      ? {
          gte: new Date(startDate),
          lte: endDate ? new Date(endDate) : new Date(),
        }
      : undefined;

    const chatsQuantity = await this.prismaService.chatSessions.count({
      where: {
        accountId,
        createdAt,
      },
    });

    const leadsQuantity = await this.prismaService.contacts.count({
      where: {
        accountId,
        createdAt,
        name: { not: null },
        OR: [{ email: { not: null } }, { phone: { not: null } }],
      },
    });

    const aiMessagesQuantity = await this.prismaService.chatMessages.count({
      where: {
        chatBot: {
          accountId,
          isAI: true,
        },
        AND: {
          sendMessage: { not: null },
        },
      },
    });

    const aiSessionsQuantity = await this.prismaService.chatSessions.count({
      where: {
        accountId,
        isAIResponder: true,
        createdAt,
      },
    });

    const attendantSessionsQuantity = await this.prismaService.chatSessions.count({
      where: {
        accountId,
        isAIResponder: false,
        createdAt,
      },
    });

    const waitingSessionsQuantity = await this.prismaService.chatSessions.count({
      where: {
        accountId,
        isAIResponder: false,
        isRead: false,
        createdAt,
      },
    });

    // Count ongoing conversations (Conversas em Andamento)
    const ongoingConversationsQuantity = await this.prismaService.chatSessions.count({
      where: {
        accountId,
        isActive: true,
        isDeleted: false,
        isArchived: false,
        isFinalized: false,
        chatMessages: {
          some: {},
        },
      },
    });

    // Count finalized conversations (Conversas Finalizadas)
    const finalizedConversationsQuantity = await this.prismaService.chatSessions.count({
      where: {
        accountId,
        isActive: true,
        isDeleted: false,
        isFinalized: true,
        createdAt,
        chatMessages: {
          some: {},
        },
      },
    });

    // Calculate average first response time
    const averageFirstResponseTime = await this.calculateAverageFirstResponseTime(accountId, startDate, endDate);

    const vo = new AppDashboardVO({
      aiMessagesQuantity,
      chatsQuantity,
      leadsQuantity,
      aiSessionsQuantity,
      attendantSessionsQuantity,
      waitingSessionsQuantity,
      ongoingConversationsQuantity,
      finalizedConversationsQuantity,
      averageFirstResponseTime,
    });

    return AppDashboardMapper.fromDashboardVOToDashboardOutputDto(vo);
  }

  /**
   * Calculates the average time between the first customer message and the first response
   * in chat sessions for a specific account within a date range.
   */
  private async calculateAverageFirstResponseTime(
    accountId: number,
    startDate?: string,
    endDate?: string
  ): Promise<number> {
    // Define date filter for queries
    const dateFilter = startDate
      ? {
          gte: new Date(startDate),
          lte: endDate ? new Date(endDate) : new Date(),
        }
      : undefined;

    // Get all chat sessions for the account within the date range
    const chatSessions = await this.prismaService.chatSessions.findMany({
      where: {
        accountId,
        createdAt: dateFilter,
      },
      select: {
        id: true,
      },
    });

    // If no sessions, return 0
    if (chatSessions.length === 0) {
      return 0;
    }

    // Get session IDs
    const sessionIds = chatSessions.map(session => session.id);

    // For each session, find the first customer message and the first response
    const messagesData = await this.prismaService.chatMessages.findMany({
      where: {
        chatSessionId: {
          in: sessionIds,
        },
      },
      select: {
        chatSessionId: true,
        messageDirection: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Group messages by session
    const messagesBySession = new Map<number, { firstCustomerMessage?: Date, firstResponse?: Date }>();


    // Process messages to find first customer message and first response for each session
    for (const message of messagesData) {
      if (!messagesBySession.has(message.chatSessionId)) {
        messagesBySession.set(message.chatSessionId, {});
      }

      const sessionData = messagesBySession.get(message.chatSessionId)!;

      // First customer message (received)
      if (message.messageDirection === 'received' && !sessionData.firstCustomerMessage) {
        sessionData.firstCustomerMessage = message.createdAt;
      }

      // First response (sent)
      if (message.messageDirection === 'sent' && !sessionData.firstResponse && sessionData.firstCustomerMessage) {
        sessionData.firstResponse = message.createdAt;
      }
    }

    // Calculate response times in seconds
    let totalResponseTime = 0;
    let validSessionsCount = 0;

    for (const sessionData of messagesBySession.values()) {
      if (sessionData.firstCustomerMessage && sessionData.firstResponse) {
        const responseTimeMs = sessionData.firstResponse.getTime() - sessionData.firstCustomerMessage.getTime();
        const responseTimeSeconds = responseTimeMs / 1000;
        totalResponseTime += responseTimeSeconds;
        validSessionsCount++;
      }
    }

    // Return average response time in seconds, or 0 if no valid sessions
    return validSessionsCount > 0 ? Math.round(totalResponseTime / validSessionsCount) : 0;
  }
}
