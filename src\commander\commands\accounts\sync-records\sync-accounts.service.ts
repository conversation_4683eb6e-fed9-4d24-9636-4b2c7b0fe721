import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

import { accounts } from 'src/constants/accounts';

import { Account } from 'src/resources/_BACKOFFICE/accounts/entities/account.entity';

import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';

@Injectable()
export class SyncAccountsService extends ISyncRecordCommand {
  entity: string = 'Accounts';

  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Sincronizando ${this.entity}:`);

    for (const role of accounts) {
      this.logRecord(role.companyName);
      await this.createRecord(role, prisma);
    }

    console.log('\x1b[32m', `\n${this.entity} sincronizado!\n`);
  }

  logRecord(itemName: string) {
    console.log('\x1b[32m', `\t${itemName}`);
  }

  async createRecord(record: Account, prisma: PrismaClient) {
    let isActive: boolean;
    let isDeleted: boolean;

    if (record.isActive === undefined || record.isActive === null) {
      isActive = true;
    } else {
      isActive = record.isActive === false ? false : true;
    }

    if (record.isDeleted === undefined || record.isDeleted === null) {
      isDeleted = false;
    } else {
      isDeleted = record.isDeleted === false ? false : true;
    }

    await prisma.accounts.upsert({
      where: { companyName: record.companyName },
      create: {
        secureId: record.secureId,
        companyName: record.companyName,
        isActive: isActive,
        isDeleted: isDeleted,
      },
      update: {
        // secureId: record.secureId, // Esse campo foi desabilitado para não alterar o secureID quando alterar os dados quando fizer um update
        companyName: record.companyName,
        isActive: isActive,
        isDeleted: isDeleted,
      },
    });
  }
}
