import { HttpException, HttpStatus } from '@nestjs/common';
import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ContactCreateDto {
  @IsString({ message: 'O nome deve ser uma string.' })
  @IsNotEmpty({ message: 'O nome não pode estar vazio.' })
  name: string;

  @IsEmail({}, { message: 'O email fornecido não é válido.' })
  @IsOptional()
  email?: string;

  @IsString({ message: 'O telefone deve ser uma string.' })
  @IsNotEmpty({ message: 'O telefone não pode estar vazio.' })
  @Transform(({ value }) => removeCharactersAndValidatePhone(value))
  phone: string;

  @IsString({ message: 'O documento deve ser uma string.' })
  @IsOptional()
  @Transform(({ value }) => removeCharactersAndValidateCpf(value))
  document?: string;
}

function removeCharactersAndValidatePhone(value: string): string {
  if (!value) {
    return value;
  }
  const formattedValue = value.replace(/[^0-9]/g, '');

  if (formattedValue.length < 12 || formattedValue.length > 13) {
    throw new HttpException(
      {
        message: [
          {
            property: 'phone',
            message:
              'phone deve conter o código do país e o DDD, sendo entre 12 e 13 caracteres.',
          },
        ],
        error: 'Bad Request',
        statusCode: HttpStatus.BAD_REQUEST,
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  return formattedValue;
}

function removeCharactersAndValidateCpf(value: string): string {
  if (!value) {
    return value;
  }
  const formattedValue = value.replace(/[^0-9]/g, '');

  if (formattedValue.length !== 11) {
    throw new HttpException(
      {
        message: [
          {
            property: 'document',
            message: 'CPF deve conter exatamente 11 dígitos.',
          },
        ],
        error: 'Bad Request',
        statusCode: HttpStatus.BAD_REQUEST,
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  return formattedValue;
}
