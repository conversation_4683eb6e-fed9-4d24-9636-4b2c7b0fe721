import { Inject, Injectable } from '@nestjs/common';
import oneSignalEnvs from './env.config';
import { CreateOneSignalDto } from './dto/create-one-signal.dto';
import axios from 'axios';
import { ConfigType } from '@nestjs/config';

@Injectable()
export class OneSignalService {
  constructor(
    @Inject(oneSignalEnvs.KEY)
    private readonly oneSignalEnvsModule: ConfigType<typeof oneSignalEnvs>,
  ) { }

  async sendEmail({to, subject, body}: {to: string, subject: string, body: string}) {
    try {
      const response = await axios.post(
        `${this.oneSignalEnvsModule.baseUrl}/notifications`,
        {
          app_id: this.oneSignalEnvsModule.oneSignalAppId,
          include_email_tokens: [to],
          email_subject: subject,
          email_body: body,
        },
        {
          headers: {
            Authorization: `Key ${this.oneSignalEnvsModule.oneSignalApiKey}`,
            'Content-Type': 'application/json',
          },
        },
      )
      return response.data;
    } catch (error) {
      console.error('Erro ao enviar email:', error.response?.data || error.message);
      // throw new Error('Falha no envio do email');
    }
  }
}
