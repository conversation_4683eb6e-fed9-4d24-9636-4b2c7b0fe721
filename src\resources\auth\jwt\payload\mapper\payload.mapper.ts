import { User } from 'src/resources/users/entities/user.entity';
import { UserTokenPayload } from '../entities/user-token-payload.entity';

export class PayloadMapper {
  static toUserTokenPayload(
    user: User,
    activeAccountSecureId: string,
  ): UserTokenPayload {
    const accountData = user.userAccounts.find(
      (userAccount) => userAccount.account.secureId === activeAccountSecureId,
    );

    const viewPermissionsSlugs = [];
    for (const permission of accountData.permissions) {
      if (permission.slug.endsWith('_view')) {
        viewPermissionsSlugs.push(permission.slug);
      }
    }

    const planSlug = accountData.account?.subscriptions[0]?.plan?.slug || null;
    const trialEndsAt =
      planSlug === 'freemium'
        ? accountData.account?.subscriptions[0]?.trialEndsAt || null
        : null;

    const activeAccount: UserTokenPayload['activeAccount'] = {
      accountSecureId: accountData.account.secureId,
      companyName: accountData.account.companyName,
      userName: user.name,
      roleSlug: accountData.role.slug,

      viewPermissionsSlugs: viewPermissionsSlugs,

      trialEndAt: trialEndsAt,
      planSlug: planSlug,
    };

    return {
      subject: user.secureId,
      email: user.email,

      activeAccount: activeAccount,
    };
  }
}
