import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

@Injectable()
export class IsPlanActive {
  constructor(private readonly prismaService: PrismaService) {}

  // TODO: LEMBRAR DE REVER EXCEPTIONS
  async execute(planSecureId: string): Promise<void> {
    const planModel = await this.prismaService.plans.findFirst({
      where: { secureId: { equals: planSecureId } },
    });

    if (!planModel) {
      throw new NotFoundException('Plano não existe');
    }

    if (!planModel.isActive) {
      throw new BadRequestException('Plano não está ativo');
    }

    if (planModel.efiIsDeleted) {
      throw new BadRequestException('Plano excluído');
    }
  }
}
