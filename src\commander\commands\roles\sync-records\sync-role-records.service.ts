import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ISyncRecordCommand } from 'src/@shared/contracts/commands/services';
import { roles } from 'src/constants/roles';
import { Role } from 'src/resources/roles/entities/role.entity';

@Injectable()
export class SyncRoleRecordsService extends ISyncRecordCommand {
  entity: string = 'Roles';

  async execute(prisma: PrismaClient) {
    console.log('\x1b[32m', `Sincronizando ${this.entity}:`);

    for (const role of roles) {
      this.logRecord(role.name);
      await this.createRecord(role, prisma);
    }

    console.log('\x1b[32m', `\n${this.entity} sincronizado!\n`);
  }

  logRecord(itemName: string) {
    console.log('\x1b[32m', `\t${itemName}`);
  }

  async createRecord(record: Role, prisma: PrismaClient) {
    await prisma.roles.upsert({
      where: { slug: record.slug },
      create: {
        secureId: record.secureId,
        name: record.name,
        slug: record.slug,
        description: record.description,
      },
      update: {
        // secureId: record.secureId, // Esse campo foi desabilitado para não alterar o secureID quando alterar os dados quando fizer um update
        name: record.name,
        slug: record.slug,
        description: record.description,
      },
    });
  }
}
