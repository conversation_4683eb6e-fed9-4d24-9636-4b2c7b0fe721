/*
  Warnings:

  - You are about to drop the column `avatar_url` on the `chats` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[upload_id]` on the table `chats` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX `chats_name_key` ON `chats`;

-- AlterTable
ALTER TABLE `chats` DROP COLUMN `avatar_url`,
    ADD COLUMN `upload_id` INTEGER NULL;

-- CreateTable
CREATE TABLE `uploads` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `file_name` VARCHAR(255) NOT NULL,
    `file_type` VARCHAR(255) NOT NULL,
    `type` VARCHAR(255) NOT NULL,
    `url_cdn` VARCHAR(255) NOT NULL,
    `bucket` VARCHAR(255) NOT NULL,
    `is_private` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `uploads_id_key`(`id`),
    UNIQUE INDEX `uploads_secure_id_key`(`secure_id`),
    INDEX `uploads_id_secure_id_is_private_idx`(`id`, `secure_id`, `is_private`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `chats_upload_id_key` ON `chats`(`upload_id`);

-- AddForeignKey
ALTER TABLE `chats` ADD CONSTRAINT `chats_upload_id_fkey` FOREIGN KEY (`upload_id`) REFERENCES `uploads`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
