import { Inject, Injectable } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import storageEnvs from '../env.config';
import axios, { AxiosInstance } from 'axios';
import { ReplyToMessageDTO } from './dto/reply-to-message.dto';
import { OutputGetMediaUrlDTO } from './dto/output-get-media-url.dto';
import { getFileTypeWpp } from 'src/utils/getFileTypeWpp';
import { v4 as uuid } from 'uuid';

@Injectable()
export class FacebookGraphService {
  private readonly metaApiClient: AxiosInstance;

  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
  ) {
    this.metaApiClient = axios.create({
      baseURL: storageConfig.metaApiUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async markMessageAsRead(
    accessToken: string,
    messageId: string,
    numberId: string,
  ) {
    try {
      await this.metaApiClient.post(
        `${numberId}/messages `,
        {
          messaging_product: 'whatsapp',
          status: 'read',
          message_id: messageId,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      return;
    } catch (error) {
      throw new Error(`Failed to mark message as read: ${error.message}`);
    }
  }

  async replyToMessage(replyMessageDto: ReplyToMessageDTO) {
    try {
      const {
        recipientNumber,
        numberId,
        accessToken,
        messageContent,
        messageProduct,
      } = replyMessageDto;

      const response = await this.metaApiClient.post(
        `${numberId}/messages`,
        {
          messaging_product: messageProduct,
          to: recipientNumber,
          text: { body: messageContent },
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      return;
    } catch (error) {
      throw new Error(`Failed to send reply message: ${error.message}`);
    }
  }

  async getMediaUrl(accessToken: string, mediaId: string) {
    try {
      const response = await this.metaApiClient.get<OutputGetMediaUrlDTO>(
        `${mediaId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get media URL: ${error.message}`);
    }
  }

  async sendWhatsAppText(
    accessToken: string,
    numberId: string,
    recepientNumber: string,
    messageContent: string,
    messageToReply?: any,
  ) {
    try {
      const response = await this.metaApiClient.post(
        `${numberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: recepientNumber,
          type: 'text',
          text: { body: messageContent },
          // quoted: messageToReply,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(`Failed to send WhatsApp text: ${error.message}`);
    }
  }

  async sendWhatsAudio(
    accessToken: string,
    numberId: string,
    urlFile: string,
    mimeType: string,
    recipientNumber: string,
    messageToReply: any,
  ) {
    try {
      const fileResponse = await axios.get(urlFile, {
        responseType: 'arraybuffer',
      });

      const base64Data = Buffer.from(fileResponse.data, 'binary').toString(
        'base64',
      );

      const fileBlob = this.base64ToBlob(base64Data, mimeType);

      const formData = new FormData();
      formData.append('messaging_product', 'whatsapp');
      formData.append('file', fileBlob, 'audio.mp3');
      formData.append('type', mimeType);

      const mediaUploadResponse = await this.metaApiClient.post(
        `${numberId}/media`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      const mediaId = mediaUploadResponse.data.id;

      const response = await this.metaApiClient.post(
        `${numberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: recipientNumber,
          type: 'audio',
          audio: {
            id: mediaId,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(`Failed to send WhatsApp audio: ${error.message}`);
    }
  }

  async sendWhatsAppFile(
    accessToken: string,
    numberId: string,
    urlFile: string,
    mimeType: string,
    recipientNumber: string,
    message: string,
    messageToReply: any,
  ) {
    try {
      const fileResponse = await axios.get(urlFile, {
        responseType: 'arraybuffer',
      });

      const base64Data = Buffer.from(fileResponse.data, 'binary').toString(
        'base64',
      );

      const fileBlob = this.base64ToBlob(base64Data, mimeType);

      const contentType = getFileTypeWpp(mimeType);

      const fileExtension = mimeType.split('/')[1]?.split(';')[0] || 'bin';
      const fileName = `${uuid()}.${fileExtension}`;

      const formData = new FormData();
      formData.append('messaging_product', 'whatsapp');
      formData.append('file', fileBlob, fileName);
      formData.append('type', mimeType);

      const mediaUploadResponse = await this.metaApiClient.post(
        `${numberId}/media`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      const mediaId = mediaUploadResponse.data.id;

      // Send the message with the uploaded file
      const response = await this.metaApiClient.post(
        `${numberId}/messages`,
        {
          messaging_product: 'whatsapp',
          to: recipientNumber,
          type: contentType,
          document:
            contentType === 'document'
              ? {
                  id: mediaId,
                  caption: message,
                }
              : undefined,
          image:
            contentType === 'image'
              ? {
                  id: mediaId,
                  caption: message,
                }
              : undefined,
          audio:
            contentType === 'audio'
              ? {
                  id: mediaId,
                }
              : undefined,
          video:
            contentType === 'video'
              ? {
                  id: mediaId,
                  caption: message,
                }
              : undefined,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw new Error(`Failed to send WhatsApp file: ${error.message}`);
    }
  }

  base64ToBlob(base64String: string, contentType: string): Blob {
    // Remove data URL prefix if present
    const base64Data = base64String.includes('base64,')
      ? base64String.split('base64,')[1]
      : base64String;

    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');

    // Create a Blob from the buffer
    return new Blob([buffer], { type: contentType });
  }
}
