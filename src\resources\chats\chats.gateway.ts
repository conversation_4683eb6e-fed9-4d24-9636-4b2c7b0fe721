import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ChatsOrchestratorService } from './chats-orchestrator.service';
import { ChatsService } from './chats.service';

export interface ChatMessage {
  secureId: string;
  createdAt: Date;
  sessionId: string;
  receiveMessage?: string;
  sendMessage?: string;
}

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class ChatsGateway {
  @WebSocketServer()
  server: Server;

  private userConnections = new Map<
    string,
    { socketId: string; rooms: Set<string> }
  >();

  constructor(
    private readonly chatsOrchestratorService: ChatsOrchestratorService,
    private readonly chatServices: ChatsService,
  ) {}

  handleDisconnect(client: Socket) {
    for (const [userId, connection] of this.userConnections.entries()) {
      if (connection.socketId === client.id) {
        this.userConnections.delete(userId);
        break;
      }
    }
  }

  @SubscribeMessage('joinRoom')
  async handleJoinRoom(
    client: Socket,
    payload: { userId: string; sessionId: string },
  ) {
    try {
      const { userId, sessionId } = payload;

      client.join(sessionId);

      // Update user connections
      this.userConnections.set(userId, {
        socketId: client.id,
        rooms: new Set([
          ...(this.userConnections.get(userId)?.rooms || []),
          sessionId,
        ]),
      });

      // Notify room about new user
      this.server.to(sessionId).emit('roomJoined', {
        userId,
        timestamp: new Date(),
      });

      return { success: true };
    } catch (error) {
      return { error: 'Failed to join room' };
    }
  }

  @SubscribeMessage('leaveRoom')
  async handleLeaveRoom(
    client: Socket,
    payload: { userId: string; sessionId: string },
  ) {
    try {
      const { userId, sessionId } = payload;

      // Remove user from room
      client.leave(sessionId);

      // Update user connections
      const existingConnection = this.userConnections.get(userId);
      if (existingConnection) {
        existingConnection.rooms.delete(sessionId);
      }

      // Notify room about user leaving
      this.server.to(sessionId).emit('roomLeft', {
        userId,
        timestamp: new Date(),
      });

      return { success: true };
    } catch (error) {
      return { error: 'Failed to leave room' };
    }
  }

  @SubscribeMessage('sendMessage')
  async handleMessage(
    _client: Socket,
    payload: {
      userId?: string;
      sessionId: string;
      content: string;
      isAttendant: boolean;
      // type?: 'audio' | 'text' | 'image' | 'file';
      chatId?: string;
      customerName?: string;
      customerId?: string;
      replyTo?: string;
      fileSecureId?: string;
      // urlFile?: string;
    },
  ) {
    try {
      const {
        userId,
        sessionId,
        content,
        isAttendant,
        chatId,
        customerName,
        customerId,
        // type,
        replyTo,
      } = payload;

      const responsePayload =
        await this.chatsOrchestratorService.orchestrateMessageFlow({
          userId,
          sessionId,
          content,
          isAttendant,
          chatId,
          customerName,
          customerId,
          // type,
          replyTo,
          // urlFile: payload.urlFile,
          fileSecureId: payload.fileSecureId,
        });

      if (
        responsePayload?.error ||
        !responsePayload?.messageToSend ||
        !responsePayload
      ) {
        return {
          error: responsePayload.error
            ? responsePayload.error
            : 'Failed to send message',
        };
      }

      const { messageToSend } = responsePayload;

      this.server.to(payload.sessionId).emit('newMessage', messageToSend);

      return { success: true, messageToSend };
    } catch (error) {
      console.error('Error in handleMessage:', error);
      return { error: 'Failed to send message' };
    }
  }

  @SubscribeMessage('attendantInterrupt')
  async handleAttendantInterrupt(
    _client: Socket,
    payload: {
      sessionSecureId: string;
      attendantSecureId: string;
    },
  ) {
    try {
      await this.chatServices.attendantInterrupt(
        payload.sessionSecureId,
        payload.attendantSecureId,
      );

      this.server.to(payload.sessionSecureId).emit('attendantAssignedChat');

      return { success: true };
    } catch (e) {
      throw new Error('Failed to interrupt chat');
    }
  }

  broadcastChatMessageUpdate({
    createdAt,
    receiveMessage,
    sendMessage,
    messageDirection,
    secureId,
    sessionId,
    type = 'text',
    urlFile,
    replyTo,
  }: {
    secureId: string;
    type?: 'audio' | 'text' | 'image' | 'file';
    receiveMessage?: string;
    messageDirection?: string;
    sendMessage?: string;
    sessionId: string;
    createdAt: Date;
    urlFile?: string;
    replyTo?: string;
  }) {
    this.server.to(sessionId).emit('newMessage', {
      secureId,
      createdAt,
      sessionId,
      messageType: type,
      messageDirection,
      receiveMessage,
      sendMessage,
      urlFile,
      replyTo,
    });

    return { success: true };
  }
}
