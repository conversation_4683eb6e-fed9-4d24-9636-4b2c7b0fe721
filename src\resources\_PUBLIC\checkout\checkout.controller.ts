import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { CheckoutService } from './checkout.service';
import { CreateCheckoutDto } from './dto/create-checkout.dto';
import { UpdateCheckoutDto } from './dto/update-checkout.dto';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { IsPlanActive } from 'src/@shared/helpers/validation/plans/is-plan-active';
import { CheckoutValidator } from './validation/checkout.validator';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';

@Controller('checkout')
export class CheckoutController {
  constructor(
    private readonly checkoutService: CheckoutService,
    private readonly planValidation: IsPlanActive,
    private readonly prismaService: PrismaService,
  ) { }

  // @Post()
  // async create(@Body() createCheckoutDto: CreateCheckoutDto) {
  //   await this.planValidation.execute(createCheckoutDto.planSecureId);
  //   await CheckoutValidator.execute(
  //     this.prismaService,
  //     createCheckoutDto.userSecureId,
  //     createCheckoutDto.accountSecureId,
  //   );

  //   return this.checkoutService.create(createCheckoutDto);
  // }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Post()
  async create(
    @Body() createCheckoutDto: CreateCheckoutDto,
    @AccountIdExtractor() accountId: number,
    @UserIdExtractorDecorator() userId: number,
  ) {
    await this.planValidation.execute(createCheckoutDto.planSecureId);
    await CheckoutValidator.execute(
      this.prismaService,
      userId,
      accountId,
    );

    return this.checkoutService.create(
      createCheckoutDto,
      userId,
      accountId,
    );
  }
}
