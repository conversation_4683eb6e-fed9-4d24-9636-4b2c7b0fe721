import {
  ApiOperationOptions,
  ApiQueryOptions,
  ApiResponseNoStatusOptions,
} from '@nestjs/swagger';
import { GetUserOutputDto } from '../../dto/get-user-output.dto';

type UserDelete = {
  apiOperation: ApiOperationOptions;
  apiQuerySecureId: ApiQueryOptions;
};

type UserUpdate = {
  apiOperation: ApiOperationOptions;
  apiQuerySecureId: ApiQueryOptions;
  apiBadRequest: ApiResponseNoStatusOptions;
  apiNotFound: ApiResponseNoStatusOptions;
  apiConflict: ApiResponseNoStatusOptions;
};

type FindUserBySecureId = {
  apiOperation: ApiOperationOptions;
  apiQuerySecureId: ApiQueryOptions;
  apiNotFound: ApiResponseNoStatusOptions;
  apiResponse: ApiResponseNoStatusOptions;
};

type FindAllUsers = {
  apiOperation: ApiOperationOptions;
  apiQueryName: ApiQueryOptions;
  apiQueryEmail: ApiQueryOptions;
  apiOkResponse: ApiResponseNoStatusOptions;
};

const findAllUsers: FindAllUsers = {
  apiOperation: {
    summary: 'Faz Busca paginada de usuários',
    description:
      'Utilizando uma conta de Backoffice, é possível buscar usuários. É necessário ter _permission_ de **user_view**.',
  },

  apiQueryName: {
    name: 'name',
    required: false,
    type: String,
    description: 'Nome do usuário que deseja buscar',
    example: 'Felipe Scola',
  },

  apiQueryEmail: {
    name: 'email',
    required: false,
    type: String,
    description: 'Email do usuário que deseja buscar',
    example: '<EMAIL>',
  },

  apiOkResponse: {
    examples: {
      usersFound: {
        summary: 'Usuários encontrados',
        value: {
          meta: {
            totalItems: 4,
            totalPages: 1,
            currentPage: 1,
            itemsPerPage: 15,
          },
          data: [
            {
              secureId: '86a5d822-0e1e-4522-959b-9db67e0071d0',
              email: '<EMAIL>',
              name: 'Rodrigo Brando',
              cpf: '123.456.789-00',
              isActive: true,
              createdAt: '2024-12-23T13:39:32.848Z',
              updatedAt: '2024-12-23T13:39:32.848Z',
            },
            {
              secureId: '8a270583-09a6-4aa4-ac3f-e2cb009b4321',
              email: '<EMAIL>',
              name: 'Felipe Scola',
              cpf: '892.541.340-07',
              isActive: true,
              createdAt: '2024-12-23T16:12:29.518Z',
              updatedAt: '2024-12-23T16:12:29.518Z',
            },
            {
              secureId: 'cd13c4ec-44f4-419f-a45e-66d351911b46',
              email: '<EMAIL>',
              name: 'Joao Jorge',
              cpf: '117.391.950-35',
              isActive: true,
              createdAt: '2024-12-23T16:22:38.608Z',
              updatedAt: '2024-12-23T16:22:38.608Z',
            },
            {
              secureId: '176e94fa-3404-464c-adb0-765dc334f8dd',
              email: '<EMAIL>',
              name: 'Juninho Goiaba alterada',
              cpf: '486.386.730-19',
              isActive: true,
              createdAt: '2024-12-24T14:39:30.534Z',
              updatedAt: '2024-12-27T12:05:48.849Z',
            },
          ],
        },
      },

      noUsersFound: {
        summary: 'Nenhum usuário encontrado',
        value: {
          meta: {
            totalItems: 0,
            totalPages: 1,
            currentPage: 1,
            itemsPerPage: 15,
          },
          data: [],
        },
      },
    },
  },
};

const findUserBySecureId: FindUserBySecureId = {
  apiOperation: {
    summary: 'Busca um usuário específico pelo SecureId',
    description:
      'Utilizando uma conta de Backoffice, é possível buscar um usuário pelo seu _SecureId_. É necessário ter _permission_ de **user_view**.',
  },

  apiQuerySecureId: {
    name: 'secureId',
    required: true,
    type: String,
    description: 'SecureId do usuário que deseja buscar',
    example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
  },

  apiNotFound: {
    examples: {
      userNotFound: {
        summary: 'SecureId de usuário não encontrado, Usuário não encontrado.',
        value: {
          message: 'Usuário não encontrado',
          error: 'Not Found',
          statusCode: 404,
        },
      },
    },
  },

  apiResponse: {
    example: {
      secureId: '8a270583-09a6-4aa4-ac3f-e2cb009b4321',
      email: '<EMAIL>',
      name: 'Felipe Scola',
      cpf: '892.541.340-07',
      accounts: [
        {
          secureId: '9f44edb6-afb0-48f6-81f0-df755ee7c093',
          companyName: 'Scolinha FPS',
          isOwner: true,
          permissions: [
            {
              accountPermissionSecureId: '2c0569de-f829-403f-af2b-87ec8a9b3370',
              name: 'App_View',
              slug: 'app_view',
              group: 'app',
              description:
                'Usuário pode ver a listagem de utilizador do sistema',
              createdAt: '2024-12-23T13:39:32.610Z',
              updatedAt: '2025-01-06T17:10:28.838Z',
            },
            {
              accountPermissionSecureId: '51b360bb-66bc-461c-80ac-ed48b683bd17',
              name: 'App_Create',
              slug: 'app_create',
              group: 'app',
              description: 'Usuário pode criar utilizador do sistema',
              createdAt: '2024-12-23T13:39:32.621Z',
              updatedAt: '2025-01-06T17:10:28.846Z',
            },
            {
              accountPermissionSecureId: '4f6125d0-1d55-4fd3-ac0f-d43902c60e3c',
              name: 'App_Edit',
              slug: 'app_edit',
              group: 'app',
              description: 'Usuário pode editar um utilizador do sistema',
              createdAt: '2024-12-23T13:39:32.631Z',
              updatedAt: '2025-01-06T17:10:28.855Z',
            },
            {
              accountPermissionSecureId: 'ea8ba491-e7f6-40fe-89aa-752dc6cb0bf6',
              name: 'App_Delete',
              slug: 'app_delete',
              group: 'app',
              description: 'Usuário pode apagar um utilizador do sistema',
              createdAt: '2024-12-23T13:39:32.642Z',
              updatedAt: '2025-01-06T17:10:28.863Z',
            },
          ],
          createdAt: '2024-12-23T16:12:29.506Z',
          updatedAt: '2024-12-23T16:12:29.506Z',
        },
      ],
      isActive: true,
      createdAt: '2024-12-23T16:12:29.518Z',
      updatedAt: '2024-12-23T16:12:29.518Z',
    },
    description: 'Retorna um usuário com suas contas e permissões',
    type: GetUserOutputDto,
  },
};

const userUpdate: UserUpdate = {
  apiOperation: {
    summary: 'Atualiza um usuário pelo SecureId',
    description:
      'Utilizando uma conta de Backoffice, é possível atualizar um usuário pelo seu _SecureId_. É necessário ter _permission_ de **user_edit**.',
  },

  apiQuerySecureId: {
    name: 'secureId',
    required: true,
    type: String,
    description: 'SecureId do usuário que deseja atualizar',
    example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
  },

  apiBadRequest: {
    examples: {
      noBody: {
        summary: 'Quando enviado um objeto vazio. Ou seja sem body',
        value: {
          message:
            'O corpo da requisição não pode estar vazio. Você deve enviar ao menos uam propriedade para atualizar um usuário.',
          error: 'Bad Request',
          statusCode: 400,
        },
      },
    },
  },

  apiNotFound: {
    examples: {
      userNotFound: {
        summary: 'SecureId de usuário não encontrado, Usuário não encontrado.',
        value: {
          message: 'Usuário não encontrado',
          error: 'Not Found',
          statusCode: 404,
        },
      },

      accountNotFound: {
        summary: 'SecureId de Account não encontrado, Conta não encontrada',
        value: {
          message: 'Conta não encontrada',
          error: 'Not Found',
          statusCode: 404,
        },
      },
    },
  },

  apiConflict: {
    examples: {
      emailAlreadyInUse: {
        summary: 'Quando já existe um usuário cadastrado com o email passado',
        value: {
          message: 'Este e-mail já está em uso por outro usuário',
          error: 'Conflict',
          statusCode: 409,
        },
      },

      cpfAlreadyInUse: {
        summary: 'Quando já existe um usuário cadastrado com o cpf passado',
        value: {
          message: 'Este CPF já está em uso por outro usuário',
          error: 'Conflict',
          statusCode: 409,
        },
      },
    },
  },
};

const userDelete: UserDelete = {
  apiOperation: {
    summary: 'Remove um usuário pelo SecureId',
    description:
      'Utilizando uma conta de Backoffice, é possível remover um usuário pelo seu _SecureId_. É necessário ter _permission_ de **user_delete**.',
  },

  apiQuerySecureId: {
    name: 'secureId',
    required: true,
    type: String,
    description: 'SecureId do usuário que deseja remover',
    example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
  },
};

export const backofficeUsersSwaggerApi = {
  findAllUsers,
  findUserBySecureId,
  userUpdate,
  userDelete,
};
