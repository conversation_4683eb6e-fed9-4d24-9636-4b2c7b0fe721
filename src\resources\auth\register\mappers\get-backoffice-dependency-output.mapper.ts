import { GetBackofficeDependenciesCustomFormerDto } from '../dto/get-backoffice-dependencies-custom-former.dto';
import { GetBackofficeDependenciesOutputDto } from '../dto/get-backoffice-dependencies-output.dto';

export class getBackofficeDependencyOutputMapper {
  static fromCustomDtoToGetBackofficeDependencyOutputDto(
    customDto: GetBackofficeDependenciesCustomFormerDto,
  ): GetBackofficeDependenciesOutputDto {
    return {
      permissions: customDto.permissionsModel.map((permission) => {
        return {
          secureId: permission.secureId,
          slug: permission.slug,
          name: permission.name,
          group: permission.group,

          description: permission.description,

          createdAt: permission.createdAt,
          updatedAt: permission.updatedAt,
        };
      }),
    };
  }
}
