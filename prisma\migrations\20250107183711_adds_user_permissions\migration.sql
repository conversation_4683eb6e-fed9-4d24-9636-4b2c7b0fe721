-- AlterTable
ALTER TABLE `permissions` MODIFY `name` ENUM('Backoffice View', 'Backoffice Create', 'Backoffice Edit', 'Backoffice Delete', 'App View', 'App Create', 'App Edit', 'App Delete', 'Account View', 'Account Create', 'Account Edit', 'Account Delete', 'Evo Integration View', 'Evo Integration Create', 'Evo Integration Edit', 'Evo Integration Delete', 'Attendant View', 'Attendant Create', 'Attendant Edit', 'Attendant Delete', 'Chat View', 'Chat Create', 'Dashboard View', 'Configuration View', 'Configuration Create', 'Configuration Edit', 'Configuration Delete', 'Configuration WhatsApp View', 'Configuration WhatsApp Create', 'Configuration WhatsApp Edit', 'Configuration WhatsApp Delete', 'Configuration WebChat View', 'Configuration WebChat Create', 'Configuration WebChat Edit', 'Configuration WebChat Delete', 'Configuration ChatBot View', 'Configuration ChatBot Create', 'Configuration ChatBot Edit', 'Configuration ChatBot Delete', 'Configuration Attendant View', 'Configuration Attendant Create', 'Configuration Attendant Edit', 'Configuration Attendant Delete', 'User View', 'User Create', 'User Edit', 'User Delete') NOT NULL,
    MODIFY `slug` ENUM('backoffice_view', 'backoffice_create', 'backoffice_edit', 'backoffice_delete', 'app_view', 'app_create', 'app_edit', 'app_delete', 'account_view', 'account_create', 'account_edit', 'account_delete', 'evo_integration_view', 'evo_integration_create', 'evo_integration_edit', 'evo_integration_delete', 'attendant_view', 'attendant_create', 'attendant_edit', 'attendant_delete', 'chat_view', 'chat_create', 'dashboard_view', 'configuration_view', 'configuration_create', 'configuration_edit', 'configuration_delete', 'configuration_whatsapp_view', 'configuration_whatsapp_create', 'configuration_whatsapp_edit', 'configuration_whatsapp_delete', 'configuration_webchat_view', 'configuration_webchat_create', 'configuration_webchat_edit', 'configuration_webchat_delete', 'configuration_chatbot_view', 'configuration_chatbot_create', 'configuration_chatbot_edit', 'configuration_chatbot_delete', 'configuration_attendant_view', 'configuration_attendant_create', 'configuration_attendant_edit', 'configuration_attendant_delete', 'user_view', 'user_create', 'user_edit', 'user_delete') NOT NULL,
    MODIFY `group` ENUM('backoffice', 'app', 'account', 'evo_integration', 'attendant', 'chat', 'dashboard', 'configuration', 'configuration_whatsapp', 'configuration_webchat', 'configuration_chatbot', 'configuration_attendant', 'user') NOT NULL;
