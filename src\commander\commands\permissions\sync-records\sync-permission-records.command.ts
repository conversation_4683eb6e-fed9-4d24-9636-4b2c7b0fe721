import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';
import { SyncPermissionRecordsService } from './sync-permission-records.service';

@Command({
  name: 'sync-permissions',
  description:
    'Sincroniza as permissions com base no arquivo das constants. Caso as permissions não existam ele irá gerar elas. Caso elas existam ele irá atualizar elas com base no slug.',
})
export class SyncPermissionRecordsCommand extends CommandRunner {
  prisma: PrismaClient;

  constructor(private readonly syncPermissions: SyncPermissionRecordsService) {
    super();
  }

  async run() {
    this.prisma = new PrismaClient();

    this.syncPermissions
      .execute(this.prisma)
      .then(() => this.prisma.$disconnect());
  }
}
