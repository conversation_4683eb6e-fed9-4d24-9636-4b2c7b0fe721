import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { EFIPlansService } from 'src/third-party/efi/plans/efi-plans.service';
import { PaginationModelHelper } from 'src/@shared/helpers/pagination/pagination.helper';

import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';

import { Plan } from './entities/plan.entity';

import { PlanMapper } from './mapper/plan.mapper';

import { PlanFactory } from './factory/plan.factory';

import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';

import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';

@Injectable()
export class PlansService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly efiPlanService: EFIPlansService,
  ) { }

  async create(createPlanDto: CreatePlanDto): Promise<void> {
    const planNameAlreadyExists = await this.findRecordByPlanName(
      createPlanDto.name,
    );
    if (planNameAlreadyExists) {
      throw new ConflictException('Já existe um plano com esse nome');
    }

    const planSlugAlreadyExists = await this.findRecordByPlanSlug(
      createPlanDto.slug,
    );
    if (planSlugAlreadyExists) {
      throw new ConflictException('Já existe um plano com esse slug');
    }

    const plan = new Plan(createPlanDto);

    const efiPlanId = await this.efiPlanService.createPlan({
      interval: plan.interval,
      name: plan.name,
    });
    plan.efiPlanId = efiPlanId;

    const modelPlan = PlanMapper.fromEntityToModel(plan);

    // NOTE: Aqui, pequeno gafanhoto, é feito esse tryCatch para que se por algum motivo der algum erro na criação do plano após criar dentro do EFI, ele deleta o plano criado no EFI
    try {
      await this.prismaService.plans.create({
        data: modelPlan,
      });
    } catch (error) {
      if (efiPlanId) {
        await this.efiPlanService.deletePlan({ planId: efiPlanId });
      }

      console.log(error);
      throw new BadRequestException('Erro ao tentar criar um plano');
    }

    return;
  }

  async findAll(
    paginationQuery: PaginationQueryType,
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
  ): Promise<IWithPagination<Plan>> {
    const skip = (paginationQuery.page - 1) * paginationQuery.limit;
    const paginationHelper = new PaginationModelHelper();

    // DRY
    const whereClause = {
      isActive: isActiveIsDeletedQuery.isActive,
      name: {
        contains: paginationQuery?.search && paginationQuery.search,
      },
    };

    const plans = await this.prismaService.plans.findMany({
      take: paginationQuery.limit,
      skip: skip,
      orderBy: {
        name: 'asc',
      },
      where: whereClause,
    });

    const meta = await paginationHelper.getPaginationModelMeta(
      this.prismaService.plans,
      {
        limit: paginationQuery.limit,
        page: paginationQuery.page,
        where: whereClause,
      },
    );

    const formattedPlans = PlanFactory.convertBatchFromModelToPlan(plans);

    return {
      meta: meta,
      data: !formattedPlans ? [] : formattedPlans,
    };
  }

  async findOne(slug: string): Promise<Plan> {
    const planModel = await this.prismaService.plans.findFirst({
      where: { slug: slug },
    });
    if (!planModel) {
      throw new NotFoundException('Plano não encontrado');
    }

    const planOutput = PlanMapper.fromModelToOutput(planModel);

    return planOutput;
  }

  async update(secureId: string, updatePlanDto: UpdatePlanDto): Promise<void> {
    if (Object.keys(updatePlanDto).length === 0) {
      throw new BadRequestException(
        'Dados inválidos. É necessário ter ao menos 1 item dentro do objeto',
      );
    }

    const planModel = await this.prismaService.plans.findFirst({
      where: { secureId: secureId },
      select: {
        name: true,
        slug: true,
        efiPlanId: true,
      },
    });
    if (!planModel) {
      throw new NotFoundException('Plano não encontrado');
    }

    if (updatePlanDto.name) {
      const planNameAlreadyExists = await this.findRecordByPlanName(
        updatePlanDto.name,
      );
      if (planNameAlreadyExists && planModel.name !== updatePlanDto.name) {
        throw new ConflictException('Já existe um plano com esse nome');
      }
    }

    if (updatePlanDto.slug) {
      const planSlugAlreadyExists = await this.findRecordByPlanSlug(
        updatePlanDto.slug,
      );
      if (planSlugAlreadyExists && planModel.slug !== updatePlanDto.slug) {
        throw new ConflictException('Já existe um plano com esse slug');
      }
    }

    const newPlan = PlanMapper.fromUpdateDtoToModel(updatePlanDto);

    await this.prismaService.plans.update({
      where: { secureId: secureId },
      data: newPlan,
    });

    // NOTE: Pequeno gafanhoto, esse cara foi deixado aqui pra baixo, pq garante que se ele chegou até aqui o plano já foi gravado no banco.
    if (updatePlanDto.name) {
      await this.efiPlanService.changePlanName({
        name: updatePlanDto.name,
        planId: planModel.efiPlanId,
      });
    }

    return;
  }

  async remove(secureId: string): Promise<void> {
    const plan = await this.prismaService.plans.findFirst({
      where: { secureId: { equals: secureId } },
    });
    if (!plan) {
      throw new NotFoundException('Plano não encontrado');
    }
    const today = new Date();
    const formattedToday = `${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;
    const formattedTodayTime = `${today.getHours()}:${today.getMinutes() < 10 ? today.getMinutes().toString().padStart(2, '0') : today.getMinutes()}:${today.getSeconds()}`;

    const newName = `${plan.name} - excluído em: ${formattedToday} às ${formattedTodayTime}`;
    const newSlug = `${plan.slug}-excluído-em-${formattedToday.replaceAll('/', '-')}-${formattedTodayTime.replaceAll(':', '-')}`;

    const planEntity = PlanMapper.fromModelToOutput(plan);
    planEntity.deactivate();
    planEntity.efiDelete();

    const planModel = PlanMapper.fromEntityToModel(planEntity);

    await this.prismaService.plans.update({
      where: {
        secureId: secureId,
      },
      data: { ...planModel, name: newName, slug: newSlug },
    });

    try {
      await this.efiPlanService.deletePlan({ planId: plan.efiPlanId });
    } catch (error) {
      planEntity.active();
      planEntity.efiRestore();

      const planModel = PlanMapper.fromEntityToModel(planEntity);

      await this.prismaService.plans.update({
        where: {
          secureId: secureId,
        },
        data: planModel,
      });

      console.log(error);
      throw new BadRequestException('Erro ao tentar deletar um plano');
    }
  }

  private async findRecordByPlanName(planName: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM plans WHERE name = ?) AS has;`,
      planName,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }

  private async findRecordByPlanSlug(planSlug: string): Promise<boolean> {
    const rawQueryResult = await this.prismaService.$queryRawUnsafe(
      `SELECT EXISTS (SELECT 1 FROM plans WHERE slug = ?) AS has;`,
      planSlug,
    );

    return Boolean(Number(rawQueryResult[0]?.has || 0));
  }
}
