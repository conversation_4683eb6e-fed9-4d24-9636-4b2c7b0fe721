import { Role } from 'src/resources/roles/entities/role.entity';
import { v4 as uuidV4 } from 'uuid';

const masterRole: Role = new Role({
  secureId: uuidV4(),
  name: 'Master',
  slug: 'MASTER',
  description:
    'Perfil de usuário master, com acesso total ao sistema. Este perfil não pode ser removido.',
});

const backfficeRole: Role = new Role({
  secureId: uuidV4(),
  name: 'Backoffice',
  slug: 'BACKOFFICE',
  description:
    'Perfil de usuário administrador, com acesso total ao sistema. Este perfil não pode ser removido.',
});

const appRole: Role = new Role({
  secureId: uuidV4(),
  name: 'App',
  slug: 'APP',
  description: 'Perfil de cliente do parceiro, com acesso ao sistema.',
});

const attendantRole: Role = new Role({
  secureId: uuidV4(),
  name: 'Attendant',
  slug: 'ATTENDANT',
  description:
    'Per<PERSON><PERSON> de atendente do cliente, com acesso limitado ao relatórios apenas.',
});

export const roles: Role[] = [
  masterRole,
  backfficeRole,
  appRole,
  attendantRole,
];
