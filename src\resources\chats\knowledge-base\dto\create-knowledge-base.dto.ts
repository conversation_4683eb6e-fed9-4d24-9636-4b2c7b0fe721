import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateKnowledgeBaseDto {
  @IsNotEmpty({ message: 'Quantidade de chunk é obrigatória' })
  chunkSize: number;

  @IsNotEmpty({ message: 'Quantidade de chunk overlap é obrigatória' })
  chunkOverlap: number;

  @IsNotEmpty({ message: 'O identificador do chatbot é obrigatório' })
  chatbotSecureId: string;
}
