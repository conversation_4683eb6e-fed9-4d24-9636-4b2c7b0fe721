const name = {
  description: 'Nome do usuário',
  required: false,
  uniqueItems: false,
  maxLength: 255,
  minLength: 3,
  type: String,
  example: '<PERSON>',
};

const email = {
  description: 'Email do usuário. Deve ser um e-mail válido',
  required: false,
  uniqueItems: true,
  maxLength: 255,
  minLength: 3,
  type: String,
  example: 'john.marston@blackwaterbank.rdr2',
};

const cpf = {
  description: 'CPF do usuário. Deve ser um CPF válido',
  required: false,
  uniqueItems: true,
  maxLength: 11,
  minLength: 11,
  type: String,
  example: '123.456.789-01',
};

const password = {
  description: 'Senha do usuário',
  required: false,
  uniqueItems: false,
  maxLength: 100,
  minLength: 6,
  type: String,
  example: 'Be greedy only for foresight',
};

const isActive = {
  description: 'Define se o usuário está ativo ou não',
  required: false,
  type: Boolean,
  example: true,
};

const selectedAccount = {
  description: 'Conta do usuário selecionada para aplicar as alterações',
  type: 'object',
  required: ['false'],
  properties: {
    secureId: {
      description: 'SecureId da conta selecionada',
      required: true,
      uniqueItems: true,
      type: 'string',
      example: 'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
    },

    companyName: {
      description: 'Nome da empresa',
      required: false,
      uniqueItems: false,
      type: 'string',
      example: 'BlackWater Bank',
    },

    permissions: {
      description: 'Array de strings contendo os secureIds das permissions',
      required: false,
      uniqueItems: true,
      type: 'array',
      example: [
        'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
        'f5e1b3e9-0d2b-4b8f-8b8d-1f1e1c7f8e2e',
      ],
    },
  },
};

export const updateUser = {
  name,
  email,
  cpf,
  password,
  isActive,

  selectedAccount,
};
