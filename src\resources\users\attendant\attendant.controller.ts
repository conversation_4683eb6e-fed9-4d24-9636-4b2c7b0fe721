import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';

import { AttendantService } from './attendant.service';

import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';

import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { CheckEmptyBodyGuard } from 'src/@shared/guards/body/body.guard';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { CanAddNewAttendantGuard } from 'src/@shared/guards/plan/attendant.guard';
import { SubscriptionGuard } from 'src/@shared/guards/subscription/subscription.guard';
import { DoesAttendantBelongsToClientAccountGuard } from 'src/@shared/guards/attendant/does-attendant-belongs-to-client-account.guard';
import { DoesAttendantBelongsToClientAccountAndIsActiveGuard } from 'src/@shared/guards/attendant/does-attendant-belongs-to-client-account-and-is-active.guard';

import { UpdateAttendantDto } from './dto/update-attendant.dto';
import { AttendantOutputDto } from './dto/attendant-output.dto';

import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';
import { PreventUserSelfAccountDeletionGuard } from 'src/@shared/guards/prevent-self-deletion/prevent-user-self-account-deletion.guard';

@Controller('attendant')
export class AttendantController {
  constructor(private readonly attendantService: AttendantService) {}

  // NOTE: Gafanhotenho isso qui é uma documentação interna do TS, esse cara aqui é o responsável por documentar internamente os métodos e funções.

  /**
   * @description Endpoint para listar todos os atendentes de uma conta. Essa conta é extraída do token JWT.
   *
   * Nesse caso tanto search quanto is deleted não são passados pois isso é feito no service. Is deleted deve ser false para poder trazer apenas os usuário não excluídos e search não faz sentido já que name e email são passados separadamente
   *
   * @param paginationQuery (opcional) Query de paginação: { page: number, limit: number }
   * @param isActiveIsDeletedQuery  (opcional) Query de ativo e deletado: { isActive: boolean }
   * @param accountId  (extraído do token JWT) NÃO DEVE SER PASSADO!
   * @param name (opcional) Nome do atendente
   * @param email (opcional) Email do atendente
   * @returns {IWithPagination<AttendantOutputDto>} Lista de atendentes
   */
  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_attendant_view', 'attendant_chat_view'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountIdExtractor() accountId: number,
    @Query('name') name?: string,
    @Query('email') email?: string,
    @Query('onlyInRotation') onlyInRotation?: boolean,
  ): Promise<IWithPagination<AttendantOutputDto>> {
    return await this.attendantService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountId,
      name,
      email,
      onlyInRotation,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_attendant_view'])
  @Get(':secureId')
  async findOne(
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
  ): Promise<AttendantOutputDto> {
    return await this.attendantService.findOne(secureId, accountId);
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
    SubscriptionGuard,
    DoesAttendantBelongsToClientAccountGuard,
    new CheckEmptyBodyGuard(
      'Para atualizar um atendente, é necessário enviar ao menos um campo para atualização.',
    ),
  )
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_edit', 'attendant_attendant_edit'])
  @Put(':secureId')
  async updateAttendant(
    @Param('secureId') secureId: string,
    @Body() updateAttendantDto: UpdateAttendantDto,
    @Res() response: Response,
    @AccountIdExtractor() accountId: number,
    @UserIdExtractorDecorator() userId: number,
  ) {
    await this.attendantService.updateAttendant(
      secureId,
      updateAttendantDto,
      accountId,
      userId,
    );

    response.status(204).send();
  }

  @UseGuards(
    JwtGuard,
    RoleGuard,
    PermissionsGuard,
    SubscriptionGuard,
    CanAddNewAttendantGuard,
    DoesAttendantBelongsToClientAccountAndIsActiveGuard,
    PreventUserSelfAccountDeletionGuard,
  )
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_delete', 'attendant_attendant_delete'])
  @Delete(':secureId')
  async deleteAttendant(
    @Param('secureId') secureId: string,
    @Res() response: Response,
    @AccountIdExtractor() accountId: number,
  ) {
    await this.attendantService.deleteAttendant(secureId, accountId);

    response.status(204).send();
  }
}
