import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { ChatSessionsService } from './chat-sessions.service';
import { ChatSessionsController } from './chat-sessions.controller';

@Module({
  imports: [PrismaModule],
  controllers: [ChatSessionsController],
  providers: [ChatSessionsService],
})
export class ChatSessionsModule {}
