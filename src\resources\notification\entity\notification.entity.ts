import { Entity } from 'src/@shared/contracts/entity/interface';
import { v4 as uuid } from 'uuid';

type NotificationJson = {
  secureId: string;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
};

type NotificationConstructorProps = {
  id?: number;
  secureId?: string;

  title: string;
  message: string;

  isRead?: boolean;

  userAccountId?: number;
  sessionId?: number;

  accountId: number;

  createdAt?: Date;
};

export class NotificationEntity implements Entity {
  id?: number;
  secureId: string;
  title: string;
  message: string;
  isRead: boolean;
  userAccountId?: number;
  sessionId?: number;
  accountId: number;
  createdAt: Date;

  constructor(props: NotificationConstructorProps) {
    this.id = props?.id ? props.id : undefined;
    this.secureId = props?.secureId ? props.secureId : uuid();

    this.title = props.title;
    this.message = props.message;
    this.isRead = props.isRead ?? false;

    this.userAccountId = props.userAccountId;
    this.sessionId = props.sessionId;
    this.accountId = props.accountId;

    this.createdAt = props?.createdAt ? props.createdAt : new Date();
  }

  toJSON() {
    return {
      secureId: this.secureId,
      title: this.title,
      message: this.message,
      isRead: this.isRead,
      createdAt: this.createdAt,
    } as NotificationJson;
  }
}
