import { Users as UserModel } from '@prisma/client';
import { InternalServerErrorException } from '@nestjs/common';

import { User } from '../entities/user.entity';
import { Role } from 'src/resources/roles/entities/role.entity';
import { Account } from 'src/resources/_BACKOFFICE/accounts/entities/account.entity';
import { Permission } from 'src/resources/permissions/entities/permission.entity';

import type { UserWithUsersAccountModel } from './type';

export class UserMapper {
  static fromModelToEntity(model: UserWithUsersAccountModel): User {
    if (!model?.usersAccounts) {
      throw new InternalServerErrorException(
        'User malformed, Contata o administrador',
      );
    }

    const user = new User({
      ...model,

      userAccounts: model.usersAccounts.map((userAccount) => ({
        account: new Account(userAccount.account),
        role: new Role(userAccount.role),
        permissions: userAccount.accountsPermissions.map(
          (accountPermission) => new Permission(accountPermission.permission),
        ),
      })),
    });

    return user;
  }

  static fromEntityToModel(entity: User): UserModel {
    const userModel = {
      secureId: entity.secureId,
      cpf: entity.cpf,
      email: entity.email,
      name: entity.name,
      password: entity.password,
      cellPhone: entity.cellPhone,
      isActive: entity.isActive,
      isDeleted: entity.isDeleted,
    };

    return userModel as UserModel;
  }
}
