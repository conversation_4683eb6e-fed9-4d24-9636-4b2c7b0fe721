import { AxiosInstance } from 'axios';
import { ConfigType } from '@nestjs/config';
import {
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { InstanceQRCode } from './entities/instance-qrcode.entity';
import { CreateEvoInstance } from './entities/create-instance.entity';

import { EvoCreateInstanceOutputDto } from './dto/evo-create-instance.output.dto';
import { EvoGetInstanceStateInputDto } from './dto/evo-get-instance-state-input.dto';

import envModule from './env.config';

import { EvoInstanceCreateOptions } from './contracts/instance-create.contract';

@Injectable()
export class EvoInstancesService {
  constructor(
    @Inject('EVO-API')
    private readonly evoApi: AxiosInstance,

    @Inject(envModule.KEY)
    private readonly env: ConfigType<typeof envModule>,
  ) {}

  async create(
    createEvoInstance: CreateEvoInstance,
  ): Promise<EvoCreateInstanceOutputDto> {
    const defaultOptions = {
      qrcode: true,
      integration: 'WHATSAPP-BAILEYS',
      groupsIgnore: true,
      webhook: {
        url: this.env.webhookUrl,
        base64: true,
        events: ['MESSAGES_UPSERT', 'CONNECTION_UPDATE'],
      },
    };

    const body = {
      instanceName: createEvoInstance.name,
      number: createEvoInstance.number,
      ...defaultOptions,
    };

    const response = await this.evoApi
      .post<EvoCreateInstanceOutputDto>(this.env.createInstanceEP, body)
      .catch((error) => {
        console.error(error);
        throw new ConflictException('Número já está em uso');
      });

    return response.data;
  }

  async remove(instanceName: string) {
    try {
      await this.evoApi.delete(`${this.env.logoutInstanceEP}/${instanceName}`);
      await this.evoApi.delete(`${this.env.deleteInstanceEP}/${instanceName}`);
    } catch (e) {
      console.error(e);
    }
  }

  async logout(instanceName: string): Promise<void> {
    try {
      const rawInstanceData =
        await this.evoApi.get<EvoGetInstanceStateInputDto>(
          `${this.env.getInstanceState}/${instanceName}`,
        );

      // NOTE: Nesse caso ele só irá fazer o logout se a instância estiver com o status de 'closed'.
      if (rawInstanceData.data.instance.state !== 'closed') {
        return;
      }

      await this.evoApi.delete(`${this.env.logoutInstanceEP}/${instanceName}`);
    } catch (error) {
      console.error(error);

      if (error?.response?.status === 404) {
        throw new NotFoundException('Instância não encontrada');
      }
    }
  }

  async login(instanceName: string): Promise<InstanceQRCode> {
    const rawInstanceData = await this.evoApi.get<EvoGetInstanceStateInputDto>(
      `${this.env.getInstanceState}/${instanceName}`,
    );

    if (rawInstanceData.data.instance.state === 'open') {
      throw new ConflictException('A conexão já está estabelecida!');
    }

    try {
      const reconnectResponse = await this.evoApi.get<InstanceQRCode>(
        `${this.env.connectInstanceEP}/${instanceName}`,
      );

      return reconnectResponse.data;
    } catch (error) {
      console.error(error);

      if (error?.response?.status === 404) {
        throw new NotFoundException('Instância não encontrada');
      }
    }
  }
}
