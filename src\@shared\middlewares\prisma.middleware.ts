import { Injectable, NestMiddleware } from '@nestjs/common';
import { PrismaService } from '../services/prisma/prisma.service';

@Injectable()
export class PrismaMiddleware implements NestMiddleware {
  constructor(private readonly prismaService: PrismaService) {}

  use(req: any, res: any, next: () => void) {
    // Injetar o PrismaService na requisição
    req.prismaService = this.prismaService;
    next();
  }
}
