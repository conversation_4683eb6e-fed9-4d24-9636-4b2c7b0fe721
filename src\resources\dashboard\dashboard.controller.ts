import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';

@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['attendant_dashboard_view', 'app_view'])
  @Get()
  findAll(
    @AccountIdExtractor() accountId: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.dashboardService.findAll(accountId, startDate, endDate);
  }
}
