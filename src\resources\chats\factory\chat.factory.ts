import { Chats as ChatModel } from '@prisma/client';
import { Chats } from '../entities/chat.entity';

export class ChatFactory {
  static convertBatchFromModelToChat(models: ChatModel[]): ChatModel[] {
    return models.map((model) => {
      const messageOutput = new Chats({
        ...model,
      });

      return messageOutput;
    });
  }

  static convertFromModelToChat(model: ChatModel): Chats {
    const messageOutput = new Chats({
      ...model,
    });

    return messageOutput;
  }
}
