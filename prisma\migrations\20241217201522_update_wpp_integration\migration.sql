/*
  Warnings:

  - A unique constraint covering the columns `[token]` on the table `whatsapp_integration` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[number_id]` on the table `whatsapp_integration` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[business_id]` on the table `whatsapp_integration` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX `whatsapp_integration_id_secure_id_idx` ON `whatsapp_integration`;

-- AlterTable
ALTER TABLE `whatsapp_integration` ADD COLUMN `business_id` VARCHAR(255) NULL,
    ADD COLUMN `is_business` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `number_id` VARCHAR(255) NULL,
    ADD COLUMN `token` VARCHAR(255) NULL;

-- CreateIndex
CREATE UNIQUE INDEX `whatsapp_integration_token_key` ON `whatsapp_integration`(`token`);

-- CreateIndex
CREATE UNIQUE INDEX `whatsapp_integration_number_id_key` ON `whatsapp_integration`(`number_id`);

-- CreateIndex
CREATE UNIQUE INDEX `whatsapp_integration_business_id_key` ON `whatsapp_integration`(`business_id`);

-- CreateIndex
CREATE INDEX `whatsapp_integration_id_secure_id_is_business_idx` ON `whatsapp_integration`(`id`, `secure_id`, `is_business`);
