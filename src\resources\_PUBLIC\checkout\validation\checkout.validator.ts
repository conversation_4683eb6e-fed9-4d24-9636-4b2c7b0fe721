import { BadRequestException, NotFoundException } from "@nestjs/common";
import { PrismaClient } from "@prisma/client";

export class CheckoutValidator {
  prisma: PrismaClient;

  static async execute(prisma: PrismaClient, userId: number, accountId: number) {
    const user = await prisma.users.findFirst({
      where: { id: userId },
    })

    const account = await prisma.accounts.findFirst({
      where: { id: accountId },
    })

    if (!user) {
      throw new NotFoundException('Usuário não existe');
    }

    if (!user.isActive) {
      throw new BadRequestException('Usuário não está ativo');
    }

    if (user.isDeleted) {
      throw new BadRequestException('Usuário excluído');
    }

    if (!account) {
      throw new NotFoundException('Conta não existe');
    }

    if (!account.isActive) {
      throw new BadRequestException('Conta não está ativa');
    }

    if (account.isDeleted) {
      throw new BadRequestException('Conta excluído');
    }
  }
}