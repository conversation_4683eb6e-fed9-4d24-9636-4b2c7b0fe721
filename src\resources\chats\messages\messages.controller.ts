import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { MessagesServices } from './messages.service';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';

@Controller('sessions/messages')
export class MessagesController {
  constructor(private readonly messagesService: MessagesServices) {}

  @Get(':secureId')
  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_chat_view'])
  async findaAllChatMessages(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @Param('secureId') secureId: string,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.messagesService.findaAllChatMessages(
      paginationQuery,
      secureId,
      accountId,
    );
  }
}
