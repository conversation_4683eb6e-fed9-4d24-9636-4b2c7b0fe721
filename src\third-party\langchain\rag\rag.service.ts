import { Inject, OnModuleInit } from '@nestjs/common';
import storageEnvs from '../env.config';
import { ConfigType } from '@nestjs/config';
import { QdrantVectorStore } from '@langchain/qdrant';
import { OpenAIEmbeddings } from '@langchain/openai';
import { TokenTextSplitter } from '@langchain/textsplitters';
import { QdrantClient } from '@qdrant/js-client-rest';
import { collectionConfig } from './collection-config';
import { format } from 'date-fns';
import { convert } from 'html-to-text';
import { v4 as uuid } from 'uuid';

export class RAGService {
  private qdrantClient: QdrantClient;
  private openaiEmbeddings: OpenAIEmbeddings;

  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
    // private readonly prisma: PrismaService,
  ) {
    this.qdrantClient = new QdrantClient({
      url: this.storageConfig.qdrantBaseUrl,
      apiKey: this.storageConfig.qdrantApiKey,
      port: this.storageConfig.qdrantBasePort,
    });

    this.openaiEmbeddings = new OpenAIEmbeddings({
      openAIApiKey: this.storageConfig.openApiKey,
      model: 'text-embedding-3-small',
    });
  }

  async ingestContent(
    collectionName: string,
    content: string,
    chunkSize: number,
    chunkOverlap: number,
  ) {
    try {
      const vectorStore = new QdrantVectorStore(this.openaiEmbeddings, {
        client: this.qdrantClient,
        collectionName,
      });

      vectorStore.collectionName = collectionName;

      const splitter = new TokenTextSplitter({
        chunkSize,
        chunkOverlap,
      });

      const contentPlainText = convert(content, {
        wordwrap: false,
      });

      const splittedData = await splitter.splitText(contentPlainText);

      const docId = uuid();
      const documents = splittedData.map((text) => ({
        pageContent: text,
        metadata: {
          docId,
          date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        },
      }));

      const BATCH_SIZE = 500;
      const DELAY_BETWEEN_BATCHES = 60000;

      for (let i = 0; i < documents.length; i += BATCH_SIZE) {
        const batch = documents.slice(i, i + BATCH_SIZE);
        await this.processBatch(batch, vectorStore);

        // If there are more documents to process, wait before next batch
        if (i + BATCH_SIZE < documents.length) {
          await this.delay(DELAY_BETWEEN_BATCHES);
        }
      }

      return true;
    } catch (error) {
      console.log(error);
      throw new Error('Erro ao inserir conteúdo');
    }
  }

  private async delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async processBatch(documents: any[], vectorStore: QdrantVectorStore) {
    for (const doc of documents) {
      await vectorStore.addDocuments([doc]);
    }
  }

  async deleteCollection(collectionName: string) {
    try {
      await this.qdrantClient.deleteCollection(collectionName);
    } catch (error) {
      console.log(error);
      throw new Error('Erro ao deletar coleção');
    }
  }

  async createCollection(collectionName: string) {
    try {
      await this.qdrantClient.createCollection(
        collectionName,
        collectionConfig,
      );
    } catch (error) {
      console.log(error);
      throw new Error('Erro ao criar coleção');
    }
  }
}
