import { Plans as PlanModel } from '@prisma/client';
import { FormatMoney } from 'src/@shared/helpers/money/format-money';
import { Plan } from 'src/resources/_BACKOFFICE/plans/entities/plan.entity';

export class PublicPlansMapper {
  static fromModelToEntity(model: PlanModel): Plan {
    return new Plan({
      ...model,
      price: FormatMoney.fromModelToOutput(model.price),
    });
  }

  static fromBatchModelToBatchEntity(models: PlanModel[]): Plan[] {
    return models.map((model) => this.fromModelToEntity(model));
  }
}
