import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Put,
  Res,
} from '@nestjs/common';
import { ChatbotsService } from './chatbots.service';
import { CreateChatbotDto } from './dto/create-chatbot.dto';
import { UpdateChatbotDto } from './dto/update-chatbot.dto';
import { Permissions } from 'src/@shared/decorators/permission/permission.decorator';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { Response } from 'express';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { CanCreateNewChatBotGuard } from 'src/@shared/guards/plan/chat-bots-validator.guard';
import { ChatBotTestConversationInputDto } from './dto/chat-test-conversation';

@Controller('chatbots')
export class ChatbotsController {
  constructor(private readonly chatbotsService: ChatbotsService) {}

  @UseGuards(JwtGuard, RoleGuard, CanCreateNewChatBotGuard)
  @Post()
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_create', 'attendant_chatbot_create'])
  create(
    @Body() createChatbotDto: CreateChatbotDto,
    @AccountIdExtractor() accountId: number,
  ) {
    return this.chatbotsService.create(createChatbotDto, accountId);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_chatbot_view'])
  @Get()
  async findAll(
    @PaginationQuery() paginationQuery: PaginationQueryType,
    @IsActiveIsDeletedQuery()
    isActiveIsDeletedQuery: IsActiveIsDeletedQueryType,
    @AccountIdExtractor() accountId: number,
  ) {
    return await this.chatbotsService.findAll(
      paginationQuery,
      isActiveIsDeletedQuery,
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_view', 'attendant_chatbot_view'])
  @Get(':secureId')
  findOne(@Param('secureId') secureId: string) {
    return this.chatbotsService.findOne(secureId);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_edit', 'attendant_chatbot_edit'])
  @Put(':secureId')
  update(
    @Param('secureId') secureId: string,
    @Body() updateChatbotDto: UpdateChatbotDto,
  ) {
    return this.chatbotsService.update(secureId, updateChatbotDto);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_delete', 'attendant_chatbot_delete'])
  @Delete(':secureId')
  remove(@Param('secureId') secureId: string) {
    return this.chatbotsService.remove(secureId);
  }

  @UseGuards(JwtGuard, RoleGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Permissions(['app_edit', 'attendant_chatbot_edit'])
  @Post(':secureId')
  async chatBotTest(
    @Param('secureId') chatBotSecureId: string,
    @Body() chatConversationInputDto: ChatBotTestConversationInputDto,
    @Res() res: Response,
  ) {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    const messages = chatConversationInputDto.messages;

    if (messages.length === 1) {
      const isPopulated =
        await this.chatbotsService.isDatabasePopulated(chatBotSecureId);

      if (!isPopulated) {
        res.write(
          `Para garantir que o teste com o chatbot funcione perfeitamente, recomendamos que você alimente sua base de dados. 😊\n\n`,
        );
        res.end();
        return;
      }
    }

    const responseLLM = await this.chatbotsService.chatConversation(
      messages.map((message) => message.content),
      chatBotSecureId,
    );

    for await (const chunk of await responseLLM) {
      res.write(`${chunk.content}`);
      await new Promise((resolve) => setTimeout(resolve, 60));
    }
    res.end();
    return;
  }
}
