import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';
import { OneSignalModule } from 'src/third-party/one-signal/one-signal.module';
import { ResetPasswordTemplate } from './templates/reset-password.template';
import { ErrorNotificationTemplate } from './templates/error-notification.template';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [PrismaModule, OneSignalModule, ConfigModule],
  controllers: [NotificationController],
  providers: [
    NotificationService,
    ResetPasswordTemplate,
    ErrorNotificationTemplate,
  ],
  exports: [
    NotificationService,
    ResetPasswordTemplate,
    ErrorNotificationTemplate,
  ],
})
export class NotificationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('notifications');
  }
}
