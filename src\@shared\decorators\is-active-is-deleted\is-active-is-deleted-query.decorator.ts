import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IIsActiveIsDeletedDecorator } from 'src/@shared/contracts/decorators/is-active-is-deleted/interface';

interface isActiveIsDeletedProps extends Headers {
  isActive?: string;
  isDeleted?: string;
}

export const IsActiveIsDeletedQuery = createParamDecorator(
  (data: any, ctx: ExecutionContext): IIsActiveIsDeletedDecorator => {
    const request = ctx.switchToHttp().getRequest();
    const query: isActiveIsDeletedProps = request.query;

    let isActive: boolean | undefined;
    let isDeleted: boolean | undefined;

    if (!query?.['isActive']) {
      isActive = undefined;
    } else {
      isActive = query['isActive'] === 'true' ? true : false;
    }

    if (!query?.['isDeleted']) {
      isDeleted = undefined;
    } else {
      isDeleted = query['isDeleted'] === 'true' ? true : false;
    }

    return {
      isActive,
      isDeleted,
    };
  },
);
