import { v4 as uuidV4 } from 'uuid';

import {
  PermissionSlug,
  PermissionName,
  PermissionGroup,
} from 'src/@shared/types/permissions';
import { Entity } from 'src/@shared/contracts/entity/interface';

type PermissionConstructorProps = {
  id?: number;
  secureId?: string;

  name: PermissionName;
  slug: PermissionSlug;
  group: PermissionGroup;
  description?: string;

  createdAt?: Date;
  updatedAt?: Date;
};

type PermissionJson = {
  secureId: string;

  name: PermissionName;
  slug: PermissionSlug;
  group: PermissionGroup;
  description?: string;

  createdAt?: Date;
  updatedAt?: Date;
};

export class Permission implements Entity {
  id?: number;

  secureId?: string;

  name: PermissionName;
  slug: PermissionSlug;
  group: PermissionGroup;
  description?: string;

  createdAt?: Date;
  updatedAt?: Date;

  constructor(props: PermissionConstructorProps) {
    this.id = props?.id && props.id;
    this.secureId = props?.secureId ? props.secureId : uuidV4();

    this.name = props.name;
    this.slug = props.slug;
    this.group = props.group;
    this.description = props?.description ? props.description : undefined;

    this.createdAt = props?.createdAt && props.createdAt;
    this.updatedAt = props?.updatedAt && props.updatedAt;
  }

  toJSON(): PermissionJson {
    return {
      secureId: this.secureId,

      name: this.name,
      slug: this.slug,
      group: this.group,
      description: this.description,

      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
