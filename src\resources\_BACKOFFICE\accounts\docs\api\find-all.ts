import { ApiResponseNoStatusOptions } from '@nestjs/swagger';

const okResponse: ApiResponseNoStatusOptions = {
  examples: {
    withData: {
      summary: 'Listagem de todas as Accounts. Contendo uma ou mais accounts',
      value: {
        meta: {
          totalItems: 2,
          totalPages: 1,
          currentPage: 1,
          itemsPerPage: 15,
        },
        data: [
          {
            secureId: '5d0d4d2c-6dea-475d-99cc-d84ec348b91d',
            companyName: 'plyrchat',
            isActive: false,
            isDeleted: false,
            createdAt: '2024-11-21T16:33:50.734Z',
            updatedAt: '2024-11-21T16:33:50.734Z',
          },
          {
            secureId: 'b9d9e297-edc5-4386-8637-v466caa709ad',
            companyName: 'plyrtech',
            isActive: true,
            isDeleted: false,
            createdAt: '2024-11-21T16:43:14.263Z',
            updatedAt: '2024-11-21T16:43:14.263Z',
          },
        ],
      },
    },

    withNoData: {
      summary: 'Listagem de todas as Accounts. Sem nenhuma account',
      value: {
        meta: {
          totalItems: 0,
          totalPages: 0,
          currentPage: 1,
          itemsPerPage: 15,
        },
        data: [],
      },
    },
  },
};

export const findAllAccounts = {
  status: {
    okResponse,
  },
};
