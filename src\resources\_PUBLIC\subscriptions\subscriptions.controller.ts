import { Controller, Get, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { UpdateSubscriptionDto } from './dto/update-subscription.dto';
import { IsPlanActive } from 'src/@shared/helpers/validation/plans/is-plan-active';
import { JwtGuard } from 'src/@shared/guards/jwt/jwt.guard';
import { RoleGuard } from 'src/@shared/guards/role/role.guard';

import { PermissionsGuard } from 'src/@shared/guards/permission/permission.guard';
import { SubscriptionGuard } from 'src/@shared/guards/subscription/subscription.guard';
import { Roles } from 'src/@shared/decorators/role/role.decorator';
import { PaginationQuery } from 'src/@shared/decorators/pagination/pagination-query.decorator';
import { IsActiveIsDeletedQuery } from 'src/@shared/decorators/is-active-is-deleted/is-active-is-deleted-query.decorator';
import { AccountIdExtractor } from 'src/@shared/decorators/token/account-id-extracto.decorator';
import { IsActiveIsDeletedQueryType } from 'src/@shared/types/decorators/is-active-is-deleted';
import { PaginationQueryType } from 'src/@shared/types/decorators/pagination';
import { IWithPagination } from 'src/@shared/contracts/dto/withPagination';
import { EfiSubscriptionsService } from 'src/third-party/efi/subscriptions/efi-subscriptions.service';
import { UserIdExtractorDecorator } from 'src/@shared/decorators/token/user-id-extractor.decorator';
import { ActiveSubscriptionDto } from './dto/find-active-subscription.dto';

@Controller('subscriptions')
export class SubscriptionsController {
  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly efiSubscriptionService: EfiSubscriptionsService,
  ) { }


  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Get()
  async findActiveSubscription(
    @AccountIdExtractor() accountId: number,
  ): Promise<ActiveSubscriptionDto> {
    return await this.subscriptionsService.findActiveSubscription(
      accountId,
    );
  }

  @UseGuards(JwtGuard, RoleGuard, PermissionsGuard, SubscriptionGuard)
  @Roles(['APP', 'ATTENDANT'])
  @Delete()
  async cancelSubscription(
    @AccountIdExtractor() accountId: number,
    @UserIdExtractorDecorator() userId: number
  ) {
    return this.subscriptionsService.cancelSubscription(accountId, userId);
  }
}
