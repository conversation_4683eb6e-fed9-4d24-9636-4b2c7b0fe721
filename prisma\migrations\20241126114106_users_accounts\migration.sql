-- CreateTable
CREATE TABLE `users_accounts` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `secure_id` VARCHAR(36) NOT NULL,
    `user_id` INTEGER NOT NULL,
    `account_id` INTEGER NOT NULL,
    `role_id` INTEGER NOT NULL,
    `is_owner` <PERSON><PERSON><PERSON>EAN NOT NULL DEFAULT false,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_deleted` B<PERSON><PERSON>EAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `users_accounts_id_key`(`id`),
    UNIQUE INDEX `users_accounts_secure_id_key`(`secure_id`),
    INDEX `users_accounts_id_secure_id_idx`(`id`, `secure_id`),
    <PERSON>IMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `users_accounts` ADD CONSTRAINT `users_accounts_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `users_accounts` ADD CONSTRAINT `users_accounts_account_id_fkey` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `users_accounts` ADD CONSTRAINT `users_accounts_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
