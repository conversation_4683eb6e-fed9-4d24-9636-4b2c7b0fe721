import { Module } from '@nestjs/common';

import { LoginModule } from './auth/login/login.module';
import { AccountsModule } from './_BACKOFFICE/accounts/accounts.module';
import { RegisterModule } from './auth/register/register.module';
import { ChatsModule } from './chats/chats.module';
import { EvoIntegrationModule } from './evo/integration/integration.module';
import { WebhookModule } from './evo/webhook/webhook.module';
import { SubscriptionsModule } from './_PUBLIC/subscriptions/subscriptions.module';
import { WebhookEfiNotificationModule } from './_PUBLIC/webhook-efi-notification/webhook-efi-notification.module';
import { CheckoutModule } from './_PUBLIC/checkout/checkout.module';
import { AttendantModule } from './users/attendant/attendant.module';
import { ProfileModule } from './profile/profile.module';
import { BackofficeModule } from './_BACKOFFICE/backoffice.module';
import { PublicPlansModule } from './_PUBLIC/plans/plans.module';
import { PublicModule } from './_PUBLIC/public.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { AIModule } from './ia/ai.module';
import { NotificationModule } from './notification/notification.module';
import { WhatsappIntegrationModule } from './whatsapp-integration/whatsapp-integration.module';
import { ContactModule } from './contact/contact.module';
import { ForgotModule } from './auth/forgot/forgot.module';

@Module({
  imports: [
    LoginModule,
    RegisterModule,
    AccountsModule,
    ChatsModule,
    WebhookModule,
    EvoIntegrationModule,
    SubscriptionsModule,
    WebhookEfiNotificationModule,
    CheckoutModule,
    AttendantModule,
    ProfileModule,
    WhatsappIntegrationModule,
    PublicPlansModule,
    AIModule,
    NotificationModule,

    BackofficeModule,

    PublicModule,

    DashboardModule,

    ContactModule,

    ForgotModule,
  ],
})
export class ResourcesModule {}
