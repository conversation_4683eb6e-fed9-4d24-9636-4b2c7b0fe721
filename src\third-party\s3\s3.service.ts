import {
  Inject,
  Injectable,
  OnM<PERSON>uleD<PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import storageEnvs from './env.config';
import {
  ObjectCannedACL,
  PutObjectCommand,
  PutObjectCommandInput,
  PutObjectCommandOutput,
  S3Client,
} from '@aws-sdk/client-s3';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';
import { Uploads } from 'src/resources/uploads/entities/upload.entity';
import { UploadMapper } from 'src/resources/uploads/mapper/upload.mapper';
import { v4 as uuid } from 'uuid';
import { getFileTypeWpp } from 'src/utils/getFileTypeWpp';
import { getFileTypeUpload } from 'src/utils/getFileTypeUpload';

@Injectable()
export class S3Service implements OnModuleInit {
  constructor(
    @Inject(storageEnvs.KEY)
    private readonly storageConfig: ConfigType<typeof storageEnvs>,
    private readonly prisma: PrismaService,
  ) {}
  private s3: S3Client;

  onModuleInit() {
    this.s3 = new S3Client({
      region: this.storageConfig.region,
      credentials: {
        accessKeyId: this.storageConfig.accessKey,
        secretAccessKey: this.storageConfig.secretKey,
      },
      endpoint: this.storageConfig.endpoint,
    });
  }

  async uploadFile(file: Express.Multer.File): Promise<string> {
    const sanitizedFileName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '');
    const fileName = `${Date.now()}-${sanitizedFileName}`;
    const filePath = `${this.storageConfig.uploadDir}/${fileName}`;

    const params: PutObjectCommandInput = {
      ACL: this.storageConfig.acl as ObjectCannedACL,
      Bucket: this.storageConfig.bucketName,
      Key: filePath,
      Body: file.buffer,
      ContentType: file.mimetype,
    };

    const contentType = getFileTypeUpload(file.mimetype);

    const command = new PutObjectCommand(params);

    try {
      const s3Response = await this.s3.send(command);

      if (s3Response.$metadata.httpStatusCode !== 200) {
        console.error('S3 response metadata:', s3Response.$metadata);
        throw new Error('Error uploading file to S3');
      }

      const url = `${this.storageConfig.cdnUrl}/${filePath}`;

      const newUpload = new Uploads({
        fileName: fileName,
        urlCdn: url,
        fileType: file.mimetype,
        isPrivate: params.ACL === 'private' ? true : false,
        type: contentType,
        bucket: this.storageConfig.bucketName,
      });

      const uploadModel = UploadMapper.toModel(newUpload);
      const upload = await this.prisma.uploads.create({
        data: uploadModel,
      });

      return upload.secureId;
    } catch (error) {
      console.error('Full S3 error:', error);
      console.error('S3 params:', params);
      return null;
    }
  }

  async uploadBase64(
    base64Content: string,
    contentType: string,
    type: string,
    fileName?: string,
  ): Promise<string> {
    // Remove base64 prefix if present (e.g., "data:image/jpeg;base64,")
    const base64Data = base64Content.replace(
      /^data:([A-Za-z-+/]+);base64,/,
      '',
    );

    // Convert base64 to Buffer
    const buffer = Buffer.from(base64Data, 'base64');

    // Set up parameters for S3 upload
    const params: PutObjectCommandInput = {
      Bucket: this.storageConfig.bucketName,
      Key: `file-messages/${fileName}`,
      Body: buffer,
      ContentType: contentType,
      ContentEncoding: 'base64',
      ACL: 'public-read', // Set this based on your security requirements
    };

    const command = new PutObjectCommand(params);

    try {
      // Upload to S3
      const s3Response = await this.s3.send(command);

      if (s3Response.$metadata.httpStatusCode !== 200) {
        console.error('S3 response metadata:', s3Response.$metadata);
        throw new Error('Error uploading file to S3');
      }

      const url = `${this.storageConfig.cdnUrl}/file-messages/${fileName}`;

      const newUpload = new Uploads({
        fileName: fileName,
        urlCdn: url,
        fileType: contentType,
        isPrivate: params.ACL === 'private' ? true : false,
        type,
        bucket: this.storageConfig.bucketName,
      });

      const uploadModel = UploadMapper.toModel(newUpload);
      const upload = await this.prisma.uploads.create({
        data: uploadModel,
      });

      return upload.secureId;
    } catch (error) {
      console.error('Error uploading to S3:', error);
      throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
  }

  async uploadFileBase64(base64Content: string, type: string): Promise<string> {
    let contentType = 'application/octet-stream';
    let fileExtension = 'bin';

    const dataUrlRegex = /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/;
    const match = base64Content.match(dataUrlRegex);

    if (match && match[1]) {
      contentType = match[1];
      fileExtension = this.getFileExtensionFromMimeType(contentType);
    }

    const fileName = `${uuid()}.${fileExtension}`;

    return await this.uploadBase64(base64Content, contentType, type, fileName);
  }

  private getFileExtensionFromMimeType(mimeType: string): string {
    const mimeToExt = {
      'image/jpeg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',
      'image/svg+xml': 'svg',
      'application/pdf': 'pdf',
      'audio/mpeg': 'mp3',
      'audio/wav': 'wav',
      'audio/x-m4a': 'm4a',
      'audio/ogg': 'ogg',
      'video/mp4': 'mp4',
      'video/webm': 'webm',
      'text/plain': 'txt',
      'application/json': 'json',
      'application/xml': 'xml',
      'application/zip': 'zip',
      'application/x-tar': 'tar',
      'application/vnd.ms-excel': 'xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        'xlsx',
      'application/msword': 'doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        'docx',
    };

    return mimeToExt[mimeType] || 'bin'; // Return 'bin' as default extension if MIME type is not recognized
  }
}
