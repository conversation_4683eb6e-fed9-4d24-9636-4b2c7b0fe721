### Get all chatbots with analytics (paginated) - Standard pagination
GET {{baseUrl}}/backoffice/chatbots?page=1&limit=20&search=&isActive=true&isDeleted=false&isAI=true&sortBy=createdAt&sortOrder=desc
Authorization: Bearer {{token}}

### Get all chatbots with search filter - Standard pagination
GET {{baseUrl}}/backoffice/chatbots?search=customer&page=1&limit=10&isActive=true&isDeleted=false
Authorization: Bearer {{token}}

### Get all chatbots filtered by account owner - Standard pagination
GET {{baseUrl}}/backoffice/chatbots?accountOwnerName=Company&page=1&limit=10&isActive=true&isDeleted=false
Authorization: Bearer {{token}}

### Get detailed chatbot information
GET {{baseUrl}}/backoffice/chatbots/{{chatbotSecureId}}
Authorization: Bearer {{token}}

### Get chatbot sent messages (for prompt evaluation) - Standard pagination
GET {{baseUrl}}/backoffice/chatbots/{{chatbotSecureId}}/messages?messageDirection=sent&page=1&limit=50
Authorization: Bearer {{token}}

### Get chatbot received messages (for conversation context) - Standard pagination
GET {{baseUrl}}/backoffice/chatbots/{{chatbotSecureId}}/messages?messageDirection=received&page=1&limit=50
Authorization: Bearer {{token}}

### Get chatbot messages with date filter - Standard pagination
GET {{baseUrl}}/backoffice/chatbots/{{chatbotSecureId}}/messages?startDate=2024-01-01&endDate=2024-12-31&page=1&limit=100
Authorization: Bearer {{token}}

### Variables
@baseUrl = http://localhost:3000
@token = your_jwt_token_here
@chatbotSecureId = chatbot_secure_id_here
