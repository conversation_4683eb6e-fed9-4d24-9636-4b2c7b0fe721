import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';
import { IStorageEnvs } from './langchain.contract';

export default registerAs('storage-environment', () => {
  const values: IStorageEnvs = {
    openApiKey: process.env.OPENAI_API_KEY,
    qdrantApiKey: process.env.QDRANT_API_KEY,
    qdrantBaseUrl: process.env.QDRANT_BASE_URL,
    qdrantBasePort: parseInt(process.env.QDRANT_BASE_PORT, 10),
  };

  // Joi validations
  const schema = Joi.object<IStorageEnvs>({
    openApiKey: Joi.string().required().messages({
      'any.required': `OPENAI_API_KEY is missing ENV`,
    }),
    qdrantApiKey: Joi.string().required().messages({
      'any.required': `QDRANT_API_KEY is missing ENV`,
    }),
    qdrantBaseUrl: Joi.string().required().messages({
      'any.required': `QDRANT_BASE_URL is missing ENV`,
    }),
    qdrantBasePort: Joi.number().required().messages({
      'any.required': `QDRANT_BASE_PORT is missing ENV`,
    }),
  });

  // Validates our values using the schema.
  // Passing a flag to tell Joi to not stop validation on the
  // first error, we want all the errors found.
  const { error } = schema.validate(values, { abortEarly: false });

  // If the validation is invalid, "error" is assigned a
  // ValidationError object providing more information.
  if (error) {
    throw new Error(
      `Validation failed - Is there an environment variable missing?
				${error.message}`,
    );
  }

  // If the validation is valid, then the "error" will be
  // undefined and this will return successfully.
  return values;
});
