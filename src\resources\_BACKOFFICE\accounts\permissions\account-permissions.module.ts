import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { PrismaModule } from 'src/@shared/services/prisma/prisma.module';
import { AccountPermissionsController } from './account-permissions.controller';
import { AccountPermissionsService } from './account-permissions.service';
import { PrismaMiddleware } from 'src/@shared/middlewares/prisma.middleware';

@Module({
  imports: [PrismaModule],
  controllers: [AccountPermissionsController],
  providers: [AccountPermissionsService],
})
export class AccountPermissionsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PrismaMiddleware).forRoutes('account-permissions');
  }
}
