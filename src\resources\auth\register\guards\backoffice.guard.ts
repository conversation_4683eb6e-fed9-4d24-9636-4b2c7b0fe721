import {
  CanActivate,
  ConflictException,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/@shared/services/prisma/prisma.service';

/**
 * @description Esse guard é responsável por verificar se o email e cpf já estão cadastrados.
 *
 */
@Injectable()
export class BackofficeRegisterGuard implements CanActivate {
  constructor(private readonly prismaService: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const email = request.body.email;
    const cpf = request.body.cpf;

    const user = await this.prismaService.users.findFirst({
      where: {
        email: { equals: email },
      },
    });

    if (user) {
      throw new ConflictException('Email já cadastrado');
    }

    const userCPF = await this.prismaService.users.findFirst({
      where: {
        cpf: { equals: cpf.replace(/\D/g, '') },
      },
    });

    if (userCPF) {
      throw new ConflictException('CPF já cadastrado');
    }

    return true;
  }
}
