import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty } from 'class-validator';

export class LoginInputDto {
  @ApiProperty({
    description: 'Email do usuário',
    required: true,
    uniqueItems: true,
    maxLength: 255,
    minLength: 3,
    default: '',
    type: String,
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'O email informado é inválido' })
  @IsNotEmpty({ message: 'O email é obrigatório' })
  email: string;

  @ApiProperty({
    description: 'Senha do usuário',
    required: true,
    uniqueItems: false,
    maxLength: 255,
    minLength: 6,
    default: '',
    type: String,
    example: 'Doyles Tavern $5000',
  })
  @IsNotEmpty({ message: 'A senha é obrigatória' })
  password: string;
}
